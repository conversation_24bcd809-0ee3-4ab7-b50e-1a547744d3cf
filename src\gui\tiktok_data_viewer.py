"""
TikTok数据查看器 - 显示和导出临时数据
"""

import os
import csv
from datetime import datetime
from typing import List

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QMessageBox, QFileDialog, QHeaderView,
    QAbstractItemView, QMenu, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QContextMenuEvent

from loguru import logger
from src.models.tiktok_product import TikTokProduct, tiktok_storage


class TikTokDataTable(QTableWidget):
    """TikTok数据表格"""

    def __init__(self):
        super().__init__()
        self.setup_table()
        self.setup_context_menu()
    
    def setup_table(self):
        """设置表格"""
        # 设置列 - 移除商品标题，添加详细运费信息和商店产品数量
        headers = [
            "商品ID", "图片链接", "币种", "售价", "原价", "折扣金额",
            "评分", "评价数量", "销量", "上架时间",
            "运费价格", "运费数值", "运费币种",
            "店铺名称", "店铺产品数量", "商品链接"
        ]
        
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSortingEnabled(True)
        
        # 设置列宽 - 更新为新的字段结构
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 商品ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 图片链接
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 币种
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 售价
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 原价
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 折扣金额
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 评分
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 评价数量
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # 销量
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # 上架时间
        header.setSectionResizeMode(10, QHeaderView.ResizeToContents)  # 运费价格
        header.setSectionResizeMode(11, QHeaderView.ResizeToContents)  # 运费数值
        header.setSectionResizeMode(12, QHeaderView.ResizeToContents)  # 运费币种
        header.setSectionResizeMode(13, QHeaderView.ResizeToContents)  # 店铺名称
        header.setSectionResizeMode(14, QHeaderView.ResizeToContents)  # 店铺产品数量
        header.setSectionResizeMode(15, QHeaderView.ResizeToContents)  # 商品链接
    
    def setup_context_menu(self):
        """设置右键菜单"""
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.itemAt(position) is None:
            return
        
        menu = QMenu(self)
        
        # 复制单元格
        copy_action = QAction("复制", self)
        copy_action.triggered.connect(self.copy_selected_cells)
        menu.addAction(copy_action)
        
        # 复制行
        copy_row_action = QAction("复制整行", self)
        copy_row_action.triggered.connect(self.copy_selected_rows)
        menu.addAction(copy_row_action)
        
        menu.exec_(self.mapToGlobal(position))
    
    def copy_selected_cells(self):
        """复制选中的单元格"""
        selection = self.selectionModel()
        if selection.hasSelection():
            selected_text = []
            for index in selection.selectedIndexes():
                item = self.item(index.row(), index.column())
                if item:
                    selected_text.append(item.text())
            
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText('\n'.join(selected_text))
    
    def copy_selected_rows(self):
        """复制选中的行"""
        selection = self.selectionModel()
        if selection.hasSelection():
            selected_rows = set()
            for index in selection.selectedIndexes():
                selected_rows.add(index.row())
            
            rows_data = []
            for row in sorted(selected_rows):
                row_data = []
                for col in range(self.columnCount()):
                    item = self.item(row, col)
                    row_data.append(item.text() if item else "")
                rows_data.append('\t'.join(row_data))
            
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText('\n'.join(rows_data))
    
    def load_data(self, products: List[TikTokProduct]):
        """加载数据 - 使用新的字段结构"""
        self.setRowCount(len(products))

        for row, product in enumerate(products):
            # 商品ID
            self.setItem(row, 0, QTableWidgetItem(product.product_id))

            # 图片链接 (替代商品标题)
            image_item = QTableWidgetItem(product.image_urls)
            image_item.setToolTip(product.image_urls)  # 设置工具提示
            self.setItem(row, 1, image_item)

            # 币种
            self.setItem(row, 2, QTableWidgetItem(product.currency))

            # 售价
            self.setItem(row, 3, QTableWidgetItem(f"{product.sale_price:.2f}"))

            # 原价
            self.setItem(row, 4, QTableWidgetItem(f"{product.original_price:.2f}"))

            # 折扣金额
            self.setItem(row, 5, QTableWidgetItem(f"{product.discount_amount:.2f}"))

            # 评分
            self.setItem(row, 6, QTableWidgetItem(f"{product.rating:.1f}"))

            # 评价数量
            self.setItem(row, 7, QTableWidgetItem(str(product.review_count)))

            # 销量
            self.setItem(row, 8, QTableWidgetItem(str(product.sold_count)))

            # 上架时间
            self.setItem(row, 9, QTableWidgetItem(product.listing_time))

            # 运费价格
            shipping_price_item = QTableWidgetItem(product.shipping_fee_price_str)
            shipping_price_item.setToolTip(product.shipping_fee_price_str)
            self.setItem(row, 10, shipping_price_item)

            # 运费数值
            shipping_val_item = QTableWidgetItem(product.shipping_fee_price_val)
            shipping_val_item.setToolTip(product.shipping_fee_price_val)
            self.setItem(row, 11, shipping_val_item)

            # 运费币种
            shipping_currency_item = QTableWidgetItem(product.shipping_fee_currency)
            shipping_currency_item.setToolTip(product.shipping_fee_currency)
            self.setItem(row, 12, shipping_currency_item)

            # 店铺名称
            shop_item = QTableWidgetItem(product.shop_name)
            shop_item.setToolTip(product.shop_name)
            self.setItem(row, 13, shop_item)

            # 店铺产品数量
            shop_count_item = QTableWidgetItem(str(product.shop_product_count))
            shop_count_item.setToolTip(f"商店共有 {product.shop_product_count} 个产品")
            self.setItem(row, 14, shop_count_item)

            # 商品链接
            url_item = QTableWidgetItem(product.canonical_url)
            url_item.setToolTip(product.canonical_url)
            self.setItem(row, 15, url_item)




class TikTokDataViewer(QWidget):
    """TikTok数据查看器"""
    
    data_exported = pyqtSignal()  # 数据导出信号
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        # 延迟刷新数据，避免在初始化时出现问题
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(100, self.refresh_data)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        
        # 统计信息
        self.stats_label = QLabel("数据总数: 0")
        self.stats_label.setFont(QFont("Arial", 10, QFont.Bold))
        toolbar_layout.addWidget(self.stats_label)
        
        toolbar_layout.addStretch()
        
        # 刷新按钮
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_button)
        
        # 清空按钮
        self.clear_button = QPushButton("🗑️ 清空")
        self.clear_button.clicked.connect(self.clear_data)
        toolbar_layout.addWidget(self.clear_button)
        
        # 导出Excel按钮
        self.export_excel_button = QPushButton("📊 导出Excel")
        self.export_excel_button.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_excel_button)
        
        # 导出CSV按钮
        self.export_csv_button = QPushButton("📄 导出CSV")
        self.export_csv_button.clicked.connect(self.export_to_csv)
        toolbar_layout.addWidget(self.export_csv_button)
        
        layout.addLayout(toolbar_layout)
        
        # 数据表格
        self.table = TikTokDataTable()
        layout.addWidget(self.table)
        
        # 底部状态栏
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
    
    def refresh_data(self):
        """刷新数据 - 从临时存储加载数据"""
        try:
            logger.info(f"🔄 [数据查看器] 开始刷新数据")

            # 从临时存储获取数据
            products = tiktok_storage.get_products()
            logger.info(f"📦 [数据查看器] 从临时存储加载了 {len(products)} 条数据")

            # 加载到表格
            self.table.load_data(products)

            # 更新统计信息
            count = len(products)
            self.stats_label.setText(f"数据总数: {count}")

            # 更新按钮状态
            has_data = count > 0
            self.clear_button.setEnabled(has_data)
            self.export_excel_button.setEnabled(has_data)
            self.export_csv_button.setEnabled(has_data)

            self.status_label.setText(f"已加载 {count} 条数据")
            logger.info(f"✅ [数据查看器] 刷新数据完成，共 {count} 条")

        except Exception as e:
            logger.error(f"💥 [数据查看器] 刷新数据失败: {str(e)}")
            # 避免在初始化时显示错误对话框
            try:
                from PyQt5.QtWidgets import QApplication
                if QApplication.instance() is not None:
                    QMessageBox.critical(self, "错误", f"刷新数据失败: {str(e)}")
            except Exception:
                pass  # 忽略对话框显示错误

    def clear_data(self):
        """清空数据"""
        try:
            reply = QMessageBox.question(
                self,
                "确认清空",
                "确定要清空所有数据吗？此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                tiktok_storage.clear()
                self.refresh_data()
                self.status_label.setText("数据已清空")
                logger.info(f"🗑️ [数据查看器] 用户清空了所有数据")

        except Exception as e:
            logger.error(f"💥 [数据查看器] 清空数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"清空数据失败: {str(e)}")
    
    def export_to_excel(self):
        """导出到Excel"""
        try:
            products = tiktok_storage.get_products()
            if not products:
                QMessageBox.information(self, "提示", "没有数据可导出")
                return
            
            # 选择保存路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"tiktok_products_{timestamp}.xlsx"
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出Excel文件", default_filename,
                "Excel文件 (*.xlsx);;所有文件 (*)"
            )
            
            if filename:
                self._export_to_excel(products, filename)
                
        except Exception as e:
            logger.error(f"💥 [数据查看器] 导出Excel失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出Excel失败: {str(e)}")
    
    def export_to_csv(self):
        """导出到CSV"""
        try:
            products = tiktok_storage.get_products()
            if not products:
                QMessageBox.information(self, "提示", "没有数据可导出")
                return
            
            # 选择保存路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"tiktok_products_{timestamp}.csv"
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出CSV文件", default_filename,
                "CSV文件 (*.csv);;所有文件 (*)"
            )
            
            if filename:
                self._export_to_csv(products, filename)
                
        except Exception as e:
            logger.error(f"💥 [数据查看器] 导出CSV失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出CSV失败: {str(e)}")
    
    def _export_to_excel(self, products: List[TikTokProduct], filename: str):
        """执行Excel导出"""
        try:
            import pandas as pd
            
            # 转换数据
            data = [product.to_export_dict() for product in products]
            df = pd.DataFrame(data)
            
            # 导出到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            
            # 标记为已保存
            tiktok_storage.mark_as_saved()
            self.data_exported.emit()
            
            QMessageBox.information(self, "成功", f"已成功导出 {len(products)} 条数据到:\n{filename}")
            logger.info(f"📊 [数据查看器] 成功导出Excel: {filename}")
            
        except ImportError:
            QMessageBox.critical(self, "错误", "缺少pandas或openpyxl库，无法导出Excel文件")
        except Exception as e:
            raise e
    
    def _export_to_csv(self, products: List[TikTokProduct], filename: str):
        """执行CSV导出"""
        try:
            # 转换数据
            data = [product.to_export_dict() for product in products]
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                if data:
                    fieldnames = data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
            
            # 标记为已保存
            tiktok_storage.mark_as_saved()
            self.data_exported.emit()
            
            QMessageBox.information(self, "成功", f"已成功导出 {len(products)} 条数据到:\n{filename}")
            logger.info(f"📄 [数据查看器] 成功导出CSV: {filename}")
            
        except Exception as e:
            raise e
