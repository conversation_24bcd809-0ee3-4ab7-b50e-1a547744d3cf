"""
商品数据模型
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum
import json


class ProductStatus(Enum):
    """商品状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    OUT_OF_STOCK = "out_of_stock"
    DISCONTINUED = "discontinued"


class Currency(Enum):
    """货币枚举"""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    CAD = "CAD"
    AUD = "AUD"
    SGD = "SGD"
    MYR = "MYR"
    THB = "THB"
    VND = "VND"
    IDR = "IDR"
    PHP = "PHP"


@dataclass
class ProductImage:
    """商品图片信息"""
    url: str
    alt_text: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    is_main: bool = False
    order: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "url": self.url,
            "alt_text": self.alt_text,
            "width": self.width,
            "height": self.height,
            "is_main": self.is_main,
            "order": self.order
        }


@dataclass
class ProductVariant:
    """商品变体信息"""
    sku: str
    name: str
    price: Decimal
    original_price: Optional[Decimal] = None
    stock: Optional[int] = None
    attributes: Dict[str, str] = field(default_factory=dict)  # 如: {"颜色": "红色", "尺寸": "L"}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "sku": self.sku,
            "name": self.name,
            "price": float(self.price),
            "original_price": float(self.original_price) if self.original_price else None,
            "stock": self.stock,
            "attributes": self.attributes
        }


@dataclass
class ProductRating:
    """商品评分信息"""
    average_rating: float = 0.0
    total_reviews: int = 0
    rating_distribution: Dict[int, int] = field(default_factory=dict)  # {5: 100, 4: 50, ...}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "average_rating": self.average_rating,
            "total_reviews": self.total_reviews,
            "rating_distribution": self.rating_distribution
        }


@dataclass
class Product:
    """商品数据模型"""
    
    # 基本信息
    product_id: str
    title: str
    description: str
    category: str
    subcategory: Optional[str] = None
    brand: Optional[str] = None
    
    # 价格信息
    price: Decimal = Decimal('0')
    original_price: Optional[Decimal] = None
    currency: Currency = Currency.USD
    discount_percentage: Optional[float] = None
    
    # 库存和销量
    stock: Optional[int] = None
    sold_count: int = 0
    monthly_sales: Optional[int] = None
    
    # 图片信息
    images: List[ProductImage] = field(default_factory=list)
    main_image_url: Optional[str] = None
    
    # 变体信息
    variants: List[ProductVariant] = field(default_factory=list)
    
    # 评分和评论
    rating: ProductRating = field(default_factory=ProductRating)
    
    # 店铺信息
    shop_id: str = ""
    shop_name: str = ""
    shop_url: Optional[str] = None
    
    # 链接信息
    product_url: str = ""
    affiliate_url: Optional[str] = None
    
    # 状态和标签
    status: ProductStatus = ProductStatus.ACTIVE
    tags: List[str] = field(default_factory=list)
    
    # 属性信息
    attributes: Dict[str, Any] = field(default_factory=dict)
    specifications: Dict[str, str] = field(default_factory=dict)
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    scraped_at: datetime = field(default_factory=datetime.now)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.main_image_url and self.images:
            # 设置主图
            main_images = [img for img in self.images if img.is_main]
            if main_images:
                self.main_image_url = main_images[0].url
            else:
                self.main_image_url = self.images[0].url
        
        # 计算折扣百分比
        if self.original_price and self.price and self.original_price > self.price:
            self.discount_percentage = float(
                (self.original_price - self.price) / self.original_price * 100
            )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "product_id": self.product_id,
            "title": self.title,
            "description": self.description,
            "category": self.category,
            "subcategory": self.subcategory,
            "brand": self.brand,
            "price": float(self.price),
            "original_price": float(self.original_price) if self.original_price else None,
            "currency": self.currency.value,
            "discount_percentage": self.discount_percentage,
            "stock": self.stock,
            "sold_count": self.sold_count,
            "monthly_sales": self.monthly_sales,
            "images": [img.to_dict() for img in self.images],
            "main_image_url": self.main_image_url,
            "variants": [variant.to_dict() for variant in self.variants],
            "rating": self.rating.to_dict(),
            "shop_id": self.shop_id,
            "shop_name": self.shop_name,
            "shop_url": self.shop_url,
            "product_url": self.product_url,
            "affiliate_url": self.affiliate_url,
            "status": self.status.value,
            "tags": self.tags,
            "attributes": self.attributes,
            "specifications": self.specifications,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "scraped_at": self.scraped_at.isoformat(),
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Product':
        """从字典创建Product实例"""
        # 处理图片列表
        images = []
        if "images" in data and data["images"]:
            for img_data in data["images"]:
                images.append(ProductImage(**img_data))
        
        # 处理变体列表
        variants = []
        if "variants" in data and data["variants"]:
            for variant_data in data["variants"]:
                variant_data["price"] = Decimal(str(variant_data["price"]))
                if variant_data.get("original_price"):
                    variant_data["original_price"] = Decimal(str(variant_data["original_price"]))
                variants.append(ProductVariant(**variant_data))
        
        # 处理评分信息
        rating = ProductRating()
        if "rating" in data and data["rating"]:
            rating = ProductRating(**data["rating"])
        
        # 处理时间字段
        time_fields = ["created_at", "updated_at", "scraped_at"]
        for field_name in time_fields:
            if field_name in data and isinstance(data[field_name], str):
                data[field_name] = datetime.fromisoformat(data[field_name])
        
        # 处理价格字段
        if "price" in data:
            data["price"] = Decimal(str(data["price"]))
        if "original_price" in data and data["original_price"]:
            data["original_price"] = Decimal(str(data["original_price"]))
        
        # 处理枚举字段
        if "currency" in data:
            data["currency"] = Currency(data["currency"])
        if "status" in data:
            data["status"] = ProductStatus(data["status"])
        
        # 设置处理后的数据
        data["images"] = images
        data["variants"] = variants
        data["rating"] = rating
        
        return cls(**data)
    
    def get_main_image(self) -> Optional[ProductImage]:
        """获取主图"""
        main_images = [img for img in self.images if img.is_main]
        return main_images[0] if main_images else (self.images[0] if self.images else None)
    
    def get_price_display(self) -> str:
        """获取价格显示字符串"""
        if self.original_price and self.original_price > self.price:
            return f"{self.currency.value} {self.price} (原价: {self.original_price})"
        return f"{self.currency.value} {self.price}"
    
    def is_on_sale(self) -> bool:
        """是否在促销"""
        return (
            self.original_price is not None 
            and self.original_price > self.price 
            and self.discount_percentage is not None 
            and self.discount_percentage > 0
        )
