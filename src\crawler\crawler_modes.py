"""
爬虫模式定义和混合爬虫类
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Callable, Union
import asyncio
import time
from loguru import logger

from .base_crawler import BaseCrawler
from .selenium_crawler import SeleniumCrawler, CaptchaDetectionResult, HAS_SELENIUM
from ..models.scraping_task import ScrapingTask


class CrawlerMode(Enum):
    """爬虫模式枚举"""
    DIRECT = "direct"           # 直连模式（默认）
    BROWSER = "browser"         # 浏览器模式
    AUTO = "auto"              # 自动切换模式


class ModeSwitch:
    """模式切换记录"""
    
    def __init__(self, from_mode: CrawlerMode, to_mode: CrawlerMode, 
                 reason: str, timestamp: float = None):
        self.from_mode = from_mode
        self.to_mode = to_mode
        self.reason = reason
        self.timestamp = timestamp or time.time()


class HybridCrawler:
    """混合模式爬虫 - 支持直连和浏览器模式切换"""
    
    def __init__(self, task: Optional[ScrapingTask] = None, 
                 initial_mode: CrawlerMode = CrawlerMode.DIRECT,
                 user_interaction_callback: Optional[Callable] = None):
        """
        初始化混合爬虫
        
        Args:
            task: 爬取任务
            initial_mode: 初始模式
            user_interaction_callback: 用户交互回调函数
        """
        self.task = task
        self.current_mode = initial_mode
        self.user_interaction_callback = user_interaction_callback
        
        # 初始化爬虫实例
        self.direct_crawler = BaseCrawler(task)
        self.selenium_crawler = None
        
        # 模式切换历史
        self.mode_switches: List[ModeSwitch] = []
        
        # 验证码检测配置
        self.captcha_detection_enabled = True
        self.auto_switch_on_captcha = True
        
        # 性能统计
        self.stats = {
            'direct_requests': 0,
            'browser_requests': 0,
            'captcha_detections': 0,
            'mode_switches': 0,
            'total_requests': 0
        }
    
    async def initialize(self) -> bool:
        """初始化爬虫"""
        try:
            logger.info(f"🚀 [混合爬虫] 初始化，初始模式: {self.current_mode.value}")
            
            # 初始化直连爬虫
            await self.direct_crawler.start_session()
            
            # 如果初始模式是浏览器模式，初始化Selenium爬虫
            if self.current_mode == CrawlerMode.BROWSER:
                if not await self._initialize_selenium():
                    logger.warning("⚠️ [混合爬虫] Selenium初始化失败，切换到直连模式")
                    self.current_mode = CrawlerMode.DIRECT
            
            logger.info("✅ [混合爬虫] 初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 初始化失败: {str(e)}")
            return False
    
    async def _initialize_selenium(self) -> bool:
        """初始化Selenium爬虫"""
        try:
            if not HAS_SELENIUM:
                logger.error("❌ [混合爬虫] Selenium依赖未安装")
                return False
            
            if self.selenium_crawler is None:
                self.selenium_crawler = SeleniumCrawler(
                    headless=False,  # 显示浏览器窗口以便用户交互
                    user_interaction_callback=self.user_interaction_callback
                )
            
            return await self.selenium_crawler.initialize()
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] Selenium初始化失败: {str(e)}")
            return False
    
    async def close(self):
        """关闭爬虫"""
        try:
            if self.direct_crawler:
                await self.direct_crawler.close_session()
            
            if self.selenium_crawler:
                await self.selenium_crawler.close()
            
            logger.info("🔒 [混合爬虫] 已关闭")
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 关闭失败: {str(e)}")
    
    async def switch_mode(self, new_mode: CrawlerMode, reason: str = "") -> bool:
        """切换爬虫模式"""
        try:
            if new_mode == self.current_mode:
                logger.debug(f"🔄 [混合爬虫] 已经是 {new_mode.value} 模式，无需切换")
                return True
            
            old_mode = self.current_mode
            logger.info(f"🔄 [混合爬虫] 切换模式: {old_mode.value} -> {new_mode.value}")
            logger.info(f"   切换原因: {reason}")
            
            # 记录模式切换
            switch = ModeSwitch(old_mode, new_mode, reason)
            self.mode_switches.append(switch)
            self.stats['mode_switches'] += 1
            
            # 如果切换到浏览器模式，需要初始化Selenium
            if new_mode == CrawlerMode.BROWSER:
                if not await self._initialize_selenium():
                    logger.error("❌ [混合爬虫] 无法切换到浏览器模式")
                    return False
                
                # 同步Cookie（从直连模式到浏览器模式）
                await self._sync_cookies_to_browser()
            
            # 如果从浏览器模式切换出来，同步Cookie
            elif old_mode == CrawlerMode.BROWSER and self.selenium_crawler:
                await self._sync_cookies_from_browser()
            
            self.current_mode = new_mode
            
            # 通知用户模式切换
            if self.user_interaction_callback:
                await self.user_interaction_callback({
                    'type': 'mode_switched',
                    'from_mode': old_mode.value,
                    'to_mode': new_mode.value,
                    'reason': reason
                })
            
            logger.info(f"✅ [混合爬虫] 模式切换完成: {new_mode.value}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 模式切换失败: {str(e)}")
            return False
    
    async def _sync_cookies_to_browser(self):
        """将直连模式的Cookie同步到浏览器"""
        try:
            if not self.selenium_crawler or not self.selenium_crawler.driver:
                return
            
            # 这里可以实现Cookie同步逻辑
            # 由于直连模式使用的是aiohttp，Cookie管理较复杂
            # 暂时跳过，让浏览器自己管理Cookie
            logger.debug("🍪 [混合爬虫] Cookie同步到浏览器（暂时跳过）")
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] Cookie同步到浏览器失败: {str(e)}")
    
    async def _sync_cookies_from_browser(self):
        """将浏览器的Cookie同步到直连模式"""
        try:
            if not self.selenium_crawler:
                return
            
            cookies = self.selenium_crawler.get_cookies()
            if cookies:
                # 这里可以实现将Cookie设置到aiohttp session的逻辑
                logger.debug(f"🍪 [混合爬虫] 从浏览器同步了 {len(cookies)} 个Cookie")
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 从浏览器同步Cookie失败: {str(e)}")
    
    def _detect_captcha_in_content(self, content: str, url: str) -> bool:
        """在页面内容中检测验证码"""
        try:
            if not content:
                return False
            
            # 检测验证码关键词
            captcha_keywords = [
                'captcha', 'challenge',
                'puzzle', '验证码', '滑块'
            ]
            
            content_lower = content.lower()
            for keyword in captcha_keywords:
                if keyword in content_lower:
                    logger.warning(f"🚫 [混合爬虫] 在页面内容中检测到验证码关键词: {keyword}")
                    return True
            
            # 检测特定的验证码HTML结构
            captcha_patterns = [
                'class="captcha',  # 通用验证码
                'id="captcha',  # 通用验证码
                'secsdk-captcha',
                'data-testid="captcha'
            ]
            
            for pattern in captcha_patterns:
                if pattern in content_lower:
                    logger.warning(f"🚫 [混合爬虫] 在页面内容中检测到验证码模式: {pattern}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 验证码检测失败: {str(e)}")
            return False
    
    async def get_text(self, url: str, **kwargs) -> Optional[str]:
        """获取页面文本内容（支持模式切换）"""
        try:
            self.stats['total_requests'] += 1
            
            if self.current_mode == CrawlerMode.DIRECT:
                return await self._get_text_direct(url, **kwargs)
            elif self.current_mode == CrawlerMode.BROWSER:
                return await self._get_text_browser(url, **kwargs)
            elif self.current_mode == CrawlerMode.AUTO:
                return await self._get_text_auto(url, **kwargs)
            else:
                logger.error(f"❌ [混合爬虫] 未知模式: {self.current_mode}")
                return None
                
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 获取页面内容失败: {str(e)}")
            return None
    
    async def _get_text_direct(self, url: str, **kwargs) -> Optional[str]:
        """使用直连模式获取页面内容"""
        try:
            self.stats['direct_requests'] += 1
            logger.debug(f"🌐 [混合爬虫] 使用直连模式获取: {url}")
            
            content = await self.direct_crawler.get_text(url, **kwargs)
            
            # 检测验证码
            if content and self.captcha_detection_enabled:
                if self._detect_captcha_in_content(content, url):
                    self.stats['captcha_detections'] += 1
                    
                    if self.auto_switch_on_captcha:
                        logger.warning("🚫 [混合爬虫] 检测到验证码，尝试切换到浏览器模式")
                        
                        if await self.switch_mode(CrawlerMode.BROWSER, "检测到验证码"):
                            # 重新使用浏览器模式获取
                            return await self._get_text_browser(url, **kwargs)
                        else:
                            logger.error("❌ [混合爬虫] 无法切换到浏览器模式")
                    else:
                        logger.warning("⚠️ [混合爬虫] 检测到验证码但未启用自动切换")
            
            return content
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 直连模式获取失败: {str(e)}")
            return None
    
    async def _get_text_browser(self, url: str, **kwargs) -> Optional[str]:
        """使用浏览器模式获取页面内容"""
        try:
            self.stats['browser_requests'] += 1
            logger.debug(f"🌐 [混合爬虫] 使用浏览器模式获取: {url}")

            if not self.selenium_crawler:
                if not await self._initialize_selenium():
                    logger.error("❌ [混合爬虫] Selenium未初始化")
                    return None

            # 检查是否应该启用load_more功能
            load_more = kwargs.get('enable_load_more', False) or self._should_enable_load_more(url)
            if load_more:
                logger.info(f"🔄 [混合爬虫] 启用加载更多和零销量检测: {url}")

            content = await self.selenium_crawler.get_page_content(
                url,
                wait_for_load=True,
                load_more=load_more,
                filter_conditions=kwargs.get('filter_conditions')
            )
            return content
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 浏览器模式获取失败: {str(e)}")
            return None

    def _should_enable_load_more(self, url: str) -> bool:
        """判断是否应该启用加载更多功能"""
        try:
            # 检查是否是TikTok Shop的店铺页面
            shop_patterns = [
                '/shop/store/',  # 店铺页面
                '/shop/category/',  # 分类页面
                '/shop/search',  # 搜索页面
            ]

            for pattern in shop_patterns:
                if pattern in url.lower():
                    logger.debug(f"🔍 [混合爬虫] 检测到shop页面模式: {pattern}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"🔍 [混合爬虫] 检测load_more模式时出错: {str(e)}")
            return False
    
    async def _get_text_auto(self, url: str, **kwargs) -> Optional[str]:
        """自动模式：先尝试直连，遇到验证码自动切换到浏览器"""
        try:
            logger.debug(f"🤖 [混合爬虫] 使用自动模式获取: {url}")
            
            # 先尝试直连模式
            content = await self._get_text_direct(url, **kwargs)
            
            # 如果直连模式成功且没有验证码，直接返回
            if content and not self._detect_captcha_in_content(content, url):
                return content
            
            # 如果检测到验证码或直连失败，切换到浏览器模式
            logger.info("🔄 [混合爬虫] 自动切换到浏览器模式")
            if await self.switch_mode(CrawlerMode.BROWSER, "自动模式检测到问题"):
                return await self._get_text_browser(url, **kwargs)
            
            return content  # 返回直连模式的结果（可能包含验证码）
            
        except Exception as e:
            logger.error(f"❌ [混合爬虫] 自动模式获取失败: {str(e)}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'current_mode': self.current_mode.value,
            'stats': self.stats.copy(),
            'mode_switches': len(self.mode_switches),
            'last_switch': self.mode_switches[-1].__dict__ if self.mode_switches else None
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    def get_current_mode(self) -> CrawlerMode:
        """获取当前爬虫模式"""
        return self.current_mode
