"""
爬虫管理器
"""

import asyncio
from typing import List, Optional, Dict, Any, Callable
from loguru import logger

from .tiktok_crawler import TikTokShopCrawler
from .network_crawler_manager import NetworkCrawlerManager
from .url_parser import URLParser
from ..models.product import Product
from ..models.shop import Shop
from ..models.scraping_task import ScrapingTask, TaskType, TaskStatus
from ..utils.memory_storage import MemoryStorage



class CrawlerManager:
    """爬虫管理器"""

    def __init__(self):
        self.url_parser = URLParser()
        self.memory_storage = MemoryStorage()
        self.active_tasks: Dict[str, ScrapingTask] = {}
        self.crawlers: Dict[str, TikTokShopCrawler] = {}
        self.network_crawler: Optional[NetworkCrawlerManager] = None
        self.enable_network_interception = False

    def enable_network_interception_mode(self, enable: bool = True):
        """启用或禁用网络拦截模式"""
        try:
            self.enable_network_interception = enable

            if enable:
                if not self.network_crawler:
                    logger.info("🕸️ [爬虫管理器] 初始化网络拦截爬虫...")
                    self.network_crawler = NetworkCrawlerManager(self.memory_storage)

                    # 设置网络拦截环境
                    success = self.network_crawler.setup_crawler(enable_interception=True)
                    if success:
                        logger.info("✅ [爬虫管理器] 网络拦截模式已启用")
                    else:
                        logger.error("❌ [爬虫管理器] 网络拦截模式启用失败")
                        self.enable_network_interception = False
                        self.network_crawler = None
                else:
                    logger.info("✅ [爬虫管理器] 网络拦截模式已启用（复用现有实例）")
            else:
                if self.network_crawler:
                    logger.info("🔒 [爬虫管理器] 关闭网络拦截爬虫...")
                    self.network_crawler.close()
                    self.network_crawler = None
                logger.info("ℹ️ [爬虫管理器] 网络拦截模式已禁用")

        except Exception as e:
            logger.error(f"❌ [爬虫管理器] 配置网络拦截模式失败: {str(e)}")
            self.enable_network_interception = False
            self.network_crawler = None

    async def create_task_from_url(self, url: str, **kwargs) -> Optional[ScrapingTask]:
        """从URL创建爬取任务"""
        try:
            # 解析URL
            parsed_info = self.url_parser.parse_url(url)
            
            if not parsed_info["is_valid"]:
                logger.error(f"无效的URL: {url} - {parsed_info.get('error')}")
                return None
            
            # 支持三种URL类型：PDP、Desktop Shop、Mobile Share
            url_type = parsed_info["url_type"]
            product_id = parsed_info.get('product_id', 'unknown')

            if url_type == "shop_pdp":
                # PDP商品任务
                product_slug = parsed_info.get('product_slug', 'unknown')

                logger.info(f"📋 [任务创建] 创建PDP商品任务")
                logger.info(f"   - 产品ID: {product_id}")
                logger.info(f"   - 产品Slug: {product_slug}")

                task = ScrapingTask(
                    name=f"PDP商品_{product_id[:8]}",
                    description=f"爬取PDP商品: {product_slug}",
                    task_type=TaskType.SINGLE_PRODUCT,
                    target_url=url,
                    **kwargs
                )

            elif url_type == "desktop_shop":
                # 桌面商店商品任务
                logger.info(f"📋 [任务创建] 创建桌面商店商品任务")
                logger.info(f"   - 产品ID: {product_id}")

                task = ScrapingTask(
                    name=f"桌面商店_{product_id[:8]}",
                    description=f"爬取桌面商店商品: {product_id}",
                    task_type=TaskType.SINGLE_PRODUCT,
                    target_url=url,
                    **kwargs
                )

            elif url_type == "mobile_share":
                # 移动分享商品任务
                share_code = parsed_info.get('share_code', 'unknown')
                clean_url = parsed_info.get('clean_url')

                logger.info(f"📋 [任务创建] 创建移动分享商品任务")
                logger.info(f"   - 分享代码: {share_code}")
                logger.info(f"   - 产品ID: {product_id}")
                if clean_url:
                    logger.info(f"   - 干净URL: {clean_url}")

                task = ScrapingTask(
                    name=f"移动分享_{product_id[:8] if product_id != 'unknown' else share_code[:8]}",
                    description=f"爬取移动分享商品: {share_code}",
                    task_type=TaskType.SINGLE_PRODUCT,
                    target_url=url,
                    **kwargs
                )

            else:
                logger.error(f"❌ [任务创建] 不支持的URL类型: {url_type}")
                logger.error(f"   支持的类型: shop_pdp, desktop_shop, mobile_share")
                return None
            
            # 保存任务
            await self.memory_storage.save_task_async(task)
            self.active_tasks[task.task_id] = task
            
            logger.info(f"创建任务成功: {task.name} ({task.task_id})")
            return task
            
        except Exception as e:
            logger.error(f"创建任务失败: {url} - {str(e)}")
            return None
    
    async def start_task(self, task_id: str) -> bool:
        """启动任务"""
        try:
            task = self.active_tasks.get(task_id)
            if not task:
                # 尝试从存储中加载任务
                task = await self.memory_storage.load_task_async(task_id)
                if not task:
                    logger.error(f"任务不存在: {task_id}")
                    return False
                self.active_tasks[task_id] = task
            
            if task.status != TaskStatus.PENDING:
                logger.warning(f"任务状态不正确: {task.status}")
                return False
            
            # 创建爬虫实例（支持模式选择）
            crawler_mode_str = getattr(task, 'crawler_mode', 'direct')

            # 导入爬虫模式枚举
            from .crawler_modes import CrawlerMode

            # 转换模式字符串为枚举
            mode_map = {
                'direct': CrawlerMode.DIRECT,
                'browser': CrawlerMode.BROWSER,
                'auto': CrawlerMode.AUTO
            }
            crawler_mode = mode_map.get(crawler_mode_str, CrawlerMode.DIRECT)

            # 创建用户交互回调
            from ..utils.user_interaction import get_interaction_manager
            interaction_manager = get_interaction_manager(use_gui=True)

            crawler = TikTokShopCrawler(
                task=task,
                crawler_mode=crawler_mode,
                user_interaction_callback=interaction_manager.handle_interaction
            )
            self.crawlers[task_id] = crawler

            logger.info(f"🔧 [任务启动] 爬虫模式: {crawler_mode.value}")
            
            # 启动任务
            task.start()
            
            # 支持多种任务类型
            if task.task_type == TaskType.SINGLE_PRODUCT:
                logger.info(f"🚀 [任务启动] 启动单商品爬取任务")
                asyncio.create_task(self._execute_single_product_task(task, crawler))
            elif task.task_type == TaskType.SHOP_PRODUCTS:
                logger.info(f"🚀 [任务启动] 启动店铺商品爬取任务")
                logger.info(f"   - 网络拦截模式: {'✅ 启用' if self.enable_network_interception else '❌ 禁用'}")
                asyncio.create_task(self._execute_shop_task_with_network_interception(task, crawler))
            else:
                logger.error(f"❌ [任务启动] 不支持的任务类型: {task.task_type}")
                return False
            
            logger.info(f"任务启动成功: {task.name}")
            return True
            
        except Exception as e:
            logger.error(f"启动任务失败: {task_id} - {str(e)}")
            return False
    
    async def _execute_single_product_task(self, task: ScrapingTask, crawler: TikTokShopCrawler):
        """执行单个商品爬取任务"""
        try:
            logger.info(f"🚀 [任务执行] 开始执行单个商品爬取任务: {task.task_id}")
            logger.info(f"📋 [任务执行] 任务信息:")
            logger.info(f"   - 任务名称: {task.name}")
            logger.info(f"   - 目标URL: {task.target_url}")
            logger.info(f"   - 任务类型: {task.task_type}")

            async with crawler:
                # 解析URL获取商品信息
                logger.info(f"🔍 [任务执行] 步骤1: 解析URL获取商品信息")
                parsed_info = self.url_parser.parse_url(task.target_url)
                url_type = parsed_info.get("url_type")
                product_id = parsed_info.get("product_id")

                logger.info(f"   - URL类型: {url_type}")
                logger.info(f"   - 商品ID: {product_id}")
                logger.info(f"   - 解析结果: {parsed_info}")

                if not product_id:
                    logger.error(f"❌ [任务执行] 无法从URL中提取商品ID")
                    task.fail("无法从URL中提取商品ID")
                    return

                # 更新进度
                logger.info(f"📊 [任务执行] 步骤2: 初始化任务进度")
                task.update_progress(total=1)

                # 支持三种URL类型的爬取方法
                logger.info(f"🎯 [任务执行] 步骤3: 根据URL类型选择爬取方法")

                # 获取用于爬取的最终URL
                crawling_url = self.url_parser.get_crawling_url(task.target_url)
                logger.info(f"🔗 [任务执行] 爬取URL: {crawling_url}")

                if url_type in ["shop_pdp", "desktop_shop", "mobile_share"]:
                    logger.info(f"🔗 [任务执行] 开始{url_type}商品爬取")

                    # 获取过滤条件和任务配置
                    filter_conditions = getattr(task, 'filter_conditions', None)
                    task_config = {
                        'include_images': getattr(task, 'include_images', True),
                        'filter_conditions': filter_conditions
                    }

                    # 对于移动分享链接，使用干净的PDP URL进行爬取
                    if url_type == "mobile_share":
                        clean_url = parsed_info.get('clean_url')
                        if clean_url:
                            logger.info(f"🎯 [任务执行] 使用干净PDP URL爬取: {clean_url}")
                            product = await crawler.get_pdp_product_detail(clean_url, filter_conditions, task_config)
                        else:
                            logger.info(f"🎯 [任务执行] 使用重定向URL爬取: {crawling_url}")
                            product = await crawler.get_pdp_product_detail(crawling_url, filter_conditions, task_config)
                    else:
                        # PDP和桌面商店URL直接使用PDP方法爬取
                        product = await crawler.get_pdp_product_detail(crawling_url, filter_conditions, task_config)
                else:
                    logger.error(f"❌ [任务执行] 不支持的URL类型: {url_type}")
                    task.fail(f"不支持的URL类型: {url_type}")
                    return

                logger.info(f"📦 [任务执行] 步骤4: 处理爬取结果")

                if product:
                    logger.info(f"✅ [任务执行] 商品爬取成功，开始保存数据")
                    logger.info(f"   - 商品ID: {product.product_id}")
                    logger.info(f"   - 商品标题: {product.title}")
                    logger.info(f"   - 商品价格: {product.price} {product.currency}")

                    # 保存商品数据到临时存储
                    logger.info(f"💾 [任务执行] 保存商品数据到临时存储")
                    self._save_product_to_temp_storage(product)

                    task.add_result(product.product_id)
                    task.update_progress(completed=1)

                    logger.info(f"🎉 [任务执行] 商品爬取和保存完成: {product.title}")
                else:
                    logger.error(f"❌ [任务执行] 获取商品详情失败，请重试")
                    task.update_progress(failed=1)
                    task.fail("获取商品详情失败,请重试")
                    return

                # 任务完成
                logger.info(f"✅ [任务执行] 步骤5: 标记任务完成")
                task.complete()
                logger.info(f"🎉 [任务执行] 单个商品爬取任务完全完成: {task.task_id}")

        except Exception as e:
            logger.error(f"💥 [任务执行] 执行单个商品任务失败: {str(e)}")
            logger.error(f"   - 任务ID: {task.task_id}")
            logger.error(f"   - 目标URL: {task.target_url}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            task.fail(f"任务执行异常: {str(e)}")
        finally:
            # 清理资源
            logger.info(f"🧹 [任务执行] 清理任务资源: {task.task_id}")
            if task.task_id in self.crawlers:
                del self.crawlers[task.task_id]
                logger.info(f"✅ [任务执行] 爬虫资源已清理")

    async def _execute_shop_task_with_network_interception(self, task: ScrapingTask, crawler: TikTokShopCrawler):
        """执行店铺爬取任务（集成网络拦截功能）"""
        try:
            logger.info(f"🚀 [网络拦截爬取] 开始执行店铺爬取任务: {task.task_id}")
            logger.info(f"📋 [网络拦截爬取] 任务信息:")
            logger.info(f"   - 任务名称: {task.name}")
            logger.info(f"   - 目标URL: {task.target_url}")
            logger.info(f"   - 网络拦截模式: {'✅ 启用' if self.enable_network_interception else '❌ 禁用'}")

            if self.enable_network_interception and self.network_crawler:
                # 使用网络拦截模式
                logger.info("🕸️ [网络拦截爬取] 使用网络拦截模式爬取店铺")

                # 获取过滤条件
                filter_conditions = getattr(task, 'filter_conditions', None)
                max_load_more = getattr(task, 'max_load_more', 10)

                # 执行网络拦截爬取
                result = self.network_crawler.crawl_shop_with_load_more(
                    shop_url=task.target_url,
                    max_load_more=max_load_more,
                    wait_time=3
                )

                if result['success']:
                    logger.info(f"✅ [网络拦截爬取] 网络拦截爬取成功:")
                    logger.info(f"   - 点击加载更多次数: {result['load_more_clicks']}")
                    logger.info(f"   - 捕获API响应数: {result['api_responses']}")
                    logger.info(f"   - 提取商品数: {result['products_extracted']}")
                    logger.info(f"   - 保存文件: {result['saved_file']}")

                    # 更新任务进度
                    task.update_progress(total=result['products_extracted'], completed=result['products_extracted'])
                    task.complete()

                    logger.info(f"🎉 [网络拦截爬取] 店铺爬取任务完成: {task.task_id}")
                else:
                    logger.error(f"❌ [网络拦截爬取] 网络拦截爬取失败: {result.get('error', 'Unknown error')}")
                    task.fail(f"网络拦截爬取失败: {result.get('error', 'Unknown error')}")
            else:
                # 使用传统模式
                logger.info("🌐 [网络拦截爬取] 使用传统Selenium模式爬取店铺")

                async with crawler:
                    # 获取过滤条件
                    filter_conditions = getattr(task, 'filter_conditions', None)

                    # 使用传统方法获取店铺页面内容
                    content = await crawler.get_shop_page_with_zero_sales_detection(
                        task.target_url,
                        filter_conditions=filter_conditions
                    )

                    if content:
                        logger.info(f"✅ [网络拦截爬取] 传统模式爬取成功，内容长度: {len(content)} 字符")
                        task.complete()
                    else:
                        logger.error(f"❌ [网络拦截爬取] 传统模式爬取失败")
                        task.fail("传统模式爬取失败")

        except Exception as e:
            logger.error(f"💥 [网络拦截爬取] 执行店铺爬取任务失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            task.fail(f"店铺爬取任务异常: {str(e)}")

    def _save_product_to_temp_storage(self, product):
        """将Product对象转换为TikTokProduct对象并保存到临时存储"""
        try:
            from ..models.tiktok_product import TikTokProduct, tiktok_storage

            logger.info(f"🔄 [数据转换] 开始转换Product对象为TikTokProduct对象")

            # 提取图片URL
            image_urls = []
            if product.images:
                image_urls = [img.url for img in product.images]
            image_urls_str = ', '.join(image_urls) if image_urls else ''

            # 提取运费信息
            shipping_fee = product.metadata.get('shipping_fee', {}) if product.metadata else {}
            shipping_fee_price_str = shipping_fee.get('price_str', '$0.00')
            shipping_fee_price_val = shipping_fee.get('price_val', '0.00')
            shipping_fee_currency = shipping_fee.get('currency', 'USD')

            # 提取店铺商品数量
            shop_product_count = product.metadata.get('shop_product_count', 0) if product.metadata else 0

            # 计算折扣金额
            discount_amount = 0
            if product.original_price and product.original_price > product.price:
                discount_amount = float(product.original_price - product.price)

            # 创建TikTokProduct对象
            tiktok_product = TikTokProduct(
                product_id=product.product_id,
                image_urls=image_urls_str,
                currency=product.currency.value,
                sale_price=float(product.price),
                original_price=float(product.original_price) if product.original_price else float(product.price),
                discount_amount=discount_amount,
                rating=product.rating.average_rating if product.rating else 0.0,
                review_count=product.rating.total_reviews if product.rating else 0,
                sold_count=product.sold_count,
                listing_time=product.scraped_at.strftime("%Y-%m-%d %H:%M:%S"),
                shipping_fee_price_str=shipping_fee_price_str,
                shipping_fee_price_val=shipping_fee_price_val,
                shipping_fee_currency=shipping_fee_currency,
                shop_name=product.shop_name,
                shop_product_count=shop_product_count,
                canonical_url=product.product_url,
                source_url=product.product_url
            )

            # 保存到临时存储
            tiktok_storage.add_product(tiktok_product)
            logger.info(f"✅ [数据转换] 成功转换并保存Product对象到临时存储")
            logger.info(f"   - 商品ID: {tiktok_product.product_id}")
            logger.info(f"   - 店铺名称: {tiktok_product.shop_name}")
            logger.info(f"   - 售价: {tiktok_product.sale_price} {tiktok_product.currency}")

        except Exception as e:
            logger.error(f"❌ [数据转换] 转换Product对象失败: {str(e)}")
            import traceback
            logger.error(f"   - 异常堆栈: {traceback.format_exc()}")

    # 移除店铺商品爬取方法，只专注于PDP单商品爬取

    async def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        try:
            task = self.active_tasks.get(task_id)
            if task and task.status == TaskStatus.RUNNING:
                task.pause()
                logger.info(f"任务已暂停: {task.name}")
                return True
            return False
        except Exception as e:
            logger.error(f"暂停任务失败: {task_id} - {str(e)}")
            return False
    
    async def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        try:
            task = self.active_tasks.get(task_id)
            if task and task.status == TaskStatus.PAUSED:
                task.resume()
                logger.info(f"任务已恢复: {task.name}")
                return True
            return False
        except Exception as e:
            logger.error(f"恢复任务失败: {task_id} - {str(e)}")
            return False
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            task = self.active_tasks.get(task_id)
            if task:
                task.cancel()
                
                # 清理爬虫实例
                if task_id in self.crawlers:
                    crawler = self.crawlers[task_id]
                    await crawler.close_session()
                    del self.crawlers[task_id]
                
                logger.info(f"任务已取消: {task.name}")
                return True
            return False
        except Exception as e:
            logger.error(f"取消任务失败: {task_id} - {str(e)}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[ScrapingTask]:
        """获取任务状态（同步版本）"""
        try:
            task = self.active_tasks.get(task_id)
            if task:
                logger.debug(f"📊 [任务管理] 获取任务状态: {task_id} - {task.status}")
                return task
            else:
                logger.warning(f"⚠️ [任务管理] 任务不存在: {task_id}")
                return None

        except Exception as e:
            logger.error(f"💥 [任务管理] 获取任务状态失败: {task_id} - {str(e)}")
            return None

    async def get_task_status_async(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态（异步版本）"""
        try:
            task = self.active_tasks.get(task_id)
            if not task:
                task = await self.memory_storage.load_task_async(task_id)

            if task:
                return task.to_dict()
            return None
        except Exception as e:
            logger.error(f"获取任务状态失败: {task_id} - {str(e)}")
            return None
    
    async def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任务"""
        try:
            tasks = []
            
            # 添加活跃任务
            for task in self.active_tasks.values():
                tasks.append(task.to_dict())
            
            # 从存储中加载其他任务
            stored_tasks = await self.memory_storage.list_tasks_async()
            for task_data in stored_tasks:
                if task_data["task_id"] not in self.active_tasks:
                    tasks.append(task_data)
            
            return tasks
        except Exception as e:
            logger.error(f"列出任务失败: {str(e)}")
            return []
    
    async def cleanup_completed_tasks(self):
        """清理已完成的任务"""
        try:
            completed_task_ids = []
            for task_id, task in self.active_tasks.items():
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed_task_ids.append(task_id)
            
            for task_id in completed_task_ids:
                del self.active_tasks[task_id]
                if task_id in self.crawlers:
                    del self.crawlers[task_id]
            
            logger.info(f"清理了 {len(completed_task_ids)} 个已完成的任务")
        except Exception as e:
            logger.error(f"清理任务失败: {str(e)}")
    
    async def shutdown(self):
        """关闭管理器"""
        try:
            # 取消所有活跃任务
            for task_id in list(self.active_tasks.keys()):
                await self.cancel_task(task_id)
            
            # 关闭数据存储
            await self.data_storage.close()
            
            logger.info("爬虫管理器已关闭")
        except Exception as e:
            logger.error(f"关闭管理器失败: {str(e)}")
