"""
TikTok Shop 专用爬虫
"""

import re
import json
import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple, Callable
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup
from loguru import logger
from decimal import Decimal

from .base_crawler import BaseCrawler
from .crawler_modes import HybridCrawler, CrawlerMode
from ..models.product import Product, ProductImage, ProductVariant, ProductRating, Currency, ProductStatus
from ..models.shop import Shop, ShopRating, ShopStats, ShopType, ShopStatus
from ..models.scraping_task import ScrapingTask
from ..config.settings import TikTokShopConstants, config
from ..utils.concurrent_manager import ConcurrentRequestManager, RequestResult
from ..utils.network_interceptor import NetworkInterceptor


class TikTokShopCrawler(BaseCrawler):
    """TikTok Shop 爬虫 - 支持直连和浏览器模式"""

    def __init__(self, task: Optional[ScrapingTask] = None,
                 crawler_mode: CrawlerMode = CrawlerMode.DIRECT,
                 user_interaction_callback: Optional[Callable] = None):
        """
        初始化TikTok Shop爬虫

        Args:
            task: 爬取任务
            crawler_mode: 爬虫模式（direct/browser/auto）
            user_interaction_callback: 用户交互回调函数
        """
        super().__init__(task)
        self.current_task_config = None  # 存储当前任务配置

        # 模式相关配置
        self.crawler_mode = crawler_mode
        self.user_interaction_callback = user_interaction_callback

        # 混合爬虫实例（用于模式切换）
        self.hybrid_crawler = None

        # 是否启用混合模式
        self.use_hybrid_mode = crawler_mode != CrawlerMode.DIRECT

        # 网络拦截器（永远启用，用于捕获API响应）
        self.network_interceptor: Optional[NetworkInterceptor] = None
        self._initialize_network_interceptor()

        logger.info(f"🚀 [TikTok爬虫] 初始化，模式: {crawler_mode.value}")
        if self.use_hybrid_mode:
            logger.info("🔄 [TikTok爬虫] 启用混合模式，支持自动切换")

    def _initialize_network_interceptor(self, output_dir: Optional[str] = None):
        """初始化网络拦截器（永远启用）"""
        try:
            from pathlib import Path
            output_path = Path(output_dir) if output_dir else Path("intercepted_data")

            logger.info("🕸️ [TikTok爬虫] 初始化网络拦截器（永远启用）...")
            self.network_interceptor = NetworkInterceptor(output_path)

            # 添加TikTok Shop特定的拦截模式
            self.network_interceptor.add_target_pattern("product_list")
            self.network_interceptor.add_target_pattern("brandy_desktop/store")
            self.network_interceptor.add_target_pattern("api/shop")
            self.network_interceptor.add_target_pattern("load_more")
            self.network_interceptor.add_target_pattern("view_more")

            logger.info("✅ [TikTok爬虫] 网络拦截器初始化完成")

        except Exception as e:
            logger.error(f"❌ [TikTok爬虫] 初始化网络拦截器失败: {str(e)}")
            self.network_interceptor = None

    async def start_session(self):
        """启动会话（重写以支持混合模式）"""
        if self.use_hybrid_mode:
            # 使用混合爬虫
            self.hybrid_crawler = HybridCrawler(
                task=self.task,
                initial_mode=self.crawler_mode,
                user_interaction_callback=self.user_interaction_callback
            )
            await self.hybrid_crawler.initialize()
            logger.info("✅ [TikTok爬虫] 混合模式会话已启动")
        else:
            # 使用传统直连模式
            await super().start_session()
            logger.info("✅ [TikTok爬虫] 直连模式会话已启动")

    async def close_session(self):
        """关闭会话（重写以支持混合模式）"""
        if self.hybrid_crawler:
            await self.hybrid_crawler.close()
            self.hybrid_crawler = None
            logger.info("🔒 [TikTok爬虫] 混合模式会话已关闭")
        else:
            await super().close_session()
            logger.info("🔒 [TikTok爬虫] 直连模式会话已关闭")

    async def get_text(self, url: str, **kwargs) -> Optional[str]:
        """获取页面文本内容（重写以支持混合模式和零销量检测）"""
        if self.hybrid_crawler:
            # 检查是否应该启用零销量检测
            if self._should_enable_zero_sales_detection(url):
                logger.info(f"🔍 [TikTok爬虫] 启用零销量检测: {url}")
                kwargs['enable_zero_sales_detection'] = True

            return await self.hybrid_crawler.get_text(url, **kwargs)
        else:
            return await super().get_text(url, **kwargs)

    async def switch_crawler_mode(self, new_mode: CrawlerMode, reason: str = "") -> bool:
        """切换爬虫模式"""
        if not self.hybrid_crawler:
            logger.warning("⚠️ [TikTok爬虫] 未启用混合模式，无法切换")
            return False

        return await self.hybrid_crawler.switch_mode(new_mode, reason)

    def _should_enable_zero_sales_detection(self, url: str) -> bool:
        """判断是否应该启用零销量检测"""
        try:
            # 检查是否是TikTok Shop的商品列表页面
            detection_patterns = [
                '/shop/store/',  # 店铺页面
                '/shop/category/',  # 分类页面
                '/shop/search',  # 搜索页面
            ]

            for pattern in detection_patterns:
                if pattern in url.lower():
                    logger.debug(f"🔍 [TikTok爬虫] 检测到需要零销量检测的页面: {pattern}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"🔍 [TikTok爬虫] 检测零销量模式时出错: {str(e)}")
            return False

    async def get_shop_page_with_zero_sales_detection(self, shop_url: str, filter_conditions: Dict[str, Any] = None, **kwargs) -> Optional[str]:
        """
        获取shop页面内容，启用零销量检测和智能停止机制
        如果启用了网络拦截模式，将同时捕获API响应数据

        Args:
            shop_url: shop页面URL
            filter_conditions: 过滤条件
            **kwargs: 其他参数

        Returns:
            页面内容，如果检测到零销量则可能提前停止加载
        """
        try:
            logger.info(f"🛒 [Shop爬取] 开始获取shop页面，启用零销量检测: {shop_url}")
            logger.info(f"🕸️ [Shop爬取] 网络拦截模式: ✅ 永远启用")

            # 设置网络拦截器（如果可用）
            if self.network_interceptor:
                await self._setup_network_interception_for_browser()

            # 强制启用浏览器模式和零销量检测
            if self.hybrid_crawler:
                # 切换到浏览器模式以支持零销量检测
                from .crawler_modes import CrawlerMode
                await self.hybrid_crawler.switch_mode(CrawlerMode.BROWSER, "启用零销量检测")

                # 获取页面内容，启用加载更多和零销量检测
                content = await self.hybrid_crawler.get_text(
                    shop_url,
                    enable_load_more=True,
                    enable_zero_sales_detection=True,
                    filter_conditions=filter_conditions,
                    **kwargs
                )

                # 处理拦截的数据（如果网络拦截器可用）
                if self.network_interceptor:
                    await self._process_intercepted_network_data(shop_url)

                if content:
                    logger.info(f"✅ [Shop爬取] 成功获取shop页面内容，长度: {len(content)}")
                    return content
                else:
                    logger.warning(f"⚠️ [Shop爬取] 未获取到shop页面内容")
                    return None
            else:
                # 回退到普通模式
                logger.warning(f"⚠️ [Shop爬取] 混合爬虫未启用，使用普通模式")
                return await self.get_text(shop_url, **kwargs)

        except Exception as e:
            logger.error(f"❌ [Shop爬取] 获取shop页面失败: {str(e)}")
            return None

    async def _setup_network_interception_for_browser(self):
        """为浏览器模式设置网络拦截"""
        try:
            if not self.network_interceptor or not self.hybrid_crawler:
                return

            logger.info("🕸️ [网络拦截] 为浏览器模式设置网络拦截...")

            # 获取Selenium爬虫实例
            selenium_crawler = self.hybrid_crawler.selenium_crawler
            if selenium_crawler and hasattr(selenium_crawler, 'driver') and selenium_crawler.driver:
                # 设置网络拦截器使用现有的WebDriver
                logger.info("🔗 [网络拦截] 连接到Selenium浏览器实例...")

                # 将WebDriver实例传递给网络拦截器
                if hasattr(self.network_interceptor, 'set_driver'):
                    self.network_interceptor.set_driver(selenium_crawler.driver)
                    logger.info("✅ [网络拦截] 网络拦截器已连接到浏览器")
                else:
                    logger.info("✅ [网络拦截] 网络拦截器已准备就绪")
            else:
                logger.warning("⚠️ [网络拦截] 无法获取Selenium浏览器实例")

        except Exception as e:
            logger.error(f"❌ [网络拦截] 设置网络拦截失败: {str(e)}")

    async def _process_intercepted_network_data(self, shop_url: str):
        """处理拦截的网络数据"""
        try:
            if not self.network_interceptor:
                return

            logger.info("🔄 [网络拦截] 处理拦截的网络数据...")

            # 停止监控并提取数据
            network_data = self.network_interceptor.stop_monitoring_and_extract()

            if network_data['responses']:
                logger.info(f"📊 [网络拦截] 捕获到 {len(network_data['responses'])} 个响应")

                # 保存拦截数据到JSON文件
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tiktok_shop_api_responses_{timestamp}.json"

                saved_file = self.network_interceptor.save_intercepted_data(network_data, filename)

                if saved_file:
                    logger.info(f"💾 [网络拦截] API响应数据已保存: {saved_file}")

                    # 显示拦截的响应详情
                    for i, response in enumerate(network_data['responses']):
                        url = response.get('url', 'Unknown')
                        status = response.get('status', 'Unknown')
                        has_body = bool(response.get('body'))
                        body_length = response.get('body_length', 0)

                        logger.info(f"   响应 {i+1}: {url[:80]}...")
                        logger.info(f"     状态: {status}, 响应体: {'✅' if has_body else '❌'} ({body_length} 字符)")
                else:
                    logger.error("❌ [网络拦截] 保存API响应数据失败")
            else:
                logger.info("ℹ️ [网络拦截] 未捕获到目标API响应")

        except Exception as e:
            logger.error(f"❌ [网络拦截] 处理拦截数据失败: {str(e)}")

    def save_intercepted_data_to_json(self, filename: Optional[str] = None) -> Optional[str]:
        """手动保存拦截的数据到JSON文件"""
        try:
            if not self.network_interceptor:
                logger.warning("⚠️ [网络拦截] 网络拦截器未启用")
                return None

            # 获取拦截的数据
            intercepted_data = self.network_interceptor.get_intercepted_data()

            if not intercepted_data['requests'] and not intercepted_data['responses']:
                logger.info("ℹ️ [网络拦截] 没有拦截到数据")
                return None

            # 生成文件名
            if not filename:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tiktok_shop_intercepted_{timestamp}.json"

            # 保存数据
            saved_file = self.network_interceptor.save_intercepted_data(intercepted_data, filename)

            if saved_file:
                logger.info(f"💾 [网络拦截] 拦截数据已手动保存: {saved_file}")
                return str(saved_file)
            else:
                logger.error("❌ [网络拦截] 手动保存失败")
                return None

        except Exception as e:
            logger.error(f"❌ [网络拦截] 手动保存拦截数据失败: {str(e)}")
            return None

    async def close(self):
        """关闭爬虫，清理资源"""
        try:
            # 关闭网络拦截器
            if self.network_interceptor:
                logger.info("🔒 [TikTok爬虫] 关闭网络拦截器...")
                self.network_interceptor.close()
                self.network_interceptor = None

            if self.hybrid_crawler:
                await self.hybrid_crawler.close()
                logger.info("✅ [TikTok爬虫] 混合爬虫已关闭")
            else:
                await super().close()
                logger.info("✅ [TikTok爬虫] 基础爬虫已关闭")
        except Exception as e:
            logger.error(f"❌ [TikTok爬虫] 关闭时出现异常: {str(e)}")

    def get_crawler_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        if self.hybrid_crawler:
            return self.hybrid_crawler.get_stats()
        else:
            return {
                'current_mode': 'direct',
                'stats': {
                    'direct_requests': self.request_count,
                    'browser_requests': 0,
                    'captcha_detections': 0,
                    'mode_switches': 0,
                    'total_requests': self.request_count
                }
            }
        
    
    def parse_product_url(self, url: str) -> Optional[Dict[str, str]]:
        """解析PDP商品URL，提取商品ID和产品slug"""
        try:
            logger.debug(f"🔍 [TikTok爬虫] 解析PDP URL: {url}")

            parsed = urlparse(url)

            # 只支持PDP格式: /shop/pdp/product-slug/product-id
            if "tiktok.com" in parsed.netloc:
                path_parts = parsed.path.strip('/').split('/')

                if len(path_parts) >= 4 and path_parts[0] == "shop" and path_parts[1] == "pdp":
                    product_slug = path_parts[2]
                    product_id = path_parts[3]

                    result = {
                        "product_id": product_id,
                        "product_slug": product_slug,
                        "shop_id": None,
                        "shop_name": None,
                        "url_type": "pdp"
                    }

                    logger.info(f"✅ [TikTok爬虫] PDP URL解析成功")
                    logger.info(f"   - 产品ID: {product_id}")
                    logger.info(f"   - 产品Slug: {product_slug}")

                    return result

            logger.warning(f"❌ [TikTok爬虫] 无法解析URL格式，仅支持PDP格式: {url}")
            return None

        except Exception as e:
            logger.error(f"💥 [TikTok爬虫] 解析URL失败: {url} - {str(e)}")
            return None
    
    # 移除传统的get_product_detail方法，只保留PDP专用方法

    async def get_product_detail_shipping_info(self, product_url: str) -> Dict[str, Any]:
        """
        从产品详情页面获取运费信息和店铺产品数量
        实现两步提取过程的第二步：访问产品详情页面获取运费数据和店铺信息
        """
        try:
            logger.info(f"🚚 [详情提取] 开始获取产品运费信息和店铺产品数量: {product_url}")

            # 获取产品详情页面HTML
            html_content = await self.get_text(product_url)
            if not html_content:
                logger.warning(f"⚠️ [详情提取] 无法获取产品页面内容: {product_url}")
                return {
                    "shipping_fee": {"price_str": "", "price_val": "", "currency": ""},
                    "shop_product_count": 0
                }

            return await self._extract_detail_info_from_html(html_content, product_url)

        except Exception as e:
            logger.error(f"❌ [详情提取] 获取运费信息和店铺产品数量失败: {product_url} - {str(e)}")
            return {
                "shipping_fee": {"price_str": "", "price_val": "", "currency": ""},
                "shop_product_count": 0
            }

    async def get_product_detail_shipping_info_concurrent(self, product_url: str, request_manager: ConcurrentRequestManager) -> Dict[str, Any]:
        """
        使用并发管理器获取产品详情信息
        """
        try:
            logger.debug(f"🔄 [并发详情] 获取产品详情: {product_url}")

            # 使用并发管理器发起请求
            result = await request_manager.make_request(product_url)

            if not result.success:
                logger.warning(f"⚠️ [并发详情] 请求失败: {product_url} - {result.error}")
                return {
                    "shipping_fee": {"price_str": "", "price_val": "", "currency": ""},
                    "shop_product_count": 0
                }

            return await self._extract_detail_info_from_html(result.data, product_url)

        except Exception as e:
            logger.error(f"❌ [并发详情] 获取详情信息失败: {product_url} - {str(e)}")
            return {
                "shipping_fee": {"price_str": "", "price_val": "", "currency": ""},
                "shop_product_count": 0
            }

    async def _extract_detail_info_from_html(self, html_content: str, product_url: str) -> Dict[str, Any]:
        """从HTML内容中提取详情信息"""
        try:
            # 查找包含运费信息和店铺信息的JSON数据
            import re

            # 初始化返回结果
            result = {
                "shipping_fee": {"price_str": "", "price_val": "", "currency": ""},
                "shop_product_count": 0
            }

            # 查找script标签中的JSON数据
            script_pattern = r'<script[^>]*>.*?window\.__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*({.*?});.*?</script>'
            script_matches = re.findall(script_pattern, html_content, re.DOTALL | re.IGNORECASE)

            for script_content in script_matches:
                try:
                    data = json.loads(script_content)

                    # 查找运费信息
                    shipping_fee = self._extract_shipping_fee_from_json(data)
                    if shipping_fee:
                        result["shipping_fee"] = shipping_fee
                        logger.info(f"✅ [运费提取] 成功提取运费信息: {shipping_fee}")

                    # 查找店铺产品数量信息
                    shop_product_count = self._extract_shop_product_count_from_json(data)
                    if shop_product_count > 0:
                        result["shop_product_count"] = shop_product_count
                        logger.info(f"✅ [店铺信息] 成功提取店铺产品数量: {shop_product_count}")

                    # 如果两个信息都找到了，可以提前返回
                    if result["shipping_fee"]["price_str"] and result["shop_product_count"] > 0:
                        return result

                except json.JSONDecodeError:
                    continue

            # 备用方法：查找其他可能的JSON数据结构
            # 查找运费信息
            shipping_pattern = r'("shipping_fee"\s*:\s*{[^}]+})'
            shipping_matches = re.findall(shipping_pattern, html_content, re.IGNORECASE)

            for match in shipping_matches:
                try:
                    json_str = '{' + match + '}'
                    data = json.loads(json_str)
                    shipping_fee_data = data.get('shipping_fee', {})

                    if shipping_fee_data:
                        result["shipping_fee"] = {
                            "price_str": shipping_fee_data.get('price_str', ''),
                            "price_val": shipping_fee_data.get('price_val', ''),
                            "currency": shipping_fee_data.get('currency', '')
                        }
                        logger.info(f"✅ [运费提取] 备用方法成功提取运费信息: {result['shipping_fee']}")
                        break

                except json.JSONDecodeError:
                    continue

            # 查找店铺信息
            shop_pattern = r'("shop_info"\s*:\s*{[^}]*"on_sell_product_count"\s*:\s*(\d+)[^}]*})'
            shop_matches = re.findall(shop_pattern, html_content, re.IGNORECASE)

            for match in shop_matches:
                try:
                    if len(match) >= 2:
                        count_str = match[1]
                        shop_product_count = int(count_str)
                        if shop_product_count > 0:
                            result["shop_product_count"] = shop_product_count
                            logger.info(f"✅ [店铺信息] 备用方法成功提取店铺产品数量: {shop_product_count}")
                            break
                except (ValueError, TypeError):
                    continue

            if result["shipping_fee"]["price_str"] or result["shop_product_count"] > 0:
                logger.info(f"✅ [详情提取] 提取完成: 运费={result['shipping_fee']}, 店铺产品数量={result['shop_product_count']}")
            else:
                logger.warning(f"⚠️ [详情提取] 未找到运费信息和店铺产品数量: {product_url}")

            return result

        except Exception as e:
            logger.error(f"❌ [详情提取] 获取运费信息和店铺产品数量失败: {product_url} - {str(e)}")
            return {
                "shipping_fee": {"price_str": "", "price_val": "", "currency": ""},
                "shop_product_count": 0
            }

    def _extract_shipping_fee_from_json(self, data: Dict) -> Optional[Dict[str, str]]:
        """从JSON数据中递归查找运费信息"""
        try:
            # 递归搜索shipping_fee字段
            def find_shipping_fee(obj, path=""):
                if isinstance(obj, dict):
                    if 'shipping_fee' in obj:
                        shipping_data = obj['shipping_fee']
                        if isinstance(shipping_data, dict):
                            return {
                                "price_str": shipping_data.get('price_str', ''),
                                "price_val": shipping_data.get('price_val', ''),
                                "currency": shipping_data.get('currency', '')
                            }

                    # 递归搜索子对象
                    for key, value in obj.items():
                        result = find_shipping_fee(value, f"{path}.{key}")
                        if result:
                            return result

                elif isinstance(obj, list):
                    # 搜索列表中的每个元素
                    for i, item in enumerate(obj):
                        result = find_shipping_fee(item, f"{path}[{i}]")
                        if result:
                            return result

                return None

            return find_shipping_fee(data)

        except Exception as e:
            logger.error(f"❌ [运费提取] JSON解析失败: {str(e)}")
            return None

    def _extract_shop_product_count_from_json(self, data: Dict) -> int:
        """从JSON数据中递归查找店铺产品数量信息"""
        try:
            # 递归搜索shop_info中的on_sell_product_count字段
            def find_shop_product_count(obj, path=""):
                if isinstance(obj, dict):
                    # 查找shop_info结构
                    if 'shop_info' in obj:
                        shop_info = obj['shop_info']
                        if isinstance(shop_info, dict):
                            count = shop_info.get('on_sell_product_count')
                            if count is not None:
                                try:
                                    count_int = int(count)
                                    if count_int > 0:
                                        logger.info(f"🔍 [店铺信息] 在路径 '{path}.shop_info' 找到产品数量: {count_int}")
                                        return count_int
                                except (ValueError, TypeError):
                                    pass

                    # 递归搜索子对象
                    for key, value in obj.items():
                        result = find_shop_product_count(value, f"{path}.{key}" if path else key)
                        if result > 0:
                            return result

                elif isinstance(obj, list):
                    # 搜索列表中的每个元素
                    for i, item in enumerate(obj):
                        result = find_shop_product_count(item, f"{path}[{i}]" if path else f"[{i}]")
                        if result > 0:
                            return result

                return 0

            return find_shop_product_count(data)

        except Exception as e:
            logger.error(f"❌ [店铺信息] JSON解析失败: {str(e)}")
            return 0

    async def batch_get_product_details_concurrent(self, products: List[Any], progress_callback=None, filter_conditions=None) -> List[Any]:
        """
        批量并发获取产品详情信息，并在过程中应用过滤条件
        """
        logger.info(f"🚀 [批量并发] 开始批量获取 {len(products)} 个产品的详情信息")

        if filter_conditions:
            logger.info(f"🔍 [批量并发] 应用过滤条件: {filter_conditions}")

        # 导入临时存储
        from ..models.tiktok_product import TikTokProduct, tiktok_storage

        # 创建并发管理器
        async with ConcurrentRequestManager() as request_manager:
            # 准备任务列表
            tasks = []
            product_objects = []

            for i, product_data in enumerate(products):
                try:
                    # 步骤1：创建基础TikTok产品对象（从商店页面数据）
                    tiktok_product = TikTokProduct.from_tiktok_data(product_data, "")
                    product_objects.append(tiktok_product)

                    # 步骤2：如果有产品URL，创建并发任务
                    if tiktok_product.canonical_url:
                        task = asyncio.create_task(
                            self._process_single_product_concurrent(
                                tiktok_product, request_manager, i + 1, filter_conditions
                            )
                        )
                        tasks.append(task)
                    else:
                        logger.warning(f"⚠️ [批量并发] 产品 {i+1} 缺少产品URL，跳过详情提取")
                        # 检查基础产品是否符合过滤条件
                        if self._product_matches_filter(tiktok_product, filter_conditions):
                            tiktok_storage.add_product(tiktok_product)
                        else:
                            logger.debug(f"🔍 [批量并发] 产品 {i+1} 不符合过滤条件，跳过")

                except Exception as e:
                    logger.error(f"❌ [批量并发] 创建产品对象失败 {i+1}: {str(e)}")
                    continue

            if not tasks:
                logger.warning("⚠️ [批量并发] 没有有效的产品URL需要处理")
                return product_objects

            logger.info(f"📊 [批量并发] 创建了 {len(tasks)} 个并发任务")

            # 执行并发任务
            completed = 0
            for coro in asyncio.as_completed(tasks):
                try:
                    updated_product = await coro
                    completed += 1

                    if progress_callback:
                        progress_callback(completed, len(tasks))

                    logger.info(f"📈 [批量并发] 进度: {completed}/{len(tasks)} 完成")

                except Exception as e:
                    logger.error(f"❌ [批量并发] 任务执行失败: {str(e)}")
                    completed += 1

            logger.info(f"✅ [批量并发] 批量处理完成，共处理 {completed} 个产品")

        return product_objects

    async def _process_single_product_concurrent(
        self,
        tiktok_product: Any,
        request_manager: ConcurrentRequestManager,
        index: int,
        filter_conditions: Dict[str, Any] = None
    ) -> Any:
        """
        并发处理单个产品的详情信息获取
        """
        try:
            logger.debug(f"🔄 [单品并发] 处理产品 {index}: {tiktok_product.canonical_url}")

            # 获取产品详情信息
            detail_info = await self.get_product_detail_shipping_info_concurrent(
                tiktok_product.canonical_url, request_manager
            )

            # 更新运费信息
            shipping_fee = detail_info.get('shipping_fee', {})
            tiktok_product.shipping_fee_price_str = shipping_fee.get('price_str', '')
            tiktok_product.shipping_fee_price_val = shipping_fee.get('price_val', '')
            tiktok_product.shipping_fee_currency = shipping_fee.get('currency', '')

            # 更新店铺产品数量
            shop_product_count = detail_info.get('shop_product_count', 0)
            if shop_product_count > 0:
                tiktok_product.shop_product_count = shop_product_count
                logger.debug(f"✅ [单品并发] 产品 {index} 店铺产品数量更新: {shop_product_count}")

            # 检查产品是否符合过滤条件
            if self._product_matches_filter(tiktok_product, filter_conditions):
                # 添加到临时存储
                from ..models.tiktok_product import tiktok_storage
                tiktok_storage.add_product(tiktok_product)
                logger.debug(f"✅ [单品并发] 产品 {index} 详情信息更新完成并已存储")
            else:
                logger.debug(f"🔍 [单品并发] 产品 {index} 不符合过滤条件，跳过存储")

            return tiktok_product

        except Exception as e:
            logger.error(f"❌ [单品并发] 处理产品 {index} 失败: {str(e)}")
            # 即使失败也要检查基础产品是否符合过滤条件
            if self._product_matches_filter(tiktok_product, filter_conditions):
                from ..models.tiktok_product import tiktok_storage
                tiktok_storage.add_product(tiktok_product)
                logger.debug(f"⚠️ [单品并发] 产品 {index} 处理失败但基础信息符合条件，已存储")
            else:
                logger.debug(f"🔍 [单品并发] 产品 {index} 处理失败且不符合过滤条件，跳过")
            return tiktok_product

    def _product_matches_filter(self, product: Any, filter_conditions: Dict[str, Any]) -> bool:
        """检查产品是否匹配过滤条件"""
        try:
            # 如果没有过滤条件，则匹配所有产品
            if not filter_conditions:
                return True

            # 店铺商品数量过滤
            shop_count = getattr(product, 'shop_product_count', 0)
            if not (filter_conditions.get('shop_count_min', 0) <= shop_count <= filter_conditions.get('shop_count_max', 999999)):
                logger.debug(f"🔍 [过滤] 店铺商品数量不符合: {shop_count} 不在 [{filter_conditions.get('shop_count_min', 0)}, {filter_conditions.get('shop_count_max', 999999)}]")
                return False

            # 运费过滤
            shipping_fee = 0.0
            try:
                shipping_fee_val = getattr(product, 'shipping_fee_price_val', '')
                if shipping_fee_val and str(shipping_fee_val).replace('.', '').replace('-', '').isdigit():
                    shipping_fee = float(shipping_fee_val)
            except (ValueError, AttributeError):
                shipping_fee = 0.0

            if not (filter_conditions.get('shipping_fee_min', 0.0) <= shipping_fee <= filter_conditions.get('shipping_fee_max', 9999.99)):
                logger.debug(f"🔍 [过滤] 运费不符合: {shipping_fee} 不在 [{filter_conditions.get('shipping_fee_min', 0.0)}, {filter_conditions.get('shipping_fee_max', 9999.99)}]")
                return False

            # 价格过滤 - 使用 sale_price 字段
            price = 0.0
            try:
                price_val = getattr(product, 'sale_price', 0)
                if price_val:
                    price = float(price_val)
            except (ValueError, AttributeError):
                price = 0.0

            if not (filter_conditions.get('price_min', 0.0) <= price <= filter_conditions.get('price_max', 99999.99)):
                logger.debug(f"🔍 [过滤] 价格不符合: {price} 不在 [{filter_conditions.get('price_min', 0.0)}, {filter_conditions.get('price_max', 99999.99)}]")
                return False

            # 销量过滤 - 使用 sold_count 字段
            sales = 0
            try:
                sales_val = getattr(product, 'sold_count', 0)
                if sales_val:
                    sales = int(sales_val)
            except (ValueError, AttributeError):
                sales = 0

            if not (filter_conditions.get('sales_min', 0) <= sales <= filter_conditions.get('sales_max', 999999)):
                logger.debug(f"🔍 [过滤] 销量不符合: {sales} 不在 [{filter_conditions.get('sales_min', 0)}, {filter_conditions.get('sales_max', 999999)}]")
                return False

            logger.debug(f"✅ [过滤] 产品符合所有过滤条件")
            return True

        except Exception as e:
            logger.error(f"❌ [过滤] 产品过滤检查失败: {str(e)}")
            return True  # 出错时默认通过过滤

    async def get_pdp_product_detail(self, url: str, filter_conditions: Dict[str, Any] = None, task_config: Dict[str, Any] = None) -> Optional[Product]:
        """从PDP URL获取商品详情"""
        try:
            # 存储任务配置和过滤条件
            if task_config:
                self.current_task_config = task_config
                logger.info(f"📋 [PDP爬取] 接收任务配置")

            if filter_conditions:
                self.current_filter_conditions = filter_conditions
                sales_min = filter_conditions.get('sales_min', 0)
                logger.info(f"📋 [PDP爬取] 接收过滤条件，最小销量阈值: {sales_min}")

            logger.info(f"🚀 [PDP爬取] 开始爬取PDP商品: {url}")

            # 第一步：获取产品页面HTML以提取seller_id（使用优化的PDP加载逻辑）
            logger.info(f"📄 [PDP爬取] 步骤1: 开始获取产品页面HTML内容（优化版）")
            start_time = time.time()

            html_content = await self.get_text(url)

            if not html_content:
                logger.error(f"❌ [PDP爬取] 步骤1失败: 无法获取产品页面内容: {url}")
                return None

            elapsed = time.time() - start_time
            logger.info(f"✅ [PDP爬取] 步骤1成功: 获取到HTML内容")
            logger.info(f"   - 内容长度: {len(html_content)} 字符")
            logger.info(f"   - 耗时: {elapsed:.2f}秒")

            # 解析HTML提取seller_id和其他信息
            logger.info(f"🔍 [PDP爬取] 步骤2: 开始从HTML中提取seller信息")
            seller_info = self._extract_seller_info_from_html(html_content)

            if not seller_info:
                logger.error(f"❌ [PDP爬取] 步骤2失败: 无法从HTML中提取seller信息")
                logger.debug(f"🔍 [PDP爬取] HTML内容预览: {html_content[:500]}...")
                return None

            seller_id = seller_info.get("seller_id")
            shop_name = seller_info.get("shop_name")
            shop_id = seller_info.get("shop_id")

            logger.info(f"✅ [PDP爬取] 步骤2成功: 提取到seller信息")
            logger.info(f"   - seller_id: {seller_id}")
            logger.info(f"   - shop_name: {shop_name}")
            logger.info(f"   - shop_id: {shop_id}")

            if not seller_id:
                logger.error(f"❌ [PDP爬取] seller_id为空，无法继续")
                return None

            # 第二步：构建shop API URL获取产品数据
            logger.info(f"🔗 [PDP爬取] 步骤3: 构建shop API URL")

            # 如果没有shop_name，尝试从URL中提取或使用默认值
            if not shop_name:
                logger.warning(f"⚠️ [PDP爬取] shop_name为空，尝试从URL中提取")
                # 从URL中提取product_slug作为shop_name的替代
                import re
                slug_match = re.search(r'/shop/pdp/([^/]+)/', url)
                if slug_match:
                    shop_name = slug_match.group(1)
                    logger.info(f"🔍 [PDP爬取] 从URL提取到shop_name: {shop_name}")
                else:
                    shop_name = "unknown"
                    logger.warning(f"⚠️ [PDP爬取] 无法提取shop_name，使用默认值: {shop_name}")

            shop_api_url = f"https://www.tiktok.com/shop/store/{shop_name}/{seller_id}"
            params = {
                "source": "product_detail",
                "enter_from": "product_detail",
                "enter_method": "product_info"
            }

            logger.info(f"🔗 [PDP爬取] 构建的shop API URL: {shop_api_url}")
            logger.info(f"📋 [PDP爬取] API参数: {params}")

            # 获取shop页面HTML内容（不是JSON API）
            logger.info(f"🌐 [PDP爬取] 步骤4: 开始请求shop页面HTML（优化版）")
            logger.info(f"🔗 [PDP爬取] 请求URL: {shop_api_url}")
            logger.info(f"📋 [PDP爬取] 请求参数: {params}")

            shop_start_time = time.time()
            # 使用专门的shop页面爬取方法，启用零销量检测
            shop_html = await self.get_shop_page_with_zero_sales_detection(
                shop_api_url,
                params=params,
                filter_conditions=filter_conditions
            )

            if not shop_html:
                logger.error(f"❌ [PDP爬取] 步骤4失败: 无法获取shop页面HTML")
                logger.error(f"   - 页面URL: {shop_api_url}")
                logger.error(f"   - 参数: {params}")
                return None

            shop_elapsed = time.time() - shop_start_time
            logger.info(f"✅ [PDP爬取] 步骤4成功: 获取到shop页面HTML")
            logger.info(f"   - HTML内容长度: {len(shop_html)} 字符")
            logger.info(f"   - 耗时: {shop_elapsed:.2f}秒")

            # 简化的内容预览（调试信息）
            logger.debug(f"📝 [PDP爬取] HTML内容预览: {shop_html[:500]}...")

            # 检查HTML内容中的关键信息
            has_product_data = 'product' in shop_html.lower()
            has_json_data = '{' in shop_html and '}' in shop_html
            has_script_tags = '<script' in shop_html.lower()

            logger.info(f"🔍 [PDP爬取] HTML内容分析:")
            logger.info(f"   包含'product'关键词: {'是' if has_product_data else '否'}")
            logger.info(f"   包含JSON数据: {'是' if has_json_data else '否'}")
            logger.info(f"   包含script标签: {'是' if has_script_tags else '否'}")

            # 步骤5: 从HTML DOM中提取所有PDP链接并并发获取商品数据
            logger.info(f"📦 [PDP爬取] 步骤5: 开始从HTML DOM提取所有PDP链接")
            products = await self._extract_and_fetch_all_products(shop_html, filter_conditions)

            if products:
                logger.info(f"🎉 [PDP爬取] 步骤5成功: 成功获取 {len(products)} 个商品")
                for i, product in enumerate(products[:3]):  # 只显示前3个商品的信息
                    logger.info(f"   商品 {i+1}: {product.product_id} - {product.title[:30]}...")
                if len(products) > 3:
                    logger.info(f"   ... 还有 {len(products) - 3} 个商品")
                logger.info(f"✅ [PDP爬取] 完整流程成功完成")

                # 保存所有商品到临时存储
                logger.info(f"💾 [数据保存] 开始保存 {len(products)} 个商品到临时存储")
                saved_count = 0
                for i, product in enumerate(products):
                    try:
                        self._save_product_to_temp_storage(product)
                        saved_count += 1
                        logger.debug(f"✅ [数据保存] 商品 {i+1}/{len(products)} 保存成功: {product.product_id}")
                    except Exception as e:
                        logger.error(f"❌ [数据保存] 商品 {i+1}/{len(products)} 保存失败: {str(e)}")

                logger.info(f"📊 [数据保存] 保存完成，成功: {saved_count}/{len(products)} 个商品")

                # 返回第一个商品（保持与原接口的兼容性）
                return products[0] if products else None
            else:
                logger.error(f"❌ [PDP爬取] 步骤5失败: 无法获取任何商品数据")
                return None

        except Exception as e:
            logger.error(f"💥 [PDP爬取] 异常: {str(e)}")
            logger.error(f"   - URL: {url}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return None

    def _save_product_to_temp_storage(self, product):
        """将Product对象转换为TikTokProduct对象并保存到临时存储"""
        try:
            from ..models.tiktok_product import TikTokProduct, tiktok_storage

            logger.debug(f"🔄 [数据转换] 开始转换Product对象为TikTokProduct对象: {product.product_id}")

            # 提取图片URL
            image_urls = []
            if product.images:
                image_urls = [img.url for img in product.images]
            image_urls_str = ', '.join(image_urls) if image_urls else ''

            # 提取运费信息
            shipping_fee = product.metadata.get('shipping_fee', {}) if product.metadata else {}
            shipping_fee_price_str = shipping_fee.get('price_str', '$0.00')
            shipping_fee_price_val = shipping_fee.get('price_val', '0.00')
            shipping_fee_currency = shipping_fee.get('currency', 'USD')

            # 提取店铺商品数量
            shop_product_count = product.metadata.get('shop_product_count', 0) if product.metadata else 0

            # 计算折扣金额
            discount_amount = 0
            if product.original_price and product.original_price > product.price:
                discount_amount = float(product.original_price - product.price)

            # 创建TikTokProduct对象
            tiktok_product = TikTokProduct(
                product_id=product.product_id,
                image_urls=image_urls_str,
                currency=product.currency.value,
                sale_price=float(product.price),
                original_price=float(product.original_price) if product.original_price else float(product.price),
                discount_amount=discount_amount,
                rating=product.rating.average_rating if product.rating else 0.0,
                review_count=product.rating.total_reviews if product.rating else 0,
                sold_count=product.sold_count,
                listing_time=product.scraped_at.strftime("%Y-%m-%d %H:%M:%S"),
                shipping_fee_price_str=shipping_fee_price_str,
                shipping_fee_price_val=shipping_fee_price_val,
                shipping_fee_currency=shipping_fee_currency,
                shop_name=product.shop_name,
                shop_product_count=shop_product_count,
                canonical_url=product.product_url,
                source_url=product.product_url
            )

            # 保存到临时存储
            tiktok_storage.add_product(tiktok_product)
            logger.debug(f"✅ [数据转换] 成功转换并保存Product对象到临时存储: {tiktok_product.product_id}")

        except Exception as e:
            logger.error(f"❌ [数据转换] 转换Product对象失败: {str(e)}")
            import traceback
            logger.error(f"   - 异常堆栈: {traceback.format_exc()}")

    async def _extract_and_fetch_all_products(self, shop_html: str, filter_conditions: Dict[str, Any] = None) -> List[Product]:
        """
        从HTML DOM中提取所有PDP链接并并发获取商品数据

        Args:
            shop_html: shop页面的HTML内容
            filter_conditions: 过滤条件

        Returns:
            List[Product]: 商品列表
        """
        try:
            logger.info(f"🔗 [PDP提取] 开始从HTML DOM提取所有PDP链接")

            # 保存shop_html内容到文件用于调试
            # try:
            #     import os
            #     from datetime import datetime
            #     debug_dir = "debug_shop_html"
            #     if not os.path.exists(debug_dir):
            #         os.makedirs(debug_dir)
            #     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            #     debug_file = os.path.join(debug_dir, f"shop_html_{timestamp}.html")
            #     with open(debug_file, 'w', encoding='utf-8') as f:
            #         f.write(shop_html)
            #     logger.debug(f"📄 [调试] shop_html已保存到: {debug_file}")
            # except Exception as e:
            #     logger.debug(f"⚠️ [调试] 保存shop_html失败: {str(e)}")

            # 使用selenium_crawler的HTML DOM解析逻辑提取商品数据
            from .selenium_crawler import SeleniumCrawler
            from ..config.settings import CrawlerConfig

            # 创建临时的selenium_crawler实例用于HTML解析
            config = CrawlerConfig()
            temp_crawler = SeleniumCrawler(config)

            # 提取所有商品数据（包含PDP链接）
            logger.debug(f"📄 [PDP提取] shop_html长度: {len(shop_html)} 字符")
            products_data = temp_crawler._extract_products_from_content(shop_html)
            logger.info(f"📦 [PDP提取] 从HTML DOM提取到 {len(products_data)} 个商品")

            if not products_data:
                logger.warning(f"⚠️ [PDP提取] 未从HTML DOM中提取到任何商品数据")
                logger.debug(f"📄 [PDP提取] shop_html前1000字符: {shop_html[:1000]}")
                return []

            # 显示提取到的商品信息
            for i, product_data in enumerate(products_data[:3]):  # 只显示前3个
                logger.debug(f"📦 [PDP提取] 商品 {i+1}: ID={product_data.get('product_id', 'unknown')}, 标题={product_data.get('title', 'unknown')[:30]}..., 销量={product_data.get('sold_count', 0)}")
            if len(products_data) > 3:
                logger.debug(f"📦 [PDP提取] ... 还有 {len(products_data) - 3} 个商品")

            # 应用销量过滤
            sales_min_threshold = 0
            if filter_conditions:
                sales_min_threshold = filter_conditions.get('sales_min', 0)

            logger.debug(f"🔍 [销量过滤] 销量阈值: {sales_min_threshold}")

            # 过滤满足销量要求的商品
            filtered_products = []
            for product_data in products_data:
                sold_count = product_data.get('sold_count', 0)
                if sold_count >= sales_min_threshold:
                    filtered_products.append(product_data)
                    logger.debug(f"✅ [销量过滤] 商品 {product_data.get('product_id', 'unknown')} 销量 {sold_count} 满足要求")
                else:
                    logger.debug(f"🚫 [销量过滤] 商品 {product_data.get('product_id', 'unknown')} 销量 {sold_count} 低于阈值 {sales_min_threshold}")

            logger.info(f"✅ [销量过滤] {len(filtered_products)} 个商品满足销量要求 (>= {sales_min_threshold})")

            if not filtered_products:
                logger.warning(f"⚠️ [销量过滤] 没有商品满足销量要求")
                logger.debug(f"📊 [销量过滤] 所有商品销量情况:")
                for product_data in products_data:
                    logger.debug(f"   - {product_data.get('product_id', 'unknown')}: {product_data.get('sold_count', 0)} 销量")
                return []

            # 提取PDP链接
            pdp_urls = []
            for product_data in filtered_products:
                pdp_url = product_data.get('pdp_url')
                if pdp_url:
                    pdp_urls.append(pdp_url)
                    logger.debug(f"🔗 [PDP提取] 商品 {product_data.get('product_id', 'unknown')} PDP链接: {pdp_url}")
                else:
                    logger.debug(f"⚠️ [PDP提取] 商品 {product_data.get('product_id', 'unknown')} 缺少PDP链接")

            logger.info(f"🔗 [PDP提取] 提取到 {len(pdp_urls)} 个有效的PDP链接")

            if not pdp_urls:
                logger.warning(f"⚠️ [PDP提取] 没有有效的PDP链接")
                return []

            # 为所有PDP链接添加查询参数
            enhanced_urls = []
            query_params = "?source=product_detail&enter_from=product_detail&enter_method=feed_list_bought_together"

            for url in pdp_urls:
                enhanced_url = url + query_params
                enhanced_urls.append(enhanced_url)

            logger.info(f"🔗 [URL增强] 为 {len(pdp_urls)} 个PDP链接添加查询参数")
            logger.debug(f"📋 [URL增强] 查询参数: {query_params}")

            # 并发获取所有商品的详细数据
            logger.info(f"🚀 [并发获取] 开始并发获取 {len(enhanced_urls)} 个商品的详细数据")
            products = await self._fetch_products_concurrently(enhanced_urls, filter_conditions)

            logger.info(f"✅ [并发获取] 成功获取 {len(products)} 个商品的详细数据")
            return products

        except Exception as e:
            logger.error(f"❌ [PDP提取] 提取和获取商品数据失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return []

    async def _fetch_products_concurrently(self, pdp_urls: List[str], filter_conditions: Dict[str, Any] = None) -> List[Product]:
        """
        并发获取多个商品的详细数据
        支持浏览器模式下的多标签页并发访问

        Args:
            pdp_urls: PDP链接列表
            filter_conditions: 过滤条件

        Returns:
            List[Product]: 商品列表
        """
        try:
            logger.info(f"🚀 [并发获取] 开始并发获取 {len(pdp_urls)} 个商品的详细数据")

            # 检测当前爬虫模式
            is_browser_mode = self._is_browser_mode()

            if is_browser_mode:
                logger.info(f"🌐 [并发获取] 检测到浏览器模式，使用多标签页并发访问")
                # 浏览器模式：使用8个并发标签页（优化版并发加载）
                max_concurrent = 8
                return await self._fetch_products_with_browser_tabs(pdp_urls, filter_conditions, max_concurrent)
            else:
                logger.info(f"⚡ [并发获取] 使用直连模式并发访问")
                # 直连模式：使用20个并发请求
                max_concurrent = 20
                return await self._fetch_products_with_direct_requests(pdp_urls, filter_conditions, max_concurrent)

        except Exception as e:
            logger.error(f"❌ [并发获取] 并发获取商品失败: {str(e)}")
            return []

    def _is_browser_mode(self) -> bool:
        """检测当前是否为浏览器模式"""
        try:
            if hasattr(self, 'hybrid_crawler') and self.hybrid_crawler:
                from .crawler_modes import CrawlerMode
                return self.hybrid_crawler.current_mode == CrawlerMode.BROWSER
            return False
        except Exception as e:
            logger.debug(f"⚠️ [模式检测] 检测浏览器模式失败: {str(e)}")
            return False

    async def _fetch_products_with_browser_tabs(self, pdp_urls: List[str], filter_conditions: Dict[str, Any], max_concurrent: int) -> List[Product]:
        """
        使用浏览器多标签页批次处理获取商品数据
        逻辑：分批处理 -> 顺序打开标签页 -> 逆序读取数据 -> 逐个关闭标签页

        Args:
            pdp_urls: PDP链接列表
            filter_conditions: 过滤条件
            max_concurrent: 每批处理的标签页数量（默认8个）

        Returns:
            List[Product]: 商品列表
        """
        try:
            logger.info(f"🌐 [标签页批处理] 开始批次处理，每批 {max_concurrent} 个标签页")

            # 获取Selenium WebDriver实例
            driver = await self._get_selenium_driver()
            if not driver:
                logger.warning(f"⚠️ [标签页批处理] 无法获取WebDriver，回退到直连模式")
                return await self._fetch_products_with_direct_requests(pdp_urls, filter_conditions, 20)

            # 记录主标签页句柄
            main_window = driver.current_window_handle
            logger.info(f"📋 [标签页批处理] 主标签页句柄: {main_window}")

            # 分批处理URL
            all_products = []
            total_batches = (len(pdp_urls) + max_concurrent - 1) // max_concurrent

            for batch_index in range(total_batches):
                start_idx = batch_index * max_concurrent
                end_idx = min(start_idx + max_concurrent, len(pdp_urls))
                batch_urls = pdp_urls[start_idx:end_idx]

                logger.info(f"📦 [批次 {batch_index + 1}/{total_batches}] 处理 {len(batch_urls)} 个商品链接")

                # 处理当前批次
                batch_products = await self._process_single_batch(driver, batch_urls, main_window, batch_index + 1)
                all_products.extend(batch_products)

                logger.info(f"✅ [批次 {batch_index + 1}/{total_batches}] 完成，获取 {len(batch_products)} 个商品")

                # 批次间短暂休息
                if batch_index < total_batches - 1:
                    await asyncio.sleep(1)

            logger.info(f"🎉 [标签页批处理] 所有批次完成，总共获取 {len(all_products)} 个商品")
            return all_products

        except Exception as e:
            logger.error(f"❌ [标签页批处理] 批处理失败: {str(e)}")
            # 回退到直连模式
            logger.info(f"🔄 [标签页批处理] 回退到直连模式")
            return await self._fetch_products_with_direct_requests(pdp_urls, filter_conditions, 20)

    async def _process_single_batch(self, driver, batch_urls: List[str], main_window: str, batch_num: int) -> List[Product]:
        """
        处理单个批次的标签页 - 优化版并发页面加载策略
        流程：并发打开标签页 -> 批量等待 -> 顺序读取数据 -> 逐个关闭标签页

        Args:
            driver: WebDriver实例
            batch_urls: 当前批次的URL列表
            main_window: 主标签页句柄
            batch_num: 批次编号

        Returns:
            List[Product]: 当前批次获取的商品列表
        """
        try:
            import time
            batch_start_time = time.time()
            logger.info(f"🚀 [批次 {batch_num}] 开始并发处理 {len(batch_urls)} 个链接")

            # 第一步：并发打开所有标签页（不等待页面加载完成）
            tab_handles = []
            tab_creation_start = time.time()
            logger.info(f"🔖 [批次 {batch_num}] 开始并发创建标签页...")

            for i, url in enumerate(batch_urls):
                logger.debug(f"🔖 [批次 {batch_num}] 创建标签页 {i+1}/{len(batch_urls)}: {url}")

                tab_handle = await self._create_new_tab_fast(driver, url)
                if tab_handle:
                    tab_handles.append((tab_handle, url))
                    logger.debug(f"✅ [批次 {batch_num}] 标签页 {i+1} 创建成功: {tab_handle}")
                else:
                    logger.warning(f"⚠️ [批次 {batch_num}] 标签页 {i+1} 创建失败")
                    tab_handles.append((None, url))

                # 最小间隔，避免浏览器阻塞（优化到0.02秒）
                await asyncio.sleep(0.02)

            tab_creation_time = time.time() - tab_creation_start
            successful_tabs = sum(1 for h, _ in tab_handles if h)
            logger.info(f"📊 [批次 {batch_num}] 标签页创建完成，成功: {successful_tabs}/{len(tab_handles)}，耗时: {tab_creation_time:.2f}秒")

            # 第二步：批量等待策略 - 统一等待3秒让所有页面充分加载
            if successful_tabs > 0:
                logger.info(f"⏳ [批次 {batch_num}] 批量等待3秒，让所有页面充分加载...")
                await asyncio.sleep(3.0)
                logger.info(f"✅ [批次 {batch_num}] 批量等待完成，开始数据解析")

            # 第三步：按顺序读取数据（保持原有逻辑，但页面已经预加载完成）
            batch_products = []
            logger.info(f"📖 [批次 {batch_num}] 开始按顺序解析已加载的标签页数据")

            # 逆序处理标签页（从最后一个开始，保持原有逻辑）
            for i in range(len(tab_handles) - 1, -1, -1):
                tab_handle, url = tab_handles[i]

                if not tab_handle:
                    # 标签页创建失败，使用直连模式作为回退
                    logger.info(f"🔄 [批次 {batch_num}] 标签页 {i+1} 失败，使用直连模式: {url}")
                    product = await self._fetch_single_product_direct(url)
                    if product:
                        batch_products.append(product)
                    continue

                try:
                    logger.debug(f"📖 [批次 {batch_num}] 解析标签页 {i+1}/{len(tab_handles)}: {tab_handle}")

                    # 切换到当前标签页（页面已经预加载完成）
                    await self._switch_to_tab(driver, tab_handle)

                    # 获取页面HTML内容（页面已经加载完成，速度更快）
                    html_content = await self._get_page_source_from_tab(driver)

                    if html_content:
                        logger.debug(f"📄 [批次 {batch_num}] 标签页 {i+1} HTML获取成功，长度: {len(html_content)} 字符")

                        # 解析商品数据
                        product = await self._extract_product_from_pdp_html(html_content, url)

                        if product:
                            batch_products.append(product)
                            logger.info(f"✅ [批次 {batch_num}] 标签页 {i+1} 商品获取成功: {product.product_id}")
                        else:
                            logger.warning(f"⚠️ [批次 {batch_num}] 标签页 {i+1} 商品数据提取失败")
                    else:
                        logger.warning(f"⚠️ [批次 {batch_num}] 标签页 {i+1} HTML获取失败")

                except Exception as e:
                    logger.error(f"❌ [批次 {batch_num}] 标签页 {i+1} 处理异常: {str(e)}")

                finally:
                    # 第三步：关闭当前标签页
                    try:
                        await self._close_single_tab(driver, tab_handle, main_window)
                        logger.info(f"🗑️ [批次 {batch_num}] 标签页 {i+1} 已关闭")
                    except Exception as e:
                        logger.error(f"❌ [批次 {batch_num}] 关闭标签页 {i+1} 失败: {str(e)}")

                    # 标签页间短暂间隔
                    await asyncio.sleep(0.3)

            # 性能统计
            batch_total_time = time.time() - batch_start_time
            avg_time_per_product = batch_total_time / len(batch_urls) if batch_urls else 0

            logger.info(f"🎯 [批次 {batch_num}] 批次处理完成，获取 {len(batch_products)} 个商品")
            logger.info(f"⏱️ [批次 {batch_num}] 性能统计: 总耗时 {batch_total_time:.2f}秒，平均每个商品 {avg_time_per_product:.2f}秒")
            logger.info(f"🚀 [批次 {batch_num}] 并发优化效果: 标签页创建 {tab_creation_time:.2f}秒 + 批量等待 3.0秒 + 数据解析 {batch_total_time - tab_creation_time - 3.0:.2f}秒")

            return batch_products

        except Exception as e:
            logger.error(f"❌ [批次 {batch_num}] 批次处理失败: {str(e)}")

            # 清理可能残留的标签页
            await self._cleanup_remaining_tabs(driver, main_window)

            return []

    async def _fetch_products_with_direct_requests(self, pdp_urls: List[str], filter_conditions: Dict[str, Any], max_concurrent: int) -> List[Product]:
        """
        使用直连HTTP请求并发获取商品数据

        Args:
            pdp_urls: PDP链接列表
            filter_conditions: 过滤条件
            max_concurrent: 最大并发数

        Returns:
            List[Product]: 商品列表
        """
        try:
            logger.info(f"⚡ [直连并发] 开始使用 {max_concurrent} 个并发请求获取商品数据")

            # 使用信号量控制并发数量
            semaphore = asyncio.Semaphore(max_concurrent)

            async def fetch_single_product_direct(pdp_url: str) -> Optional[Product]:
                """使用直连模式获取单个商品数据"""
                async with semaphore:
                    try:
                        logger.info(f"📦 [直连获取] 开始获取商品: {pdp_url}")

                        # 使用直连模式获取HTML
                        html_content = await self._get_html_content_direct(pdp_url)

                        if not html_content:
                            logger.warning(f"⚠️ [直连获取] 无法获取商品页面: {pdp_url}")
                            return None

                        logger.info(f"📄 [直连获取] HTML获取成功，长度: {len(html_content)} 字符")

                        # 解析商品数据
                        product = await self._extract_product_from_pdp_html(html_content, pdp_url)

                        if product:
                            logger.info(f"✅ [直连获取] 成功获取商品: {product.product_id}")
                        else:
                            logger.warning(f"⚠️ [直连获取] 商品数据提取失败: {pdp_url}")

                        return product

                    except Exception as e:
                        logger.error(f"❌ [直连获取] 获取商品失败: {pdp_url} - {str(e)}")
                        return None

            # 并发执行所有任务
            tasks = [fetch_single_product_direct(url) for url in pdp_urls]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            return self._process_concurrent_results(results)

        except Exception as e:
            logger.error(f"❌ [直连并发] 直连并发获取失败: {str(e)}")
            return []

    async def _get_selenium_driver(self):
        """获取Selenium WebDriver实例"""
        try:
            if hasattr(self, 'hybrid_crawler') and self.hybrid_crawler:
                if hasattr(self.hybrid_crawler, 'selenium_crawler') and self.hybrid_crawler.selenium_crawler:
                    selenium_crawler = self.hybrid_crawler.selenium_crawler
                    if hasattr(selenium_crawler, 'driver') and selenium_crawler.driver:
                        return selenium_crawler.driver
            logger.warning(f"⚠️ [WebDriver] 无法获取Selenium WebDriver实例")
            return None
        except Exception as e:
            logger.error(f"❌ [WebDriver] 获取WebDriver失败: {str(e)}")
            return None

    async def _create_new_tab_fast(self, driver, url: str) -> Optional[str]:
        """超快速创建新标签页并导航到指定URL（极速并发优化版 - 最小等待时间）"""
        try:
            # 获取当前标签页数量
            current_tabs = len(driver.window_handles)
            logger.debug(f"🔖 [超快标签页] 当前标签页数量: {current_tabs}")

            # 检查标签页数量限制（并发模式下限制为8个）
            if current_tabs >= 9:  # 主标签页 + 8个并发标签页
                logger.warning(f"⚠️ [超快标签页] 标签页数量已达上限 ({current_tabs})，跳过创建")
                return None

            # 创建新标签页并立即导航（一步完成）
            driver.execute_script(f"window.open('{url}');")

            # 最小等待新标签页创建（只等待DOM操作完成）
            await asyncio.sleep(0.02)

            # 获取新标签页句柄
            new_handles = driver.window_handles
            if len(new_handles) > current_tabs:
                new_tab = new_handles[-1]  # 最新的标签页

                # 立即返回标签页句柄，不等待任何加载
                logger.debug(f"⚡ [超快标签页] 极速创建标签页: {new_tab}")
                return new_tab
            else:
                logger.warning(f"⚠️ [超快标签页] 新标签页创建失败")
                return None

        except Exception as e:
            logger.error(f"❌ [快速标签页] 创建标签页失败: {str(e)}")
            return None

    async def _create_new_tab(self, driver, url: str) -> Optional[str]:
        """创建新标签页并导航到指定URL（批处理优化版）"""
        try:
            # 获取当前标签页数量
            current_tabs = len(driver.window_handles)
            logger.debug(f"🔖 [标签页创建] 当前标签页数量: {current_tabs}")

            # 检查标签页数量限制（批处理模式下放宽限制）
            if current_tabs >= 15:  # 批处理模式下允许更多标签页
                logger.warning(f"⚠️ [标签页创建] 标签页数量已达上限 ({current_tabs})，跳过创建")
                return None

            # 创建新标签页
            driver.execute_script("window.open();")

            # 等待新标签页创建
            await asyncio.sleep(0.3)  # 批处理模式下缩短等待时间

            # 获取新标签页句柄
            new_handles = driver.window_handles
            if len(new_handles) > current_tabs:
                new_tab = new_handles[-1]  # 最新的标签页

                # 切换到新标签页
                driver.switch_to.window(new_tab)

                # 导航到目标URL
                driver.get(url)

                # 等待页面开始加载（不等待完全加载）
                await asyncio.sleep(1.5)  # 批处理模式下缩短加载等待时间

                logger.debug(f"✅ [标签页创建] 成功创建标签页: {new_tab}")
                return new_tab
            else:
                logger.warning(f"⚠️ [标签页创建] 新标签页创建失败")
                return None

        except Exception as e:
            logger.error(f"❌ [标签页创建] 创建标签页失败: {str(e)}")
            return None

    async def _switch_to_tab(self, driver, tab_handle: str):
        """切换到指定标签页"""
        try:
            driver.switch_to.window(tab_handle)
            await asyncio.sleep(0.2)
        except Exception as e:
            logger.error(f"❌ [标签页切换] 切换标签页失败: {str(e)}")

    async def _get_page_source_from_tab(self, driver) -> Optional[str]:
        """从当前标签页获取页面源码"""
        try:
            # 等待页面完全加载
            await asyncio.sleep(1)

            # 获取页面源码
            html_content = driver.page_source

            if html_content and len(html_content) > 1000:  # 基本的内容检查
                return html_content
            else:
                logger.warning(f"⚠️ [页面源码] 页面内容过短或为空")
                return None

        except Exception as e:
            logger.error(f"❌ [页面源码] 获取页面源码失败: {str(e)}")
            return None

    async def _cleanup_tab(self, driver, tab_handle: str, main_window: str):
        """清理标签页资源"""
        try:
            # 切换到要关闭的标签页
            if tab_handle in driver.window_handles:
                driver.switch_to.window(tab_handle)
                driver.close()
                logger.debug(f"🗑️ [标签页清理] 已关闭标签页: {tab_handle}")

            # 切换回主标签页
            if main_window in driver.window_handles:
                driver.switch_to.window(main_window)
            else:
                # 如果主标签页不存在，切换到第一个可用标签页
                if driver.window_handles:
                    driver.switch_to.window(driver.window_handles[0])

        except Exception as e:
            logger.error(f"❌ [标签页清理] 清理标签页失败: {str(e)}")

    async def _close_single_tab(self, driver, tab_handle: str, main_window: str):
        """关闭单个标签页并切换回主标签页"""
        try:
            # 检查标签页是否仍然存在
            if tab_handle not in driver.window_handles:
                logger.debug(f"🔍 [标签页关闭] 标签页已不存在: {tab_handle}")
                return

            # 切换到要关闭的标签页
            driver.switch_to.window(tab_handle)

            # 关闭标签页
            driver.close()
            logger.debug(f"🗑️ [标签页关闭] 已关闭标签页: {tab_handle}")

            # 切换回主标签页
            if main_window in driver.window_handles:
                driver.switch_to.window(main_window)
                logger.debug(f"🔄 [标签页关闭] 已切换回主标签页: {main_window}")
            else:
                # 主标签页不存在，切换到第一个可用标签页
                if driver.window_handles:
                    driver.switch_to.window(driver.window_handles[0])
                    logger.debug(f"🔄 [标签页关闭] 主标签页不存在，切换到第一个标签页")

        except Exception as e:
            logger.error(f"❌ [标签页关闭] 关闭标签页失败: {tab_handle} - {str(e)}")

    async def _cleanup_remaining_tabs(self, driver, main_window: str):
        """清理所有残留的标签页，只保留主标签页"""
        try:
            current_handles = driver.window_handles.copy()
            logger.info(f"🧹 [标签页清理] 开始清理残留标签页，当前标签页数: {len(current_handles)}")

            for handle in current_handles:
                if handle != main_window:
                    try:
                        driver.switch_to.window(handle)
                        driver.close()
                        logger.debug(f"🗑️ [标签页清理] 已清理标签页: {handle}")
                    except Exception as e:
                        logger.debug(f"⚠️ [标签页清理] 清理标签页失败: {handle} - {str(e)}")

            # 切换回主标签页
            if main_window in driver.window_handles:
                driver.switch_to.window(main_window)
                logger.info(f"✅ [标签页清理] 清理完成，已切换回主标签页")
            else:
                # 主标签页也被意外关闭，切换到第一个可用标签页
                if driver.window_handles:
                    driver.switch_to.window(driver.window_handles[0])
                    logger.warning(f"⚠️ [标签页清理] 主标签页丢失，切换到第一个可用标签页")

        except Exception as e:
            logger.error(f"❌ [标签页清理] 清理残留标签页失败: {str(e)}")

    async def _get_html_content_direct(self, pdp_url: str) -> Optional[str]:
        """使用直连模式获取HTML内容"""
        try:
            if hasattr(self, 'hybrid_crawler') and self.hybrid_crawler:
                # 使用混合爬虫的直连模式
                html_content = await self.hybrid_crawler._get_text_direct(pdp_url)
            else:
                # 使用基础爬虫的get_text方法
                html_content = await self.get_text(pdp_url)

            return html_content

        except Exception as e:
            logger.error(f"❌ [直连获取] 获取HTML内容失败: {pdp_url} - {str(e)}")
            return None

    async def _fetch_single_product_direct(self, pdp_url: str) -> Optional[Product]:
        """直连模式获取单个商品数据（回退方案）"""
        try:
            logger.info(f"🔄 [回退获取] 使用直连模式获取商品: {pdp_url}")

            html_content = await self._get_html_content_direct(pdp_url)

            if not html_content:
                return None

            product = await self._extract_product_from_pdp_html(html_content, pdp_url)

            if product:
                logger.info(f"✅ [回退获取] 成功获取商品: {product.product_id}")

            return product

        except Exception as e:
            logger.error(f"❌ [回退获取] 获取商品失败: {pdp_url} - {str(e)}")
            return None

    def _process_concurrent_results(self, results: List) -> List[Product]:
        """处理并发结果"""
        try:
            products = []
            failed_count = 0
            exception_count = 0

            for i, result in enumerate(results):
                if isinstance(result, Product):
                    products.append(result)
                    logger.info(f"✅ [结果处理] 商品 {i+1} 成功: {result.product_id}")
                elif isinstance(result, Exception):
                    exception_count += 1
                    logger.error(f"❌ [结果处理] 商品 {i+1} 异常: {str(result)}")
                elif result is None:
                    failed_count += 1
                    logger.warning(f"⚠️ [结果处理] 商品 {i+1} 失败: 返回None")
                else:
                    logger.warning(f"⚠️ [结果处理] 商品 {i+1} 未知结果类型: {type(result)}")

            logger.info(f"📊 [并发获取] 结果统计:")
            logger.info(f"   - 成功: {len(products)} 个")
            logger.info(f"   - 失败: {failed_count} 个")
            logger.info(f"   - 异常: {exception_count} 个")
            logger.info(f"   - 总计: {len(results)} 个")

            return products

        except Exception as e:
            logger.error(f"❌ [结果处理] 处理并发结果失败: {str(e)}")
            return []

    async def _extract_product_from_pdp_html(self, html_content: str, pdp_url: str) -> Optional[Product]:
        """
        从PDP页面HTML中提取商品数据

        Args:
            html_content: PDP页面的HTML内容
            pdp_url: PDP页面URL

        Returns:
            Optional[Product]: 商品对象
        """
        try:
            logger.info(f"📦 [PDP解析] 开始从PDP页面HTML提取商品数据")
            logger.info(f"📄 [PDP解析] HTML内容长度: {len(html_content)} 字符")

            # 从URL中提取product_id
            product_id_match = re.search(r'/([^/]+)$', pdp_url)
            product_id = product_id_match.group(1) if product_id_match else "unknown"
            logger.info(f"🆔 [PDP解析] 提取到商品ID: {product_id}")

            # 保存HTML内容到调试文件
            # try:
            #     import os
            #     from datetime import datetime
            #     debug_dir = "debug_pdp_html"
            #     if not os.path.exists(debug_dir):
            #         os.makedirs(debug_dir)
            #     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            #     debug_file = os.path.join(debug_dir, f"pdp_{product_id}_{timestamp}.html")
            #     with open(debug_file, 'w', encoding='utf-8') as f:
            #         f.write(html_content)
            #     logger.info(f"📄 [调试] PDP HTML已保存到: {debug_file}")
            # except Exception as e:
            #     logger.debug(f"⚠️ [调试] 保存PDP HTML失败: {str(e)}")

            # 查找包含商品数据的JSON脚本
            logger.info(f"🔍 [PDP解析] 开始查找__MODERN_ROUTER_DATA__的JSON脚本")

            # 专门查找__MODERN_ROUTER_DATA__的JSON脚本
            json_patterns = [
                r'<script[^>]*type=["\']application/json["\'][^>]*id=["\']__MODERN_ROUTER_DATA__["\'][^>]*>(.*?)</script>',
                r'<script[^>]*id=["\']__MODERN_ROUTER_DATA__["\'][^>]*type=["\']application/json["\'][^>]*>(.*?)</script>',
            ]

            script_matches = []
            for i, pattern in enumerate(json_patterns):
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                if matches:
                    logger.info(f"✅ [PDP解析] 模式 {i+1} 找到 {len(matches)} 个JSON脚本")
                    script_matches.extend(matches)
                else:
                    logger.debug(f"⚠️ [PDP解析] 模式 {i+1} 未找到JSON脚本")

            if not script_matches:
                logger.warning(f"⚠️ [PDP解析] 未找到任何JSON脚本数据")
            else:
                logger.info(f"📊 [PDP解析] 总共找到 {len(script_matches)} 个JSON脚本")

            for i, script_content in enumerate(script_matches):
                try:
                    logger.info(f"🔍 [PDP解析] 尝试解析第 {i+1} 个JSON脚本，长度: {len(script_content)} 字符")
                    data = json.loads(script_content)
                    logger.info(f"✅ [PDP解析] JSON解析成功")

                    # 从JSON数据中提取商品信息
                    product_info = self._extract_product_info_from_json(data, pdp_url)
                    if product_info:
                        logger.info(f"✅ [PDP解析] 成功从JSON数据提取商品信息")
                        return product_info
                    else:
                        logger.warning(f"⚠️ [PDP解析] JSON数据中未找到商品信息")

                except json.JSONDecodeError as e:
                    logger.warning(f"❌ [PDP解析] JSON解析失败: {str(e)}")
                    continue

            # 如果JSON解析失败，尝试从HTML中直接提取基本信息
            logger.warning(f"⚠️ [PDP解析] JSON解析失败，尝试HTML直接提取")
            result = self._extract_product_info_from_html(html_content, pdp_url, product_id)
            if result:
                logger.info(f"✅ [PDP解析] HTML直接提取成功")
            else:
                logger.warning(f"⚠️ [PDP解析] HTML直接提取也失败")
            return result

        except Exception as e:
            logger.debug(f"❌ [PDP解析] 从PDP页面提取商品数据失败: {str(e)}")
            return None

    def _extract_product_info_from_json(self, data: Dict[str, Any], pdp_url: str) -> Optional[Product]:
        """
        从__MODERN_ROUTER_DATA__的JSON数据中提取商品信息
        分布式数据结构版本：全面扫描整个JSON结构，整合分散的商品数据
        """
        try:
            logger.info(f"🔍 [JSON解析] 开始从__MODERN_ROUTER_DATA__提取分布式商品信息")
            logger.debug(f"📄 [JSON解析] JSON数据结构预览: {str(data)[:500]}...")

            # 使用分布式数据提取策略
            consolidated_data = self._extract_distributed_product_data(data, pdp_url)

            if not consolidated_data:
                logger.error(f"❌ [JSON解析] 分布式数据提取失败")
                self._log_json_structure_debug(data)
                return None

            logger.info(f"✅ [JSON解析] 成功整合分布式商品数据，开始字段提取")

            # 提取并验证所有必需字段
            extracted_fields = self._extract_all_product_fields(consolidated_data, pdp_url)

            if not extracted_fields:
                logger.error(f"❌ [JSON解析] 字段提取失败")
                return None

            # 创建Product对象
            product = self._create_product_from_fields(extracted_fields)

            if product:
                logger.info(f"✅ [JSON解析] 成功创建Product对象: {product.product_id}")
                self._log_extracted_fields_summary(extracted_fields)
            else:
                logger.error(f"❌ [JSON解析] Product对象创建失败")

            return product

        except Exception as e:
            logger.error(f"❌ [JSON解析] JSON解析过程异常: {str(e)}")
            import traceback
            logger.error(f"   - 异常堆栈: {traceback.format_exc()}")
            return None

    def _extract_distributed_product_data(self, data: Dict[str, Any], pdp_url: str) -> Optional[Dict[str, Any]]:
        """
        从分布式JSON结构中提取并整合商品数据
        扫描整个__MODERN_ROUTER_DATA__结构，收集分散的商品信息
        """
        try:
            logger.info(f"🔍 [分布式提取] 开始全面扫描JSON结构")

            # 创建商品数据收集器
            product_collector = ProductDataCollector()

            # 递归扫描整个JSON结构
            self._scan_json_structure_recursively(data, product_collector, "根级别")

            # 整合收集到的数据
            consolidated_data = product_collector.consolidate_data(pdp_url)

            if consolidated_data:
                logger.info(f"✅ [分布式提取] 成功整合商品数据")
                logger.info(f"📋 [分布式提取] 整合后数据包含字段: {list(consolidated_data.keys())}")
                return consolidated_data
            else:
                logger.warning(f"⚠️ [分布式提取] 未能整合有效的商品数据")
                return None

        except Exception as e:
            logger.error(f"❌ [分布式提取] 分布式数据提取异常: {str(e)}")
            return None

    def _scan_json_structure_recursively(self, obj: Any, collector: 'ProductDataCollector', path: str, depth: int = 0):
        """递归扫描JSON结构，收集商品相关数据"""
        try:
            if depth > 8:  # 限制递归深度
                return

            if isinstance(obj, dict):
                # 检查当前字典是否包含商品相关字段
                collector.collect_from_dict(obj, path)

                # 递归扫描子结构
                for key, value in obj.items():
                    if isinstance(value, (dict, list)):
                        self._scan_json_structure_recursively(value, collector, f"{path}.{key}", depth + 1)

            elif isinstance(obj, list):
                # 扫描列表中的每个元素
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        self._scan_json_structure_recursively(item, collector, f"{path}[{i}]", depth + 1)

        except Exception as e:
            logger.debug(f"⚠️ [递归扫描] 扫描异常: {str(e)}")

    def _extract_seller_info_from_html(self, html_content: str) -> Optional[Dict[str, str]]:
        """从HTML内容中提取seller信息"""
        try:
            logger.info(f"🔍 [HTML解析] 开始从HTML中提取seller信息")
            logger.debug(f"📄 [HTML解析] HTML内容长度: {len(html_content)} 字符")

            from bs4 import BeautifulSoup
            import json

            soup = BeautifulSoup(html_content, 'html.parser')
            logger.info(f"✅ [HTML解析] BeautifulSoup解析完成")

            # 查找__MODERN_ROUTER_DATA__脚本标签
            logger.info(f"🔍 [HTML解析] 查找__MODERN_ROUTER_DATA__脚本标签")
            router_script = soup.find('script', {'id': '__MODERN_ROUTER_DATA__'})

            if not router_script or not router_script.string:
                logger.warning(f"❌ [HTML解析] 未找到__MODERN_ROUTER_DATA__脚本标签")
                return self._fallback_seller_extraction(html_content)

            logger.info(f"✅ [HTML解析] 找到__MODERN_ROUTER_DATA__脚本标签")

            try:
                # 解析JSON数据
                router_data = json.loads(router_script.string)
                logger.info(f"✅ [HTML解析] 成功解析__MODERN_ROUTER_DATA__ JSON")

                # 按照路径导航: loaderData -> shop/pdp/(product_name_slug)/(product_id)/page -> page_config -> components_map -> component_data -> product_info -> shop_info -> shop_link
                loader_data = router_data.get('loaderData', {})
                logger.debug(f"📊 [HTML解析] loaderData存在: {bool(loader_data)}")

                if not loader_data:
                    logger.warning(f"❌ [HTML解析] loaderData为空")
                    return self._fallback_seller_extraction(html_content)

                # 查找PDP页面数据（键名包含shop/pdp/）
                pdp_page_key = None
                for key in loader_data.keys():
                    if 'shop' in key and 'pdp' in key and 'page' in key:
                        pdp_page_key = key
                        break

                if not pdp_page_key:
                    logger.warning(f"❌ [HTML解析] 未找到PDP页面数据键")
                    logger.debug(f"📊 [HTML解析] loaderData键列表: {list(loader_data.keys())}")
                    return self._fallback_seller_extraction(html_content)

                logger.info(f"✅ [HTML解析] 找到PDP页面数据键: {pdp_page_key}")

                # 导航到页面配置
                page_data = loader_data[pdp_page_key]
                page_config = page_data.get('page_config', {})
                components_map = page_config.get('components_map', [])

                logger.debug(f"📊 [HTML解析] page_config存在: {bool(page_config)}")
                logger.debug(f"📊 [HTML解析] components_map长度: {len(components_map) if isinstance(components_map, list) else 0}")

                # 在components_map中查找包含product_info的组件
                shop_link = None
                for component in components_map:
                    if isinstance(component, dict):
                        component_data = component.get('component_data', {})
                        product_info = component_data.get('product_info', {})
                        shop_info = product_info.get('shop_info', {})

                        if 'shop_link' in shop_info:
                            shop_link = shop_info['shop_link']
                            logger.info(f"✅ [HTML解析] 找到shop_link: {shop_link}")
                            break

                if not shop_link:
                    logger.warning(f"❌ [HTML解析] 未找到shop_link")
                    return self._fallback_seller_extraction(html_content)

                # 转换Unicode转义字符
                import codecs
                decoded_shop_link = codecs.decode(shop_link, 'unicode_escape')
                logger.info(f"🔄 [HTML解析] 解码shop_link: {decoded_shop_link}")

                # 从shop_link中提取seller_id
                # 格式: https://www.tiktok.com/shop/store/shop-name/seller_id
                import re
                seller_id_match = re.search(r'/shop/store/[^/]+/(\d+)', decoded_shop_link)

                if seller_id_match:
                    seller_id = seller_id_match.group(1)

                    # 提取shop_name
                    shop_name_match = re.search(r'/shop/store/([^/]+)/', decoded_shop_link)
                    shop_name = shop_name_match.group(1) if shop_name_match else ''

                    result = {
                        'seller_id': seller_id,
                        'shop_name': shop_name,
                        'shop_id': seller_id,  # 通常seller_id和shop_id相同
                        'shop_link': decoded_shop_link
                    }

                    logger.info(f"🎉 [HTML解析] 成功提取seller信息:")
                    logger.info(f"   - seller_id: {result['seller_id']}")
                    logger.info(f"   - shop_name: {result['shop_name']}")
                    logger.info(f"   - shop_link: {result['shop_link']}")

                    return result
                else:
                    logger.warning(f"❌ [HTML解析] 无法从shop_link中提取seller_id: {decoded_shop_link}")
                    return self._fallback_seller_extraction(html_content)

            except json.JSONDecodeError as e:
                logger.error(f"💥 [HTML解析] JSON解析失败: {str(e)}")
                return self._fallback_seller_extraction(html_content)
            except Exception as e:
                logger.error(f"💥 [HTML解析] 数据导航失败: {str(e)}")
                return self._fallback_seller_extraction(html_content)

        except Exception as e:
            logger.error(f"💥 [HTML解析] seller信息提取异常: {str(e)}")
            return self._fallback_seller_extraction(html_content)

    def _fallback_seller_extraction(self, html_content: str) -> Optional[Dict[str, str]]:
        """备用seller信息提取方法 - 多策略提取"""
        try:
            logger.info(f"🔄 [备用提取] 使用备用方法提取seller信息")
            import re
            import json

            # 策略1: 在HTML中搜索shop_link模式
            logger.info(f"🔍 [备用提取] 策略1: 搜索shop_link模式")
            shop_link_patterns = [
                r'"shop_link"[:\s]*"([^"]*shop/store/[^"]*)"',
                r'shop_link["\']?\s*[:=]\s*["\']([^"\']*shop/store/[^"\']*)["\']',
                r'https:\\u002F\\u002Fwww\.tiktok\.com\\u002Fshop\\u002Fstore\\u002F[^\\]+\\u002F\d+',
                r'https://www\.tiktok\.com/shop/store/[^/]+/\d+',
            ]

            for i, pattern in enumerate(shop_link_patterns, 1):
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    shop_link = matches[0]
                    logger.info(f"✅ [备用提取] 策略1.{i}找到shop_link: {shop_link}")

                    # 解码Unicode转义字符
                    import codecs
                    try:
                        decoded_shop_link = codecs.decode(shop_link, 'unicode_escape')
                        logger.info(f"🔄 [备用提取] 解码shop_link: {decoded_shop_link}")

                        # 提取seller_id和shop_name
                        result = self._extract_seller_info_from_link(decoded_shop_link)
                        if result:
                            logger.info(f"🎉 [备用提取] 策略1成功提取seller信息")
                            return result
                    except Exception as decode_error:
                        logger.warning(f"⚠️ [备用提取] 解码shop_link失败: {str(decode_error)}")
                        continue

            # 策略2: 搜索seller_id模式
            logger.info(f"🔍 [备用提取] 策略2: 搜索seller_id模式")
            seller_patterns = [
                r'"seller_id"[:\s]*"?(\d+)"?',
                r'seller_id["\']?\s*[:=]\s*["\']?(\d+)',
                r'sellerId["\']?\s*[:=]\s*["\']?(\d+)',
                r'"shop_id"[:\s]*"?(\d+)"?',
                r'shop_id["\']?\s*[:=]\s*["\']?(\d+)',
            ]

            for i, pattern in enumerate(seller_patterns, 1):
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    seller_id = matches[0]
                    logger.info(f"✅ [备用提取] 策略2.{i}找到seller_id: {seller_id}")

                    # 尝试提取shop_name
                    shop_name = self._extract_shop_name_from_html(html_content)

                    result = {
                        'seller_id': seller_id,
                        'shop_name': shop_name or '',
                        'shop_id': seller_id
                    }

                    logger.info(f"🎉 [备用提取] 策略2成功提取seller信息")
                    return result

            # 策略3: 从JSON脚本中搜索
            logger.info(f"🔍 [备用提取] 策略3: 从JSON脚本中搜索")
            result = self._extract_seller_from_json_scripts(html_content)
            if result:
                logger.info(f"🎉 [备用提取] 策略3成功提取seller信息")
                return result

            # 策略4: 从URL路径中提取（如果可用）
            logger.info(f"🔍 [备用提取] 策略4: 从URL路径中提取")
            result = self._extract_seller_from_url_context(html_content)
            if result:
                logger.info(f"🎉 [备用提取] 策略4成功提取seller信息")
                return result

            logger.warning(f"❌ [备用提取] 所有策略都失败")
            return None

        except Exception as e:
            logger.error(f"💥 [备用提取] 备用提取异常: {str(e)}")
            return None

    def _extract_seller_info_from_link(self, shop_link: str) -> Optional[Dict[str, str]]:
        """从shop_link中提取seller信息"""
        try:
            import re

            # 从shop_link中提取seller_id
            # 格式: https://www.tiktok.com/shop/store/shop-name/seller_id
            seller_id_match = re.search(r'/shop/store/[^/]+/(\d+)', shop_link)

            if seller_id_match:
                seller_id = seller_id_match.group(1)

                # 提取shop_name
                shop_name_match = re.search(r'/shop/store/([^/]+)/', shop_link)
                shop_name = shop_name_match.group(1) if shop_name_match else ''

                result = {
                    'seller_id': seller_id,
                    'shop_name': shop_name,
                    'shop_id': seller_id,  # 通常seller_id和shop_id相同
                    'shop_link': shop_link
                }

                logger.info(f"✅ [链接解析] 成功从shop_link提取seller信息")
                logger.info(f"   - seller_id: {result['seller_id']}")
                logger.info(f"   - shop_name: {result['shop_name']}")

                return result
            else:
                logger.warning(f"❌ [链接解析] 无法从shop_link中提取seller_id: {shop_link}")
                return None

        except Exception as e:
            logger.error(f"💥 [链接解析] 解析异常: {str(e)}")
            return None

    def _extract_shop_name_from_html(self, html_content: str) -> Optional[str]:
        """从HTML中提取shop_name"""
        try:
            import re

            # 多种shop_name模式
            shop_name_patterns = [
                r'"shop_name"[:\s]*"([^"]+)"',
                r'shop_name["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'"shopName"[:\s]*"([^"]+)"',
                r'shopName["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'"seller_name"[:\s]*"([^"]+)"',
                r'seller_name["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'"store_name"[:\s]*"([^"]+)"',
                r'store_name["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ]

            for pattern in shop_name_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    shop_name = matches[0].strip()
                    if shop_name:
                        logger.debug(f"✅ [店铺名提取] 找到shop_name: {shop_name}")
                        return shop_name

            logger.debug(f"⚠️ [店铺名提取] 未找到shop_name")
            return None

        except Exception as e:
            logger.debug(f"💥 [店铺名提取] 提取异常: {str(e)}")
            return None

    def _extract_seller_from_json_scripts(self, html_content: str) -> Optional[Dict[str, str]]:
        """从JSON脚本中提取seller信息"""
        try:
            from bs4 import BeautifulSoup
            import json
            import re

            soup = BeautifulSoup(html_content, 'html.parser')
            script_tags = soup.find_all('script')

            for script in script_tags:
                if script.string:
                    script_content = script.string.strip()

                    # 查找包含seller相关信息的JSON
                    if any(keyword in script_content.lower() for keyword in ['seller', 'shop', 'store']):
                        try:
                            # 尝试解析JSON
                            json_match = re.search(r'\{.*\}', script_content, re.DOTALL)
                            if json_match:
                                json_str = json_match.group()
                                data = json.loads(json_str)

                                # 递归搜索seller信息
                                result = self._search_seller_in_json(data)
                                if result:
                                    return result

                        except json.JSONDecodeError:
                            continue

            return None

        except Exception as e:
            logger.debug(f"💥 [JSON脚本提取] 提取异常: {str(e)}")
            return None

    def _search_seller_in_json(self, data: Any, depth: int = 0) -> Optional[Dict[str, str]]:
        """在JSON数据中递归搜索seller信息"""
        try:
            if depth > 5:  # 限制递归深度
                return None

            if isinstance(data, dict):
                # 检查当前层级是否包含seller信息
                seller_id = None
                shop_name = None

                # 查找seller_id
                for key in ['seller_id', 'sellerId', 'shop_id', 'shopId']:
                    if key in data and data[key]:
                        seller_id = str(data[key])
                        break

                # 查找shop_name
                for key in ['shop_name', 'shopName', 'seller_name', 'sellerName', 'store_name', 'storeName']:
                    if key in data and data[key]:
                        shop_name = str(data[key])
                        break

                if seller_id:
                    result = {
                        'seller_id': seller_id,
                        'shop_name': shop_name or '',
                        'shop_id': seller_id
                    }
                    logger.debug(f"✅ [JSON搜索] 找到seller信息: seller_id={seller_id}, shop_name={shop_name}")
                    return result

                # 递归搜索子结构
                for value in data.values():
                    if isinstance(value, (dict, list)):
                        result = self._search_seller_in_json(value, depth + 1)
                        if result:
                            return result

            elif isinstance(data, list):
                for item in data:
                    if isinstance(item, (dict, list)):
                        result = self._search_seller_in_json(item, depth + 1)
                        if result:
                            return result

            return None

        except Exception as e:
            logger.debug(f"💥 [JSON搜索] 搜索异常: {str(e)}")
            return None

    def _extract_seller_from_url_context(self, html_content: str) -> Optional[Dict[str, str]]:
        """从URL上下文中提取seller信息"""
        try:
            import re

            # 从HTML中查找当前页面URL或相关链接
            url_patterns = [
                r'https://www\.tiktok\.com/shop/pdp/[^/]+/(\d+)',
                r'"url"[:\s]*"([^"]*shop/pdp/[^"]*)"',
                r'canonical["\']?\s*href["\']?\s*[:=]\s*["\']([^"\']*shop/pdp/[^"\']*)["\']',
            ]

            for pattern in url_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    if pattern.endswith(r'(\d+)'):
                        # 直接匹配到product_id
                        product_id = matches[0]
                        logger.debug(f"✅ [URL上下文] 找到product_id: {product_id}")

                        # 可以基于product_id构造一个基本的seller信息
                        # 这里可以实现更复杂的逻辑，比如从product_id推导seller_id
                        return None  # 暂时返回None，因为仅有product_id不足以确定seller_id
                    else:
                        # 匹配到完整URL
                        url = matches[0]
                        logger.debug(f"✅ [URL上下文] 找到相关URL: {url}")

                        # 从URL中提取信息
                        if '/shop/store/' in url:
                            return self._extract_seller_info_from_link(url)

            return None

        except Exception as e:
            logger.debug(f"💥 [URL上下文] 提取异常: {str(e)}")
            return None

    def _extract_all_product_fields(self, product_data: Dict[str, Any], pdp_url: str) -> Optional[Dict[str, Any]]:
        """提取所有必需的商品字段"""
        try:
            logger.info(f"📋 [字段提取] 开始提取所有商品字段")

            fields = {}

            # 基本信息字段
            fields.update(self._extract_basic_fields(product_data, pdp_url))

            # 价格信息字段
            fields.update(self._extract_price_fields(product_data))

            # 图片信息字段
            fields.update(self._extract_image_fields(product_data))

            # 运费信息字段
            fields.update(self._extract_shipping_fields(product_data))

            # 评价信息字段
            fields.update(self._extract_review_fields(product_data))

            # 验证必需字段
            if not self._validate_required_fields(fields):
                logger.error(f"❌ [字段提取] 必需字段验证失败")
                return None

            logger.info(f"✅ [字段提取] 所有字段提取完成")
            return fields

        except Exception as e:
            logger.error(f"❌ [字段提取] 字段提取异常: {str(e)}")
            return None

    def _extract_basic_fields(self, product_data: Dict[str, Any], pdp_url: str) -> Dict[str, Any]:
        """提取基本信息字段"""
        try:
            logger.debug(f"📋 [基本字段] 开始提取基本信息字段")

            fields = {}

            # route_product_id (必需)
            fields['route_product_id'] = product_data.get('route_product_id', '')

            # request_url
            fields['request_url'] = product_data.get('request_url', pdp_url)

            # shop_name
            fields['shop_name'] = product_data.get('shop_name', '')

            # title
            fields['title'] = product_data.get('title', '')

            # description
            fields['description'] = product_data.get('description', '')

            # on_sell_product_count
            fields['on_sell_product_count'] = product_data.get('on_sell_product_count', 0)

            logger.debug(f"✅ [基本字段] 基本信息字段提取完成")
            return fields

        except Exception as e:
            logger.debug(f"❌ [基本字段] 基本字段提取异常: {str(e)}")
            return {}

    def _extract_price_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取价格信息字段"""
        try:
            logger.debug(f"📋 [价格字段] 开始提取价格信息字段")

            fields = {}

            # 处理price字段
            price_data = product_data.get('price', {})
            if isinstance(price_data, dict):
                fields['price'] = price_data
            else:
                fields['price'] = {}

            # 处理product_base字段
            product_base = product_data.get('product_base', {})
            if isinstance(product_base, dict):
                fields['product_base'] = product_base

                # 从product_base中提取价格信息
                if 'price' in product_base:
                    base_price = product_base['price']
                    if isinstance(base_price, dict):
                        fields['real_price'] = base_price.get('real_price', '')
                        fields['original_price'] = base_price.get('original_price', '')
                        fields['currency'] = base_price.get('currency', 'USD')
            else:
                fields['product_base'] = {}

            # 直接提取的价格字段
            fields['real_price'] = product_data.get('real_price', fields.get('real_price', ''))
            fields['original_price'] = product_data.get('original_price', fields.get('original_price', ''))
            fields['currency'] = product_data.get('currency', fields.get('currency', 'USD'))

            logger.debug(f"✅ [价格字段] 价格信息字段提取完成")
            return fields

        except Exception as e:
            logger.debug(f"❌ [价格字段] 价格字段提取异常: {str(e)}")
            return {}

    def _extract_image_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取图片信息字段"""
        try:
            logger.debug(f"📋 [图片字段] 开始提取图片信息字段")

            fields = {}

            # 处理images字段
            images_data = product_data.get('images', [])
            if isinstance(images_data, list):
                fields['images'] = images_data
            else:
                fields['images'] = []

            logger.debug(f"✅ [图片字段] 图片信息字段提取完成，图片数量: {len(fields['images'])}")
            return fields

        except Exception as e:
            logger.debug(f"❌ [图片字段] 图片字段提取异常: {str(e)}")
            return {'images': []}

    def _extract_shipping_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取运费信息字段"""
        try:
            logger.debug(f"📋 [运费字段] 开始提取运费信息字段")

            fields = {}

            # 处理shipping_fee字段
            shipping_fee = product_data.get('shipping_fee', {})
            if isinstance(shipping_fee, dict):
                fields['shipping_fee'] = shipping_fee
            else:
                fields['shipping_fee'] = {}

            logger.debug(f"✅ [运费字段] 运费信息字段提取完成")
            return fields

        except Exception as e:
            logger.debug(f"❌ [运费字段] 运费字段提取异常: {str(e)}")
            return {'shipping_fee': {}}

    def _extract_review_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取评价信息字段"""
        try:
            logger.debug(f"📋 [评价字段] 开始提取评价信息字段")

            fields = {}

            # 处理product_detail_review字段
            review_data = product_data.get('product_detail_review', {})
            if isinstance(review_data, dict):
                fields['product_detail_review'] = review_data
            else:
                fields['product_detail_review'] = {}

            # 处理sold_count字段
            fields['sold_count'] = product_data.get('sold_count', 0)

            logger.debug(f"✅ [评价字段] 评价信息字段提取完成")
            return fields

        except Exception as e:
            logger.debug(f"❌ [评价字段] 评价字段提取异常: {str(e)}")
            return {'product_detail_review': {}, 'sold_count': 0}

    def _validate_required_fields(self, fields: Dict[str, Any]) -> bool:
        """验证必需字段"""
        try:
            logger.debug(f"📋 [字段验证] 开始验证必需字段")

            # 检查route_product_id
            if not fields.get('route_product_id'):
                logger.error(f"❌ [字段验证] 缺少必需字段: route_product_id")
                return False

            logger.debug(f"✅ [字段验证] 必需字段验证通过")
            return True

        except Exception as e:
            logger.debug(f"❌ [字段验证] 字段验证异常: {str(e)}")
            return False

    def _create_product_from_fields(self, fields: Dict[str, Any]) -> Optional[Product]:
        """从提取的字段创建Product对象"""
        try:
            logger.debug(f"📋 [Product创建] 开始创建Product对象")

            from src.models.product import Product, ProductImage, ProductRating, Currency, ProductStatus
            from decimal import Decimal
            import re

            # 提取基本信息
            product_id = str(fields.get('route_product_id', ''))
            title = str(fields.get('title', ''))
            description = str(fields.get('description', ''))
            shop_name = str(fields.get('shop_name', ''))
            request_url = str(fields.get('request_url', ''))

            # 处理价格信息
            real_price_str = str(fields.get('real_price', '0'))
            original_price_str = str(fields.get('original_price', '0'))
            currency_str = str(fields.get('currency', 'USD'))

            # 解析价格
            price = self._parse_price(real_price_str)
            original_price = self._parse_price(original_price_str) if original_price_str != '0' else None

            # 解析货币
            currency = self._parse_currency(currency_str)

            # 处理图片信息
            images_data = fields.get('images', [])
            images = self._parse_images(images_data)

            # 处理评价信息
            review_data = fields.get('product_detail_review', {})
            rating = self._parse_rating(review_data)

            # 处理销量
            sold_count = int(fields.get('sold_count', 0))

            # 处理运费信息
            shipping_fee = fields.get('shipping_fee', {})

            # 创建metadata
            metadata = {
                'shipping_fee': shipping_fee,
                'shop_product_count': fields.get('on_sell_product_count', 0),
                'product_base': fields.get('product_base', {}),
                'price_data': fields.get('price', {}),
            }

            # 计算折扣
            discount_amount = 0
            if original_price and original_price > price:
                discount_amount = float(original_price - price)
            metadata['discount_amount'] = discount_amount

            # 创建Product对象
            product = Product(
                product_id=product_id,
                title=title or f"Product {product_id}",
                description=description,
                category='Unknown',  # 可以后续从其他字段推导
                price=price,
                original_price=original_price,
                currency=currency,
                images=images,
                product_url=request_url,
                shop_name=shop_name,
                rating=rating,
                status=ProductStatus.ACTIVE,
                sold_count=sold_count,
                metadata=metadata
            )

            logger.debug(f"✅ [Product创建] Product对象创建成功: {product_id}")
            return product

        except Exception as e:
            logger.error(f"❌ [Product创建] Product对象创建异常: {str(e)}")
            return None

    def _parse_price(self, price_str: str) -> Decimal:
        """解析价格字符串（支持价格区间，取第一个值）"""
        try:
            if not price_str:
                return Decimal('0')

            import re
            price_str = str(price_str).strip()

            # 处理价格区间情况，如 "$18.86 - 28.54" 或 "18.86-28.54"
            # 匹配第一个价格数字（支持小数点）
            price_match = re.search(r'[\d.]+', price_str)
            if price_match:
                first_price = price_match.group()
                logger.debug(f"💰 [价格解析] 从'{price_str}'中提取第一个价格: {first_price}")
                return Decimal(first_price)
            else:
                logger.debug(f"⚠️ [价格解析] 无法从'{price_str}'中提取价格数字")
                return Decimal('0')

        except Exception as e:
            logger.debug(f"⚠️ [价格解析] 价格解析失败: {price_str}, 错误: {str(e)}")
            return Decimal('0')

    def _parse_currency(self, currency_str: str) -> Currency:
        """解析货币"""
        try:
            from src.models.product import Currency

            currency_map = {
                'USD': Currency.USD,
                'EUR': Currency.EUR,
                'GBP': Currency.GBP,
                'CNY': Currency.CNY,
                'JPY': Currency.JPY,
            }

            currency_upper = str(currency_str).upper()
            return currency_map.get(currency_upper, Currency.USD)

        except Exception as e:
            logger.debug(f"⚠️ [货币解析] 货币解析失败: {currency_str}, 错误: {str(e)}")
            from src.models.product import Currency
            return Currency.USD

    def _parse_images(self, images_data: list) -> list:
        """解析图片信息"""
        try:
            from src.models.product import ProductImage

            images = []
            for i, img_data in enumerate(images_data):
                if isinstance(img_data, dict):
                    # 处理TikTok的图片数据结构
                    url_list = img_data.get('url_list', [])
                    if url_list and len(url_list) > 0:
                        url = url_list[0]
                        height = img_data.get('height', 800)
                        width = img_data.get('width', 800)

                        image = ProductImage(
                            url=url,
                            alt_text=f"Product image {i+1}",
                            width=width,
                            height=height
                        )
                        images.append(image)

            return images

        except Exception as e:
            logger.debug(f"⚠️ [图片解析] 图片解析失败: {str(e)}")
            return []

    def _parse_rating(self, review_data: dict) -> ProductRating:
        """解析评价信息"""
        try:
            from src.models.product import ProductRating

            if isinstance(review_data, dict):
                average_rating = float(review_data.get('product_rating', 0))
                total_reviews = int(review_data.get('review_count', 0))
            else:
                average_rating = 0.0
                total_reviews = 0

            return ProductRating(
                average_rating=average_rating,
                total_reviews=total_reviews
            )

        except Exception as e:
            logger.debug(f"⚠️ [评价解析] 评价解析失败: {str(e)}")
            from src.models.product import ProductRating
            return ProductRating(average_rating=0.0, total_reviews=0)

    def _log_extracted_fields_summary(self, fields: Dict[str, Any]):
        """记录提取字段的摘要"""
        try:
            logger.info(f"📋 [字段摘要] 提取字段摘要:")
            logger.info(f"   - route_product_id: {fields.get('route_product_id', 'N/A')}")
            logger.info(f"   - title: {fields.get('title', 'N/A')}")
            logger.info(f"   - shop_name: {fields.get('shop_name', 'N/A')}")
            logger.info(f"   - real_price: {fields.get('real_price', 'N/A')}")
            logger.info(f"   - currency: {fields.get('currency', 'N/A')}")
            logger.info(f"   - sold_count: {fields.get('sold_count', 'N/A')}")
            logger.info(f"   - images_count: {len(fields.get('images', []))}")

        except Exception as e:
            logger.debug(f"⚠️ [字段摘要] 记录摘要失败: {str(e)}")


class ProductDataCollector:
    """商品数据收集器，用于从分布式JSON结构中收集和整合商品信息"""

    def __init__(self):
        self.collected_data = {}
        self.field_locations = {}  # 记录字段发现的位置
        self.route_product_id = None

        # 定义需要收集的字段映射
        self.field_mappings = {
            # 基本信息字段
            'route_product_id': ['route_product_id', 'product_id', 'productId'],
            'request_url': ['request_url', 'url', 'product_url'],
            'shop_name': ['shop_name', 'shopName', 'seller_name', 'sellerName', 'store_name'],
            'on_sell_product_count': ['on_sell_product_count', 'onSellProductCount', 'product_count'],

            # 价格相关字段
            'price': ['price', 'pricing', 'priceInfo', 'price_info'],
            'product_base': ['product_base', 'productBase'],
            'real_price': ['real_price', 'current_price', 'sale_price'],
            'original_price': ['original_price', 'list_price', 'msrp'],
            'currency': ['currency', 'currencyCode', 'currency_code'],

            # 商品详情字段
            'images': ['images', 'imageList', 'product_images', 'image_list'],
            'sold_count': ['sold_count', 'soldCount', 'sales_count', 'sales'],
            'shipping_fee': ['shipping_fee', 'shippingFee', 'shipping_info'],
            'product_detail_review': ['product_detail_review', 'reviews', 'rating', 'review_info'],

            # 其他字段
            'title': ['title', 'name', 'product_name', 'productName'],
            'description': ['description', 'desc', 'product_desc'],
        }

    def collect_from_dict(self, data_dict: Dict[str, Any], path: str):
        """从字典中收集商品相关字段"""
        try:
            for target_field, possible_names in self.field_mappings.items():
                for field_name in possible_names:
                    if field_name in data_dict and data_dict[field_name] is not None:
                        # 记录字段值和发现位置
                        if target_field not in self.collected_data:
                            self.collected_data[target_field] = data_dict[field_name]
                            self.field_locations[target_field] = f"{path}.{field_name}"
                            logger.debug(f"✅ [数据收集] 在{path}.{field_name}找到{target_field}")

                            # 特别处理route_product_id
                            if target_field == 'route_product_id':
                                self.route_product_id = str(data_dict[field_name])

        except Exception as e:
            logger.debug(f"⚠️ [数据收集] 收集异常: {str(e)}")

    def consolidate_data(self, pdp_url: str) -> Optional[Dict[str, Any]]:
        """整合收集到的数据"""
        try:
            if not self.route_product_id:
                logger.warning(f"⚠️ [数据整合] 未找到route_product_id，无法整合数据")
                return None

            # 创建整合后的数据结构
            consolidated = {
                'route_product_id': self.route_product_id,
                'request_url': self.collected_data.get('request_url', pdp_url)
            }

            # 添加所有收集到的字段
            for field, value in self.collected_data.items():
                if field not in consolidated:
                    consolidated[field] = value

            # 记录数据来源
            logger.info(f"📋 [数据整合] 整合了 {len(self.collected_data)} 个字段")
            for field, location in self.field_locations.items():
                logger.debug(f"   - {field}: 来自 {location}")

            return consolidated

        except Exception as e:
            logger.error(f"❌ [数据整合] 整合异常: {str(e)}")
            return None

    def _find_product_data_in_json(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """在JSON数据中查找包含商品信息的数据结构"""
        try:
            logger.debug(f"🔍 [数据查找] 开始在JSON中查找商品数据")

            # 首先尝试查找完整的商品数据
            complete_data = self._find_complete_product_data_recursive(data, "根级别")
            if complete_data:
                return complete_data

            # 如果没有找到完整数据，尝试查找包含route_product_id的数据并尝试合并
            route_data = self._find_route_data_and_merge(data)
            if route_data:
                return route_data

            logger.warning(f"⚠️ [数据查找] 未找到商品数据")
            return None

        except Exception as e:
            logger.error(f"❌ [数据查找] 查找商品数据异常: {str(e)}")
            return None

    def _find_complete_product_data_recursive(self, obj: Any, path_name: str, depth: int = 0) -> Optional[Dict[str, Any]]:
        """递归查找完整的商品数据"""
        try:
            if depth > 6:  # 增加递归深度
                return None

            if isinstance(obj, dict):
                # 检查是否包含完整的商品数据
                if self._is_complete_product_data(obj):
                    logger.info(f"✅ [完整数据查找] 在{path_name}找到完整商品数据 (深度: {depth})")
                    self._log_found_product_data_structure(obj, path_name)
                    return obj

                # 递归查找子结构
                for key, value in obj.items():
                    if isinstance(value, (dict, list)):
                        result = self._find_complete_product_data_recursive(value, f"{path_name}.{key}", depth + 1)
                        if result:
                            return result

            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        result = self._find_complete_product_data_recursive(item, f"{path_name}[{i}]", depth + 1)
                        if result:
                            return result

            return None

        except Exception as e:
            logger.debug(f"⚠️ [完整数据查找] 递归查找异常: {str(e)}")
            return None

    def _find_route_data_and_merge(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """查找路由数据并尝试合并其他商品信息"""
        try:
            logger.info(f"🔍 [路由数据查找] 开始查找路由数据并尝试合并")

            # 查找包含route_product_id的路由数据
            route_data = self._recursive_find_product_data(data, "根级别")
            if not route_data:
                return None

            logger.info(f"✅ [路由数据查找] 找到路由数据，尝试在同级查找其他商品信息")

            # 尝试在loaderData中查找其他商品相关数据
            if 'loaderData' in data:
                merged_data = self._merge_product_data_from_loader(route_data, data['loaderData'])
                if merged_data:
                    return merged_data

            # 如果无法合并，返回路由数据（至少包含product_id）
            logger.warning(f"⚠️ [路由数据查找] 无法找到额外商品数据，返回路由数据")
            return route_data

        except Exception as e:
            logger.error(f"❌ [路由数据查找] 查找异常: {str(e)}")
            return None

    def _merge_product_data_from_loader(self, route_data: Dict[str, Any], loader_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从loaderData中合并商品数据"""
        try:
            logger.info(f"🔍 [数据合并] 开始从loaderData合并商品数据")

            # 创建合并后的数据字典
            merged_data = route_data.copy()

            # 在loaderData中查找商品相关数据
            product_data_found = False

            for key, value in loader_data.items():
                if isinstance(value, dict):
                    logger.debug(f"🔍 [数据合并] 检查loaderData.{key}")

                    # 直接查找product_data字段
                    if 'product_data' in value and isinstance(value['product_data'], dict):
                        logger.info(f"✅ [数据合并] 在loaderData.{key}.product_data中找到商品数据")
                        merged_data.update(value['product_data'])
                        product_data_found = True
                        break

                    # 查找包含商品数据的结构
                    product_info = self._extract_product_info_from_loader_item(value)
                    if product_info:
                        logger.info(f"✅ [数据合并] 在loaderData.{key}中找到商品信息")
                        merged_data.update(product_info)
                        product_data_found = True

            if product_data_found:
                logger.info(f"✅ [数据合并] 成功合并商品数据")
                logger.info(f"📋 [数据合并] 合并后数据键: {list(merged_data.keys())}")
                self._log_found_product_data_structure(merged_data, "合并后的数据")
                return merged_data
            else:
                logger.warning(f"⚠️ [数据合并] 未找到可合并的商品数据")
                return None

        except Exception as e:
            logger.error(f"❌ [数据合并] 合并异常: {str(e)}")
            return None

    def _extract_product_info_from_loader_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从loaderData的单个项目中提取商品信息"""
        try:
            product_info = {}

            # 查找各种可能的商品数据字段
            fields_to_extract = {
                'shop_name': ['shop_name', 'shopName', 'seller_name'],
                'price': ['price', 'pricing', 'priceInfo'],
                'product_base': ['product_base', 'productBase'],
                'images': ['images', 'imageList', 'product_images'],
                'sold_count': ['sold_count', 'soldCount', 'sales'],
                'shipping_fee': ['shipping_fee', 'shippingFee'],
                'product_detail_review': ['product_detail_review', 'reviews', 'rating'],
                'on_sell_product_count': ['on_sell_product_count', 'productCount']
            }

            # 直接查找字段
            for target_field, possible_names in fields_to_extract.items():
                for name in possible_names:
                    if name in item and item[name] is not None:
                        product_info[target_field] = item[name]
                        logger.debug(f"✅ [字段提取] 找到{target_field}: {name}")
                        break

            # 递归查找嵌套字段
            if not product_info:
                for key, value in item.items():
                    if isinstance(value, dict):
                        nested_info = self._extract_product_info_from_loader_item(value)
                        if nested_info:
                            product_info.update(nested_info)

            return product_info if product_info else None

        except Exception as e:
            logger.debug(f"⚠️ [字段提取] 提取异常: {str(e)}")
            return None

    def _recursive_find_product_data(self, obj: Any, path_name: str, depth: int = 0) -> Optional[Dict[str, Any]]:
        """递归查找包含商品数据的结构"""
        try:
            if depth > 5:  # 限制递归深度
                return None

            if isinstance(obj, dict):
                # 检查是否包含完整的商品数据（不仅仅是route_product_id）
                if self._is_complete_product_data(obj):
                    logger.info(f"✅ [数据查找] 在{path_name}的深层结构中找到完整商品数据 (深度: {depth})")
                    self._log_found_product_data_structure(obj, path_name)
                    return obj

                # 如果只有route_product_id但缺少其他关键字段，继续在同级查找
                if 'route_product_id' in obj:
                    logger.info(f"🔍 [数据查找] 在{path_name}找到route_product_id，但缺少其他商品数据，继续查找...")
                    self._log_found_product_data_structure(obj, path_name)

                    # 在父级或同级查找完整的商品数据
                    parent_result = self._find_complete_product_data_in_parent(obj, path_name)
                    if parent_result:
                        return parent_result

                for key, value in obj.items():
                    if isinstance(value, (dict, list)):
                        result = self._recursive_find_product_data(value, f"{path_name}.{key}", depth + 1)
                        if result:
                            return result

            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        result = self._recursive_find_product_data(item, f"{path_name}[{i}]", depth + 1)
                        if result:
                            return result

            return None

        except Exception as e:
            logger.debug(f"⚠️ [数据查找] 递归查找异常: {str(e)}")
            return None

    def _is_complete_product_data(self, data: Dict[str, Any]) -> bool:
        """检查数据是否包含完整的商品信息"""
        try:
            # 必须包含route_product_id
            if 'route_product_id' not in data:
                return False

            # 检查是否包含关键的商品数据字段
            key_fields = ['shop_name', 'price', 'product_base', 'images', 'sold_count']
            found_fields = sum(1 for field in key_fields if field in data)

            # 如果包含至少2个关键字段，认为是完整数据
            if found_fields >= 2:
                logger.debug(f"✅ [完整性检查] 找到 {found_fields} 个关键字段，认为是完整商品数据")
                return True

            logger.debug(f"⚠️ [完整性检查] 只找到 {found_fields} 个关键字段，可能只是路由信息")
            return False

        except Exception as e:
            logger.debug(f"⚠️ [完整性检查] 检查异常: {str(e)}")
            return False

    def _find_complete_product_data_in_parent(self, route_data: Dict[str, Any], path_name: str) -> Optional[Dict[str, Any]]:
        """在父级或同级查找完整的商品数据"""
        try:
            logger.info(f"🔍 [父级查找] 在路由数据的父级查找完整商品数据")

            # 从路径中提取可能的父级路径
            path_parts = path_name.split('.')
            if len(path_parts) < 3:
                return None

            # 尝试在同级的其他字段中查找商品数据
            # 例如：如果在 loaderData.shop/pdp/.../page.route_info 找到路由信息
            # 那么商品数据可能在 loaderData.shop/pdp/.../page.product_data 或类似路径

            # 这里需要访问原始数据结构，暂时返回None
            # 在实际实现中，我们需要修改查找策略
            logger.warning(f"⚠️ [父级查找] 暂时无法在父级查找，需要改进查找策略")
            return None

        except Exception as e:
            logger.debug(f"⚠️ [父级查找] 查找异常: {str(e)}")
            return None

    async def _parse_shop_html_product_data(self, shop_html: str, original_url: str, filter_conditions: Dict[str, Any] = None) -> Optional[Product]:
        """从店铺HTML页面解析商品数据"""
        try:
            logger.info(f"🔍 [店铺HTML解析] 开始解析店铺HTML页面商品数据")

            # 这里可以实现店铺HTML页面的商品数据解析逻辑
            # 暂时返回None，表示未实现
            logger.warning(f"⚠️ [店铺HTML解析] 店铺HTML解析功能暂未实现")
            return None

        except Exception as e:
            logger.error(f"❌ [店铺HTML解析] 解析异常: {str(e)}")
            return None

    async def _parse_shop_html_product_data(self, shop_html: str, original_url: str, filter_conditions: Dict[str, Any] = None) -> Optional[Product]:
        """提取基本信息字段"""
        try:
            fields = {}

            logger.info(f"🔍 [基本字段] 开始提取基本字段")
            logger.debug(f"📋 [基本字段] 输入数据键: {list(product_data.keys())}")

            # route_product_id -> product_id
            product_id = str(product_data.get('route_product_id', ''))
            if not product_id:
                logger.warning(f"⚠️ [基本字段] route_product_id为空")
            else:
                logger.info(f"✅ [基本字段] 找到product_id: {product_id}")
            fields['product_id'] = product_id

            # request_url -> product_url (处理Unicode转义)
            request_url = product_data.get('request_url', pdp_url)
            if request_url:
                # 解码Unicode转义字符
                request_url = self._decode_unicode_escapes(request_url)
                logger.info(f"✅ [基本字段] 找到request_url: {request_url[:100]}...")
            else:
                logger.warning(f"⚠️ [基本字段] request_url为空，使用pdp_url")
            fields['product_url'] = request_url

            # shop_name - 可能在不同的路径中
            shop_name = self._extract_shop_name_from_data(product_data)
            fields['shop_name'] = shop_name
            logger.info(f"{'✅' if shop_name else '⚠️'} [基本字段] shop_name: '{shop_name}'")

            # on_sell_product_count - 可能在不同的路径中
            on_sell_product_count = self._extract_shop_product_count_from_data(product_data)
            fields['on_sell_product_count'] = on_sell_product_count
            logger.info(f"{'✅' if on_sell_product_count > 0 else '⚠️'} [基本字段] on_sell_product_count: {on_sell_product_count}")

            # sold_count - 可能在不同的路径中
            sold_count = self._extract_sold_count_from_data(product_data)
            fields['sold_count'] = sold_count
            logger.info(f"{'✅' if sold_count > 0 else '⚠️'} [基本字段] sold_count: {sold_count}")

            # 生成标题（从URL中提取）
            title = self._extract_title_from_url(request_url, product_id)
            fields['title'] = title

            logger.info(f"📦 [基本字段] 提取完成 - ID={product_id}, 店铺={shop_name}, 销量={sold_count}")
            return fields

        except Exception as e:
            logger.error(f"❌ [基本字段] 提取基本字段异常: {str(e)}")
            import traceback
            logger.error(f"   - 异常堆栈: {traceback.format_exc()}")
            return {}

    def _extract_price_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取价格信息字段"""
        try:
            fields = {}

            logger.info(f"🔍 [价格字段] 开始提取价格字段")

            # 智能查找价格信息
            price_info = self._find_price_info_in_data(product_data)

            if not price_info:
                logger.warning(f"⚠️ [价格字段] 未找到价格信息")
                return {'real_price': 0.0, 'original_price': 0.0, 'currency': 'USD'}

            # real_price -> price
            real_price = self._extract_real_price_from_data(price_info)
            fields['real_price'] = real_price

            # original_price
            original_price = self._extract_original_price_from_data(price_info)
            fields['original_price'] = original_price

            # currency
            currency = self._extract_currency_from_data(price_info)
            fields['currency'] = currency

            # 计算折扣金额
            discount_amount = original_price - real_price if original_price > real_price else 0
            fields['discount_amount'] = discount_amount

            logger.info(f"💰 [价格字段] 售价=${real_price}, 原价=${original_price}, 币种={currency}")
            return fields

        except Exception as e:
            logger.error(f"❌ [价格字段] 提取价格字段异常: {str(e)}")
            import traceback
            logger.error(f"   - 异常堆栈: {traceback.format_exc()}")
            return {'real_price': 0.0, 'original_price': 0.0, 'currency': 'USD'}

    def _find_price_info_in_data(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """在数据中智能查找价格信息"""
        try:
            # 尝试多种可能的价格字段路径
            possible_paths = ['price', 'pricing', 'priceInfo', 'price_info']

            # 优先检查product_base.price路径（TikTok Shop新结构）
            if 'product_base' in data and isinstance(data['product_base'], dict):
                product_base = data['product_base']
                if 'price' in product_base and isinstance(product_base['price'], dict):
                    logger.info(f"✅ [价格查找] 在'product_base.price'找到价格信息")
                    return product_base['price']

            # 直接查找
            for path in possible_paths:
                if path in data and isinstance(data[path], dict):
                    logger.info(f"✅ [价格查找] 在'{path}'找到价格信息")
                    return data[path]

            # 在嵌套结构中查找
            for key, value in data.items():
                if isinstance(value, dict):
                    for path in possible_paths:
                        if path in value and isinstance(value[path], dict):
                            logger.info(f"✅ [价格查找] 在'{key}.{path}'找到价格信息")
                            return value[path]

            # 如果没有找到专门的价格对象，检查是否直接包含价格字段
            price_fields = ['real_price', 'current_price', 'sale_price', 'price']
            if any(field in data for field in price_fields):
                logger.info(f"✅ [价格查找] 在根级别找到价格字段")
                return data

            logger.warning(f"⚠️ [价格查找] 未找到价格信息")
            return None

        except Exception as e:
            logger.error(f"❌ [价格查找] 查找价格信息异常: {str(e)}")
            return None

    def _extract_real_price_from_data(self, price_data: Dict[str, Any]) -> float:
        """从价格数据中提取实际价格"""
        try:
            possible_fields = ['real_price', 'current_price', 'sale_price', 'price', 'finalPrice']

            for field in possible_fields:
                if field in price_data and price_data[field] is not None:
                    price = self._extract_price_number(price_data[field])
                    if price > 0:
                        logger.info(f"✅ [实际价格] 在'{field}'找到: ${price}")
                        return price

            logger.warning(f"⚠️ [实际价格] 未找到有效的实际价格")
            return 0.0

        except Exception as e:
            logger.error(f"❌ [实际价格] 提取实际价格异常: {str(e)}")
            return 0.0

    def _extract_original_price_from_data(self, price_data: Dict[str, Any]) -> float:
        """从价格数据中提取原价"""
        try:
            possible_fields = ['original_price', 'list_price', 'msrp', 'regularPrice']

            for field in possible_fields:
                if field in price_data and price_data[field] is not None:
                    price = self._extract_price_number(price_data[field])
                    if price > 0:
                        logger.info(f"✅ [原价] 在'{field}'找到: ${price}")
                        return price

            logger.warning(f"⚠️ [原价] 未找到有效的原价")
            return 0.0

        except Exception as e:
            logger.error(f"❌ [原价] 提取原价异常: {str(e)}")
            return 0.0

    def _extract_currency_from_data(self, price_data: Dict[str, Any]) -> str:
        """从价格数据中提取货币类型"""
        try:
            possible_fields = ['currency', 'currencyCode', 'currency_code']

            for field in possible_fields:
                if field in price_data and price_data[field]:
                    currency = str(price_data[field])
                    logger.info(f"✅ [货币] 在'{field}'找到: {currency}")
                    return currency

            logger.warning(f"⚠️ [货币] 未找到货币信息，使用默认USD")
            return 'USD'

        except Exception as e:
            logger.error(f"❌ [货币] 提取货币异常: {str(e)}")
            return 'USD'

    def _extract_image_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取图片信息字段"""
        try:
            fields = {}

            images_data = product_data.get('images', [])
            if not isinstance(images_data, list):
                logger.warning(f"⚠️ [图片字段] images字段不是列表类型")
                return {'images': []}

            product_images = []
            for i, img_data in enumerate(images_data):
                if isinstance(img_data, dict):
                    url_list = img_data.get('url_list', [])
                    if url_list and isinstance(url_list, list):
                        # 使用第一个URL并解码Unicode转义字符
                        img_url = self._decode_unicode_escapes(url_list[0])

                        from ..models.product import ProductImage
                        product_image = ProductImage(
                            url=img_url,
                            is_main=(i == 0),
                            order=i
                        )
                        product_images.append(product_image)

            fields['images'] = product_images
            logger.info(f"🖼️ [图片字段] 提取到 {len(product_images)} 张图片")
            return fields

        except Exception as e:
            logger.error(f"❌ [图片字段] 提取图片字段异常: {str(e)}")
            return {'images': []}

    def _extract_shipping_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取运费信息字段"""
        try:
            fields = {}

            shipping_fee_info = product_data.get('shipping_fee', {})
            if not isinstance(shipping_fee_info, dict):
                logger.warning(f"⚠️ [运费字段] shipping_fee字段不是字典类型")
                return {
                    'shipping_price_str': '$0.00',
                    'shipping_price_val': '0',
                    'shipping_currency': 'USD'
                }

            # shipping_fee.price_str
            shipping_price_str = shipping_fee_info.get('price_str', '$0.00')
            fields['shipping_price_str'] = shipping_price_str

            # shipping_fee.price_val
            shipping_price_val = shipping_fee_info.get('price_val', '0')
            fields['shipping_price_val'] = shipping_price_val

            # shipping_fee.currency
            shipping_currency = shipping_fee_info.get('currency', 'USD')
            fields['shipping_currency'] = shipping_currency

            logger.info(f"🚚 [运费字段] 运费={shipping_price_str} ({shipping_currency})")
            return fields

        except Exception as e:
            logger.error(f"❌ [运费字段] 提取运费字段异常: {str(e)}")
            return {
                'shipping_price_str': '$0.00',
                'shipping_price_val': '0',
                'shipping_currency': 'USD'
            }

    def _extract_review_fields(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取评价信息字段"""
        try:
            fields = {}

            review_info = product_data.get('product_detail_review', {})
            if not isinstance(review_info, dict):
                logger.warning(f"⚠️ [评价字段] product_detail_review字段不是字典类型")
                return {'rating': 0.0, 'review_count': 0}

            # product_detail_review.product_rating -> rating.average_rating
            rating = float(review_info.get('product_rating', 0.0))
            fields['rating'] = rating

            # product_detail_review.review_count -> rating.total_reviews
            review_count = int(review_info.get('review_count', 0))
            fields['review_count'] = review_count

            logger.info(f"⭐ [评价字段] 评分={rating}, 评价数={review_count}")
            return fields

        except Exception as e:
            logger.error(f"❌ [评价字段] 提取评价字段异常: {str(e)}")
            return {'rating': 0.0, 'review_count': 0}

    def _decode_unicode_escapes(self, text: str) -> str:
        """解码Unicode转义字符"""
        try:
            if not text:
                return text

            # 处理常见的Unicode转义字符
            decoded_text = text.replace('\\u002F', '/')
            decoded_text = decoded_text.replace('\\u0026', '&')
            decoded_text = decoded_text.replace('\\u003D', '=')
            decoded_text = decoded_text.replace('\\u003F', '?')

            # 使用codecs处理其他Unicode转义
            try:
                import codecs
                decoded_text = codecs.decode(decoded_text, 'unicode_escape')
            except Exception:
                # 如果codecs解码失败，使用原始文本
                pass

            return decoded_text

        except Exception as e:
            logger.debug(f"⚠️ [Unicode解码] 解码失败: {str(e)}")
            return text

    def _extract_price_number(self, price_str: str) -> float:
        """从价格字符串中提取数字（支持价格区间，取第一个值）"""
        try:
            if not price_str:
                return 0.0

            import re
            price_str = str(price_str).strip()

            # 处理价格区间情况，如 "$18.86 - 28.54" 或 "18.86-28.54"
            # 匹配第一个价格数字（支持小数点）
            price_match = re.search(r'[\d.]+', price_str)
            if price_match:
                first_price = price_match.group()
                logger.debug(f"💰 [价格提取] 从'{price_str}'中提取第一个价格: {first_price}")
                return float(first_price)
            else:
                logger.debug(f"⚠️ [价格提取] 无法从'{price_str}'中提取价格数字")
                return 0.0

        except Exception as e:
            logger.debug(f"⚠️ [价格提取] 价格提取异常: {str(e)}")
            return 0.0

    def _extract_title_from_url(self, url: str, product_id: str) -> str:
        """从URL中提取商品标题"""
        try:
            if not url:
                return f"Product {product_id}"

            # 从URL路径中提取标题部分
            url_parts = url.split('/')
            if len(url_parts) > 1:
                title_part = url_parts[-2]  # 倒数第二个部分通常是标题
                if title_part and title_part != product_id:
                    # 将连字符替换为空格并标题化
                    title = title_part.replace('-', ' ').title()
                    return title

            return f"Product {product_id}"

        except Exception as e:
            logger.debug(f"⚠️ [标题提取] 从URL提取标题失败: {str(e)}")
            return f"Product {product_id}"

    def _extract_shop_name_from_data(self, data: Dict[str, Any]) -> str:
        """从数据中智能提取店铺名称"""
        try:
            # 尝试多种可能的字段名和路径
            possible_paths = [
                'shop_name'
            ]

            # 直接查找
            for path in possible_paths:
                if path in data and data[path]:
                    logger.info(f"✅ [店铺名称] 在'{path}'找到: {data[path]}")
                    return str(data[path])

            # 在嵌套结构中查找
            for key, value in data.items():
                if isinstance(value, dict):
                    for path in possible_paths:
                        if path in value and value[path]:
                            logger.info(f"✅ [店铺名称] 在'{key}.{path}'找到: {value[path]}")
                            return str(value[path])

            logger.warning(f"⚠️ [店铺名称] 未找到店铺名称字段")
            return ''

        except Exception as e:
            logger.error(f"❌ [店铺名称] 提取店铺名称异常: {str(e)}")
            return ''

    def _extract_shop_product_count_from_data(self, data: Dict[str, Any]) -> int:
        """从数据中智能提取店铺商品数量"""
        try:
            # 尝试多种可能的字段名和路径
            possible_paths = [
                'on_sell_product_count',
                'onSellProductCount',
                'product_count',
                'productCount',
                'total_products',
                'totalProducts'
            ]

            # 直接查找
            for path in possible_paths:
                if path in data and data[path] is not None:
                    count = int(data[path])
                    logger.info(f"✅ [商品数量] 在'{path}'找到: {count}")
                    return count

            # 在嵌套结构中查找
            for key, value in data.items():
                if isinstance(value, dict):
                    for path in possible_paths:
                        if path in value and value[path] is not None:
                            count = int(value[path])
                            logger.info(f"✅ [商品数量] 在'{key}.{path}'找到: {count}")
                            return count

            logger.warning(f"⚠️ [商品数量] 未找到商品数量字段")
            return 0

        except Exception as e:
            logger.error(f"❌ [商品数量] 提取商品数量异常: {str(e)}")
            return 0

    def _extract_sold_count_from_data(self, data: Dict[str, Any]) -> int:
        """从数据中智能提取销量"""
        try:
            # 尝试多种可能的字段名和路径
            possible_paths = [
                'sold_count'
            ]

            # 优先检查product_base.sold_count路径（TikTok Shop新结构）
            if 'product_base' in data and isinstance(data['product_base'], dict):
                product_base = data['product_base']
                for path in possible_paths:
                    if path in product_base and product_base[path] is not None:
                        count = int(product_base[path])
                        logger.info(f"✅ [销量] 在'product_base.{path}'找到: {count}")
                        return count

            # 直接查找
            for path in possible_paths:
                if path in data and data[path] is not None:
                    count = int(data[path])
                    logger.info(f"✅ [销量] 在'{path}'找到: {count}")
                    return count

            # 在嵌套结构中查找
            for key, value in data.items():
                if isinstance(value, dict):
                    for path in possible_paths:
                        if path in value and value[path] is not None:
                            count = int(value[path])
                            logger.info(f"✅ [销量] 在'{key}.{path}'找到: {count}")
                            return count

            logger.warning(f"⚠️ [销量] 未找到销量字段")
            return 0

        except Exception as e:
            logger.error(f"❌ [销量] 提取销量异常: {str(e)}")
            return 0

    def _validate_required_fields(self, fields: Dict[str, Any]) -> bool:
        """验证必需字段是否存在"""
        try:
            required_fields = [
                'product_id', 'product_url', 'shop_name', 'title',
                'real_price', 'currency', 'sold_count', 'rating', 'review_count'
            ]

            missing_fields = []
            for field in required_fields:
                if field not in fields or fields[field] is None:
                    missing_fields.append(field)

            if missing_fields:
                logger.error(f"❌ [字段验证] 缺少必需字段: {missing_fields}")
                return False

            # 验证product_id不为空
            if not fields.get('product_id'):
                logger.error(f"❌ [字段验证] product_id为空")
                return False

            logger.info(f"✅ [字段验证] 所有必需字段验证通过")
            return True

        except Exception as e:
            logger.error(f"❌ [字段验证] 字段验证异常: {str(e)}")
            return False

    def _create_product_from_fields(self, fields: Dict[str, Any]) -> Optional[Product]:
        """从提取的字段创建Product对象"""
        try:
            logger.info(f"🏗️ [对象创建] 开始创建Product对象")

            # 转换货币枚举
            from ..models.product import Currency, ProductStatus, ProductRating
            try:
                currency_enum = Currency(fields['currency'])
            except ValueError:
                logger.warning(f"⚠️ [对象创建] 未知货币类型: {fields['currency']}, 使用USD")
                currency_enum = Currency.USD

            # 创建ProductRating对象
            product_rating = ProductRating(
                average_rating=fields['rating'],
                total_reviews=fields['review_count']
            )

            # 使用当前时间作为上架时间
            from datetime import datetime
            current_time = datetime.now()

            # 创建Product对象
            product = Product(
                product_id=fields['product_id'],
                title=fields['title'],
                description=f"Shop: {fields['shop_name']}, Reviews: {fields['review_count']}, Shop Products: {fields.get('on_sell_product_count', 0)}",
                category='Unknown',  # 默认类别
                price=Decimal(str(fields['real_price'])),
                original_price=Decimal(str(fields['original_price'])) if fields.get('original_price', 0) > 0 else None,
                currency=currency_enum,
                images=fields.get('images', []),
                product_url=fields['product_url'],
                shop_name=fields['shop_name'],
                rating=product_rating,
                status=ProductStatus.ACTIVE,
                sold_count=fields['sold_count'],
                created_at=current_time,
                updated_at=current_time,
                # 将额外信息存储在metadata中
                metadata={
                    'shipping_fee': {
                        'price_str': fields.get('shipping_price_str', '$0.00'),
                        'price_val': fields.get('shipping_price_val', '0'),
                        'currency': fields.get('shipping_currency', 'USD')
                    },
                    'shop_product_count': fields.get('on_sell_product_count', 0),
                    'review_count': fields['review_count'],
                    'discount_amount': fields.get('discount_amount', 0)
                }
            )

            logger.info(f"✅ [对象创建] Product对象创建成功: {product.product_id}")
            return product

        except Exception as e:
            logger.error(f"❌ [对象创建] 创建Product对象异常: {str(e)}")
            import traceback
            logger.error(f"   - 异常堆栈: {traceback.format_exc()}")
            return None

    def _log_json_structure_debug(self, data: Dict[str, Any]):
        """记录JSON结构调试信息"""
        try:
            logger.info(f"🔍 [JSON调试] JSON数据结构分析:")
            logger.info(f"   - 根级别键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, dict):
                        logger.info(f"   - {key}: dict with keys {list(value.keys())[:10]}")
                        # 如果是loaderData，显示更深层的结构
                        if key == 'loaderData' and isinstance(value, dict):
                            self._log_loader_data_structure(value)
                    elif isinstance(value, list):
                        logger.info(f"   - {key}: list with {len(value)} items")
                    else:
                        logger.info(f"   - {key}: {type(value).__name__}")

        except Exception as e:
            logger.debug(f"⚠️ [JSON调试] 记录调试信息失败: {str(e)}")

    def _log_loader_data_structure(self, loader_data: Dict[str, Any]):
        """记录loaderData的详细结构"""
        try:
            logger.info(f"🔍 [loaderData结构] 详细分析:")
            for key, value in loader_data.items():
                if isinstance(value, dict):
                    logger.info(f"   - loaderData.{key}: dict with keys {list(value.keys())[:10]}")
                    # 如果包含shop/pdp路径，进一步分析
                    if 'shop/pdp' in str(key) or any('shop' in str(k) for k in value.keys()):
                        self._log_shop_pdp_structure(value, f"loaderData.{key}")
                elif isinstance(value, list):
                    logger.info(f"   - loaderData.{key}: list with {len(value)} items")
                else:
                    logger.info(f"   - loaderData.{key}: {type(value).__name__}")
        except Exception as e:
            logger.debug(f"⚠️ [loaderData结构] 记录失败: {str(e)}")

    def _log_shop_pdp_structure(self, data: Dict[str, Any], path: str):
        """记录shop/pdp相关的结构"""
        try:
            logger.info(f"🔍 [shop/pdp结构] {path} 详细分析:")
            for key, value in data.items():
                if isinstance(value, dict):
                    logger.info(f"   - {path}.{key}: dict with keys {list(value.keys())[:15]}")
                    # 检查是否包含商品相关字段
                    if any(field in value for field in ['route_product_id', 'product_id', 'shop_name', 'price']):
                        logger.info(f"   - {path}.{key} 包含商品相关字段!")
                elif isinstance(value, list):
                    logger.info(f"   - {path}.{key}: list with {len(value)} items")
                else:
                    logger.info(f"   - {path}.{key}: {type(value).__name__}")
        except Exception as e:
            logger.debug(f"⚠️ [shop/pdp结构] 记录失败: {str(e)}")

    def _log_found_product_data_structure(self, product_data: Dict[str, Any], path: str):
        """记录找到的商品数据结构"""
        try:
            logger.info(f"🎯 [找到的商品数据] 在路径 {path} 的数据结构:")
            logger.info(f"   - 数据键数量: {len(product_data.keys())}")
            logger.info(f"   - 所有键: {list(product_data.keys())}")

            # 检查关键字段是否存在
            key_fields = ['route_product_id', 'shop_name', 'price', 'images', 'sold_count', 'shipping_fee', 'product_detail_review']
            for field in key_fields:
                if field in product_data:
                    value = product_data[field]
                    if isinstance(value, dict):
                        logger.info(f"   - {field}: dict with keys {list(value.keys())[:10]}")
                    elif isinstance(value, list):
                        logger.info(f"   - {field}: list with {len(value)} items")
                    else:
                        logger.info(f"   - {field}: {type(value).__name__} = {str(value)[:100]}")
                else:
                    logger.warning(f"   - {field}: ❌ 缺失")

        except Exception as e:
            logger.debug(f"⚠️ [找到的商品数据] 记录失败: {str(e)}")

    def _log_extracted_fields_summary(self, fields: Dict[str, Any]):
        """记录提取字段的摘要信息"""
        try:
            logger.info(f"📋 [字段摘要] 提取字段总结:")
            logger.info(f"   - 商品ID: {fields.get('product_id', 'N/A')}")
            logger.info(f"   - 商品标题: {fields.get('title', 'N/A')}")
            logger.info(f"   - 店铺名称: {fields.get('shop_name', 'N/A')}")
            logger.info(f"   - 售价: ${fields.get('real_price', 0)}")
            logger.info(f"   - 原价: ${fields.get('original_price', 0)}")
            logger.info(f"   - 币种: {fields.get('currency', 'N/A')}")
            logger.info(f"   - 销量: {fields.get('sold_count', 0)}")
            logger.info(f"   - 评分: {fields.get('rating', 0)}")
            logger.info(f"   - 评价数: {fields.get('review_count', 0)}")
            logger.info(f"   - 图片数: {len(fields.get('images', []))}")
            logger.info(f"   - 运费: {fields.get('shipping_price_str', 'N/A')}")

        except Exception as e:
            logger.debug(f"⚠️ [字段摘要] 记录摘要失败: {str(e)}")

    def _extract_product_info_from_html(self, html_content: str, pdp_url: str, product_id: str) -> Optional[Product]:
        """从HTML中直接提取商品信息（备用方案）"""
        try:
            logger.debug(f"📦 [HTML解析] 使用备用方案从HTML提取商品信息")

            # 使用BeautifulSoup解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 尝试从页面标题提取商品名称
            title = ""
            title_tag = soup.find('title')
            if title_tag:
                title = title_tag.get_text().strip()
                # 清理标题（移除TikTok Shop等后缀）
                title = title.replace(' | TikTok Shop', '').replace(' - TikTok Shop', '').strip()

            if not title:
                title = f"Product {product_id}"

            # 创建基本的Product对象
            product = Product(
                product_id=product_id,
                title=title,
                description='',
                category='Unknown',  # 添加必需的category参数
                price=Decimal('0'),
                currency=Currency.USD,
                images=[],
                product_url=pdp_url,
                shop_name='',
                rating=0.0,
                status=ProductStatus.ACTIVE,
                sold_count=0
            )

            logger.debug(f"✅ [HTML解析] 创建基本商品对象: {title}")
            return product

        except Exception as e:
            logger.debug(f"❌ [HTML解析] 从HTML提取商品信息失败: {str(e)}")
            return None
    
    

    


    def _fallback_seller_extraction(self, html_content: str) -> Optional[Dict[str, str]]:
        """备用seller信息提取方法"""
        try:
            logger.info(f"🔄 [HTML解析] 提取seller信息")

            import re

            # 方法1: 在整个HTML中搜索shop_link模式
            logger.info(f"🔍 [HTML解析] 搜索shop_link模式")
            shop_link_patterns = [
                r'"shop_link"[:\s]*"([^"]*shop/store/[^"]*)"',
                r'shop_link["\']?\s*[:=]\s*["\']([^"\']*shop/store/[^"\']*)["\']',
                r'https:\\u002F\\u002Fwww\.tiktok\.com\\u002Fshop\\u002Fstore\\u002F[^\\]+\\u002F\d+',
            ]

            for pattern in shop_link_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    shop_link = matches[0]
                    logger.info(f"✅ [HTML解析] 方法1找到shop_link: {shop_link}")

                    # 解码Unicode转义字符
                    import codecs
                    try:
                        decoded_shop_link = codecs.decode(shop_link, 'unicode_escape')
                        logger.info(f"🔄 [HTML解析] 解码shop_link: {decoded_shop_link}")

                        # 提取seller_id
                        seller_id_match = re.search(r'/shop/store/[^/]+/(\d+)', decoded_shop_link)
                        if seller_id_match:
                            seller_id = seller_id_match.group(1)
                            shop_name_match = re.search(r'/shop/store/([^/]+)/', decoded_shop_link)
                            shop_name = shop_name_match.group(1) if shop_name_match else ''

                            result = {
                                'seller_id': seller_id,
                                'shop_name': shop_name,
                                'shop_id': seller_id,
                                'shop_link': decoded_shop_link
                            }

                            logger.info(f"🎉 [HTML解析] 方法1成功提取seller信息")
                            return result
                    except Exception as decode_error:
                        logger.warning(f"⚠️ [HTML解析] 解码shop_link失败: {str(decode_error)}")
                        continue

            # 方法2: 搜索seller_id模式
            logger.info(f"🔍 [HTML解析] 备用方法: 搜索seller_id模式")
            seller_patterns = [
                r'"seller_id"[:\s]*"?(\d+)"?',
                r'seller_id["\']?\s*[:=]\s*["\']?(\d+)',
                r'sellerId["\']?\s*[:=]\s*["\']?(\d+)',
            ]

            for pattern in seller_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    seller_id = matches[0]
                    logger.info(f"✅ [HTML解析] 备用方法找到seller_id: {seller_id}")

                    # 尝试提取shop_name
                    shop_name_match = re.search(r'"shop_name"[:\s]*"([^"]+)"', html_content)
                    shop_name = shop_name_match.group(1) if shop_name_match else ''

                    result = {
                        'seller_id': seller_id,
                        'shop_name': shop_name,
                        'shop_id': seller_id
                    }

                    logger.info(f"🎉 [HTML解析] 备用方法成功提取seller信息")
                    return result

            logger.warning(f"❌ [HTML解析] 所有方法都失败")
            return None

        except Exception as e:
            logger.error(f"💥 [HTML解析] 备用方法异常: {str(e)}")
            return None

    def _parse_shop_html_product_data(self, shop_html: str, original_url: str) -> Optional[Product]:
        """从shop页面HTML中解析产品数据"""
        try:
            logger.info(f"📦 [HTML解析] 开始从shop页面HTML解析产品数据")
            logger.debug(f"📄 [HTML解析] HTML内容长度: {len(shop_html)} 字符")

            # 使用BeautifulSoup解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(shop_html, 'html.parser')
            logger.info(f"✅ [HTML解析] BeautifulSoup解析完成")

            # 查找包含产品数据的script标签
            logger.info(f"🔍 [HTML解析] 查找包含产品数据的script标签")
            script_tags = soup.find_all('script')
            logger.info(f"📊 [HTML解析] 找到 {len(script_tags)} 个script标签")

            product_data_found = False
            for i, script in enumerate(script_tags):
                if script.string:
                    script_content = script.string.strip()

                    # 检查是否包含产品相关数据
                    if any(keyword in script_content.lower() for keyword in ['product', 'item', 'goods', 'shop']):
                        logger.info(f"🎯 [HTML解析] 在第{i+1}个script标签中发现产品相关数据")
                        logger.debug(f"📝 [HTML解析] script内容长度: {len(script_content)} 字符")
                        logger.debug(f"📝 [HTML解析] script内容预览: {script_content[:500]}...")

                        # 尝试解析JSON数据
                        try:
                            # 查找JSON对象
                            json_start = script_content.find('{')
                            json_end = script_content.rfind('}') + 1

                            if json_start != -1 and json_end > json_start:
                                json_content = script_content[json_start:json_end]
                                logger.debug(f"📋 [HTML解析] 提取的JSON内容: {json_content[:200]}...")

                                try:
                                    data = json.loads(json_content)
                                    logger.info(f"✅ [HTML解析] JSON解析成功")
                                    logger.debug(f"📊 [HTML解析] JSON数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

                                    # 检查是否是Schema.org的ItemList（产品列表）
                                    if isinstance(data, dict) and data.get('@type') == 'ItemList':
                                        logger.info(f"🎯 [HTML解析] 发现Schema.org ItemList产品数据")

                                        item_list = data.get('itemListElement', [])
                                        logger.info(f"📦 [HTML解析] 产品列表包含 {len(item_list)} 个产品")

                                        if item_list:
                                            # 取第一个产品（通常是我们要找的产品）
                                            first_product = item_list[0]
                                            logger.info(f"🔍 [HTML解析] 解析第一个产品数据")

                                            # 解析产品信息
                                            product = self._parse_schema_product_data(first_product, original_url)
                                            if product:
                                                logger.info(f"🎉 [HTML解析] 成功从Schema.org数据解析产品")
                                                return product

                                    # 检查其他可能的数据结构
                                    elif isinstance(data, dict) and 'product' in str(data).lower():
                                        logger.info(f"� [HTML解析] 发现其他产品相关JSON数据")
                                        logger.debug(f"📋 [HTML解析] 数据类型: {data.get('@type', 'unknown')}")

                                    product_data_found = True

                                except json.JSONDecodeError as json_error:
                                    logger.debug(f"⚠️ [HTML解析] JSON解析失败: {str(json_error)}")
                                    continue
                        except Exception as parse_error:
                            logger.debug(f"⚠️ [HTML解析] 解析script内容失败: {str(parse_error)}")
                            continue

            if not product_data_found:
                logger.warning(f"❌ [HTML解析] 未在shop页面HTML中找到产品数据")
                logger.info(f"🔍 [HTML解析] HTML结构分析:")

                # 分析HTML结构
                title_tags = soup.find_all(['title', 'h1', 'h2', 'h3'])
                logger.info(f"   标题标签数量: {len(title_tags)}")
                for tag in title_tags[:3]:  # 只显示前3个
                    logger.info(f"   {tag.name}: {tag.get_text()[:100]}")

                # 查找可能包含数据的div
                data_divs = soup.find_all('div', {'id': True})
                logger.info(f"   带ID的div数量: {len(data_divs)}")
                for div in data_divs[:5]:  # 只显示前5个
                    logger.info(f"   div#{div.get('id')}")

            # 暂时返回None，等分析完HTML结构后再实现具体解析
            logger.info(f"📦 [HTML解析] 当前返回None，等待进一步分析HTML结构")
            return None

        except Exception as e:
            logger.error(f"💥 [HTML解析] 解析shop页面HTML失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return None

    def _parse_schema_product_data(self, schema_data: Dict[str, Any], original_url: str) -> Optional[Product]:
        """解析Schema.org格式的产品数据"""
        try:
            logger.info(f"📦 [Schema解析] 开始解析Schema.org产品数据")
            logger.debug(f"📊 [Schema解析] 数据结构: {list(schema_data.keys()) if isinstance(schema_data, dict) else type(schema_data)}")

            # 基本产品信息
            product_name = schema_data.get('name', '')
            product_url = schema_data.get('url', original_url)
            description = schema_data.get('description', '')

            logger.info(f"📋 [Schema解析] 产品名称: {product_name}")
            logger.info(f"🔗 [Schema解析] 产品URL: {product_url}")

            # 提取产品ID（从URL中提取）
            product_id = ''
            if product_url:
                import re
                id_match = re.search(r'/(\d+)/?$', product_url)
                if id_match:
                    product_id = id_match.group(1)
                    logger.info(f"🆔 [Schema解析] 提取到产品ID: {product_id}")

            # 价格信息
            price_info = schema_data.get('offers', {})
            if isinstance(price_info, list) and price_info:
                price_info = price_info[0]  # 取第一个价格信息

            price = 0.0
            currency = 'USD'
            if isinstance(price_info, dict):
                price_value = price_info.get('price', price_info.get('lowPrice', 0))
                if isinstance(price_value, (str, int, float)):
                    try:
                        price = float(price_value)
                        logger.info(f"💰 [Schema解析] 价格: {price}")
                    except (ValueError, TypeError):
                        logger.warning(f"⚠️ [Schema解析] 价格解析失败: {price_value}")

                currency = price_info.get('priceCurrency', currency)

            # 图片信息
            images = []
            image_data = schema_data.get('image', [])
            if isinstance(image_data, str):
                images = [image_data]
            elif isinstance(image_data, list):
                images = image_data

            logger.info(f"🖼️ [Schema解析] 找到 {len(images)} 张图片")

            # 评分信息
            rating = 0.0
            review_count = 0
            rating_data = schema_data.get('aggregateRating', {})
            if isinstance(rating_data, dict):
                rating_value = rating_data.get('ratingValue', 0)
                if isinstance(rating_value, (str, int, float)):
                    try:
                        rating = float(rating_value)
                        logger.info(f"⭐ [Schema解析] 评分: {rating}")
                    except (ValueError, TypeError):
                        pass

                review_count_value = rating_data.get('reviewCount', 0)
                if isinstance(review_count_value, (str, int)):
                    try:
                        review_count = int(review_count_value)
                        logger.info(f"📝 [Schema解析] 评论数: {review_count}")
                    except (ValueError, TypeError):
                        pass

            # 品牌信息
            brand = ''
            brand_data = schema_data.get('brand', {})
            if isinstance(brand_data, dict):
                brand = brand_data.get('name', '')
            elif isinstance(brand_data, str):
                brand = brand_data

            # 创建Product对象
            from src.models.product import Product, ProductStatus

            product = Product(
                product_id=product_id,
                title=product_name,
                description=description,
                price=price,
                currency=currency,
                images=images,
                product_url=product_url,
                shop_name=brand,  # 使用brand作为shop_name
                rating=rating,
                review_count=review_count,
                status=ProductStatus.ACTIVE
            )

            logger.info(f"🎉 [Schema解析] 成功创建Product对象")
            logger.info(f"   - ID: {product.product_id}")
            logger.info(f"   - 标题: {product.title}")
            logger.info(f"   - 价格: {product.price} {product.currency}")
            logger.info(f"   - 图片数量: {len(product.images)}")

            return product

        except Exception as e:
            logger.error(f"💥 [Schema解析] 解析Schema.org产品数据失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return None

    def _parse_pdp_product_data(self, shop_data: Dict[str, Any], original_url: str) -> Optional[Product]:
        """解析PDP商品数据"""
        try:
            logger.info(f"📦 [数据解析] 开始解析PDP商品数据")
            logger.debug(f"📊 [数据解析] shop_data结构: {list(shop_data.keys()) if isinstance(shop_data, dict) else type(shop_data)}")

            # 从shop API响应中提取产品数据
            data_section = shop_data.get('data', {})
            logger.info(f"📋 [数据解析] data节点存在: {bool(data_section)}")

            if isinstance(data_section, dict):
                logger.debug(f"📊 [数据解析] data节点结构: {list(data_section.keys())}")

            products_data = data_section.get('products', []) if isinstance(data_section, dict) else []
            logger.info(f"📦 [数据解析] 找到 {len(products_data)} 个产品")

            if not products_data:
                logger.warning(f"❌ [数据解析] Shop API响应中没有产品数据")
                logger.debug(f"📊 [数据解析] 完整响应结构: {shop_data}")
                return None

            # 通常取第一个产品，或者根据URL中的product_id匹配
            product_data = products_data[0]
            logger.info(f"📦 [数据解析] 选择第一个产品进行解析")
            logger.debug(f"📊 [数据解析] 产品数据结构: {list(product_data.keys()) if isinstance(product_data, dict) else type(product_data)}")

            # 提取基本信息
            logger.info(f"📝 [数据解析] 提取基本信息")
            product_id = str(product_data.get('product_id', ''))
            title = product_data.get('title', '')
            description = product_data.get('description', '')

            logger.info(f"   - 商品ID: {product_id}")
            logger.info(f"   - 商品标题: {title}")
            logger.info(f"   - 描述长度: {len(description)} 字符")

            # 提取图片信息
            logger.info(f"🖼️ [数据解析] 提取图片信息")
            images = []
            image_data = product_data.get('image', {})

            if image_data:
                logger.info(f"   - 找到主图数据")
                main_image = ProductImage(
                    url=image_data.get('url', ''),
                    width=int(image_data.get('width', 0)),
                    height=int(image_data.get('height', 0)),
                    is_main=True
                )
                images.append(main_image)
                logger.info(f"   - 主图URL: {main_image.url}")
                logger.info(f"   - 主图尺寸: {main_image.width}x{main_image.height}")
            else:
                logger.warning(f"   - 未找到图片数据")

            # 提取价格信息
            logger.info(f"💰 [数据解析] 提取价格信息")
            price_info = product_data.get('product_price_info', {})
            logger.debug(f"   - 价格信息结构: {list(price_info.keys()) if isinstance(price_info, dict) else type(price_info)}")

            sale_price = Decimal(str(price_info.get('sale_price', 0))) / 100  # 转换为实际价格
            original_price = Decimal(str(price_info.get('original_price', 0))) / 100
            currency = Currency(price_info.get('currency', 'USD'))

            discount_rate = 0
            if original_price > 0:
                discount_rate = float((original_price - sale_price) / original_price * 100)

            logger.info(f"   - 售价: {sale_price} {currency}")
            logger.info(f"   - 原价: {original_price} {currency}")
            logger.info(f"   - 折扣率: {discount_rate:.1f}%")

            # 提取评分信息
            logger.info(f"⭐ [数据解析] 提取评分信息")
            rate_info = product_data.get('rate_info', {})
            logger.debug(f"   - 评分信息结构: {list(rate_info.keys()) if isinstance(rate_info, dict) else type(rate_info)}")

            rating = ProductRating(
                score=float(rate_info.get('rating_score', 0)),
                count=int(rate_info.get('review_count', 0))
            )

            logger.info(f"   - 评分: {rating.score}")
            logger.info(f"   - 评论数: {rating.count}")

            # 提取销量信息
            logger.info(f"📈 [数据解析] 提取销量信息")
            sold_info = product_data.get('sold_info', {})
            logger.debug(f"   - 销量信息结构: {list(sold_info.keys()) if isinstance(sold_info, dict) else type(sold_info)}")

            sold_count = int(sold_info.get('sold_count', 0))
            logger.info(f"   - 已售数量: {sold_count}")

            # 提取seller信息
            logger.info(f"🏪 [数据解析] 提取seller信息")
            seller_info = product_data.get('seller_info', {})
            logger.debug(f"   - seller信息结构: {list(seller_info.keys()) if isinstance(seller_info, dict) else type(seller_info)}")

            shop_id = str(seller_info.get('seller_id', ''))
            shop_name = seller_info.get('shop_name', '')

            logger.info(f"   - 店铺ID: {shop_id}")
            logger.info(f"   - 店铺名称: {shop_name}")

            # 提取SEO URL信息
            logger.info(f"🔗 [数据解析] 提取SEO URL信息")
            seo_info = product_data.get('seo_url', {})
            logger.debug(f"   - SEO信息结构: {list(seo_info.keys()) if isinstance(seo_info, dict) else type(seo_info)}")

            canonical_url = seo_info.get('canonical_url', original_url)
            logger.info(f"   - 规范URL: {canonical_url}")

            # 创建Product对象
            logger.info(f"🏗️ [数据解析] 创建Product对象")
            try:
                product = Product(
                    product_id=product_id,
                    title=title,
                    description=description,
                    price=sale_price,
                    original_price=original_price,
                    currency=currency,
                    discount_rate=discount_rate,
                    images=images,
                    rating=rating,
                    sold_count=sold_count,
                    shop_id=shop_id,
                    shop_name=shop_name,
                    product_url=canonical_url,
                    status=ProductStatus.ACTIVE
                )

                logger.info(f"✅ [数据解析] Product对象创建成功")
                logger.info(f"🎉 [数据解析] 完整的产品信息:")
                logger.info(f"   - ID: {product.product_id}")
                logger.info(f"   - 标题: {product.title}")
                logger.info(f"   - 价格: {product.price} {product.currency}")
                logger.info(f"   - 评分: {product.rating.score} ({product.rating.count}条评论)")
                logger.info(f"   - 销量: {product.sold_count}")
                logger.info(f"   - 店铺: {product.shop_name} (ID: {product.shop_id})")
                logger.info(f"   - 图片数量: {len(product.images)}")

                return product

            except Exception as create_error:
                logger.error(f"❌ [数据解析] 创建Product对象失败: {str(create_error)}")
                logger.error(f"   - 参数检查:")
                logger.error(f"     * product_id: {product_id} (类型: {type(product_id)})")
                logger.error(f"     * title: {title} (类型: {type(title)})")
                logger.error(f"     * price: {sale_price} (类型: {type(sale_price)})")
                logger.error(f"     * currency: {currency} (类型: {type(currency)})")
                raise create_error

        except Exception as e:
            logger.error(f"💥 [数据解析] 解析PDP产品数据异常: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return None

    async def _parse_shop_html_product_data(self, shop_html: str, original_url: str, filter_conditions: Dict[str, Any] = None) -> Optional[Product]:
        """从shop页面HTML中解析产品数据，支持分页获取"""
        try:
            logger.info(f"📦 [HTML解析] 开始从shop页面HTML解析产品数据")
            logger.debug(f"📄 [HTML解析] HTML内容长度: {len(shop_html)} 字符")

            # 调试模式：保存HTML内容到文件
            if len(shop_html) > 0:
                try:
                    import os
                    debug_dir = "debug_html"
                    if not os.path.exists(debug_dir):
                        os.makedirs(debug_dir)

                    import time
                    timestamp = int(time.time())
                    debug_file = os.path.join(debug_dir, f"shop_html_{timestamp}.html")

                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(shop_html)

                    logger.info(f"🔍 [调试] HTML内容已保存到: {debug_file}")
                except Exception as save_error:
                    logger.debug(f"⚠️ [调试] HTML保存失败: {str(save_error)}")

            # 检测是否是验证码页面
            if self._detect_captcha_in_html(shop_html):
                logger.warning(f"🚫 [HTML解析] 检测到验证码页面，跳过解析")
                return None

            # 使用BeautifulSoup解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(shop_html, 'html.parser')
            logger.info(f"✅ [HTML解析] BeautifulSoup解析完成")

            # 查找包含产品数据的script标签
            logger.info(f"🔍 [HTML解析] 查找包含产品数据的script标签")
            script_tags = soup.find_all('script')
            logger.info(f"📊 [HTML解析] 找到 {len(script_tags)} 个script标签")

            # 统计script标签类型
            json_scripts = 0
            product_scripts = 0
            for script in script_tags:
                if script.string:
                    content = script.string.strip()
                    if content and (content.startswith('{') or '"' in content):
                        json_scripts += 1
                        if any(keyword in content.lower() for keyword in ['product', 'products', 'product_id']):
                            product_scripts += 1

            logger.info(f"📊 [HTML解析] Script统计: 总数={len(script_tags)}, JSON类型={json_scripts}, 包含产品数据={product_scripts}")

            for i, script in enumerate(script_tags):
                if script.string:
                    script_content = script.string.strip()

                    # 记录每个script的基本信息
                    logger.debug(f"📋 [HTML解析] Script {i+1}: 长度={len(script_content)}, 前100字符={script_content[:100]}...")

                    # 扩展产品数据搜索模式
                    product_patterns = [
                        ('"products":[', 'product_id'),  # 原始模式
                        ('"productList":[', 'productId'),  # 可能的变体
                        ('"items":[', 'product_id'),  # 另一种可能
                        ('product_id', 'title'),  # 单个产品
                        ('productId', 'name'),  # 另一种单个产品格式
                    ]

                    found_pattern = None
                    for pattern_name, (pattern1, pattern2) in enumerate(product_patterns):
                        if pattern1 in script_content and pattern2 in script_content:
                            found_pattern = pattern_name
                            logger.info(f"🎯 [HTML解析] 在第{i+1}个script中发现产品数据模式{pattern_name+1}: {pattern1} + {pattern2}")
                            break

                    if found_pattern is not None:
                        logger.debug(f"📝 [HTML解析] script内容长度: {len(script_content)} 字符")

                        try:
                            # 查找JSON对象
                            json_start = script_content.find('{')
                            json_end = script_content.rfind('}') + 1

                            if json_start != -1 and json_end > json_start:
                                json_content = script_content[json_start:json_end]
                                logger.debug(f"📋 [HTML解析] 提取的JSON内容: {json_content[:300]}...")

                                data = json.loads(json_content)
                                logger.info(f"✅ [HTML解析] JSON解析成功")

                                # 查找products数组和分页信息
                                products = self._extract_products_from_json(data)
                                pagination_info = self._extract_pagination_info(data)

                                if products:
                                    logger.info(f"📦 [HTML解析] 找到 {len(products)} 个产品")

                                    # 检查是否需要分页获取更多产品
                                    all_products = await self._get_paginated_products(
                                        products, pagination_info, original_url, filter_conditions
                                    )

                                    logger.info(f"📦 [分页获取] 总共获取到 {len(all_products)} 个产品")

                                    # 解析所有产品并存储到临时存储（实现两步提取过程）
                                    logger.info(f"📦 [HTML解析] 开始解析 {len(all_products)} 个产品")

                                    # 使用并发处理替代串行处理
                                    logger.info(f"🚀 [并发优化] 使用并发方式处理 {len(all_products)} 个产品")

                                    # 定义进度回调函数
                                    def progress_callback(completed, total):
                                        progress = (completed / total) * 100
                                        logger.info(f"📊 [并发进度] {completed}/{total} ({progress:.1f}%) 完成")

                                    # 批量并发处理产品（传递过滤条件）
                                    processed_products = await self.batch_get_product_details_concurrent(
                                        all_products, progress_callback, filter_conditions
                                    )

                                    parsed_count = len(processed_products)

                                    logger.info(f"🎉 [HTML解析] 成功解析 {parsed_count}/{len(all_products)} 个产品（包含运费信息）")

                                    # 为了兼容现有代码，仍然返回第一个产品的Product对象
                                    if parsed_count > 0:
                                        first_product_data = all_products[0]
                                        try:
                                            product = self._parse_tiktok_product_data(first_product_data, original_url)
                                            if product:
                                                logger.info(f"🎉 [HTML解析] 成功解析TikTok产品数据")
                                                return product
                                        except Exception as product_parse_error:
                                            logger.error(f"💥 [HTML解析] 产品数据解析异常: {str(product_parse_error)}")
                                            # 继续尝试其他方法

                        except json.JSONDecodeError as json_error:
                            logger.debug(f"⚠️ [HTML解析] JSON解析失败: {str(json_error)}")
                            continue
                        except Exception as parse_error:
                            logger.debug(f"⚠️ [HTML解析] 解析script内容失败: {str(parse_error)}")
                            continue

            # 如果没有找到产品数据，尝试Schema.org格式
            logger.info(f"🔍 [HTML解析] 未找到TikTok格式产品数据，尝试Schema.org格式")
            return self._parse_schema_org_products(soup, original_url)

        except Exception as e:
            logger.error(f"💥 [HTML解析] 解析shop页面HTML失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return None

    def _extract_products_from_json(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从JSON数据中递归查找products数组"""
        try:
            products = []

            def find_products_recursive(obj, path=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key

                        if key == "products" and isinstance(value, list):
                            logger.info(f"🔍 [JSON解析] 在路径 '{current_path}' 找到products数组，包含 {len(value)} 个产品")
                            products.extend(value)
                        else:
                            find_products_recursive(value, current_path)

                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        find_products_recursive(item, f"{path}[{i}]")

            find_products_recursive(data)
            return products

        except Exception as e:
            logger.error(f"💥 [JSON解析] 查找products数组失败: {str(e)}")
            return []

    def _extract_pagination_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """从JSON数据中提取分页信息"""
        try:
            pagination_info = {}

            def find_pagination_recursive(obj, path=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key

                        # 查找load_more_params
                        if key == "load_more_params" and isinstance(value, dict):
                            logger.info(f"🔍 [分页解析] 在路径 '{current_path}' 找到load_more_params")
                            pagination_info.update(value)

                        # 查找seller_id
                        elif key == "seller_id" and isinstance(value, str):
                            pagination_info["seller_id"] = value
                            logger.info(f"🔍 [分页解析] 找到seller_id: {value}")

                        # 查找shop_name或store_name
                        elif key in ["shop_name", "store_name"] and isinstance(value, str):
                            pagination_info["shop_name"] = value
                            logger.info(f"🔍 [分页解析] 找到shop_name: {value}")

                        else:
                            find_pagination_recursive(value, current_path)

                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        find_pagination_recursive(item, f"{path}[{i}]")

            find_pagination_recursive(data)

            # 如果没有找到shop_name，尝试从URL中提取
            if "shop_name" not in pagination_info:
                # 这个方法稍后会在_get_paginated_products中处理
                pass

            logger.info(f"📋 [分页解析] 提取到分页信息: {list(pagination_info.keys())}")

            # 详细记录提取到的关键信息
            if pagination_info:
                logger.info(f"📋 [分页解析] 详细信息:")
                for key, value in pagination_info.items():
                    if isinstance(value, str) and len(value) > 100:
                        logger.info(f"   - {key}: {value[:100]}...")
                    else:
                        logger.info(f"   - {key}: {value}")
            else:
                logger.warning(f"⚠️ [分页解析] 未找到任何分页信息")

            return pagination_info

        except Exception as e:
            logger.error(f"💥 [分页解析] 提取分页信息失败: {str(e)}")
            return {}

    async def _get_paginated_products(self, initial_products: List[Dict[str, Any]], pagination_info: Dict[str, Any],
                                    original_url: str, filter_conditions: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取分页产品数据（移除数量限制，基于销量条件停止）"""
        try:
            # 获取销量过滤条件
            sales_min_threshold = 0
            if filter_conditions:
                sales_min_threshold = filter_conditions.get('sales_min', 0)
            elif hasattr(self, 'current_filter_conditions') and self.current_filter_conditions:
                sales_min_threshold = self.current_filter_conditions.get('sales_min', 0)

            logger.info(f"📊 [分页获取] 开始无限制分页获取，销量阈值: {sales_min_threshold}")
            logger.info(f"📊 [分页获取] 初始获取商品数量: {len(initial_products)}")
            logger.info(f"📊 [分页获取] 分页信息状态: {bool(pagination_info)}")
            if pagination_info:
                logger.info(f"📊 [分页获取] 分页信息内容: {list(pagination_info.keys())}")
            else:
                logger.warning(f"⚠️ [分页获取] 分页信息为空，无法执行分页")

            all_products = initial_products.copy()

            # 检查是否有分页信息
            if not pagination_info:
                logger.info(f"📊 [分页获取] 无分页信息，返回初始 {len(all_products)} 个产品")
                return all_products

            # 提取分页所需的信息
            seller_id = pagination_info.get('seller_id')
            shop_name = pagination_info.get('shop_name')
            search_params = pagination_info.get('search_params')

            # 如果没有shop_name，尝试从URL中提取
            if not shop_name:
                import re
                shop_match = re.search(r'/shop/store/([^/]+)/', original_url)
                if shop_match:
                    shop_name = shop_match.group(1)
                    logger.info(f"🔍 [分页获取] 从URL提取shop_name: {shop_name}")

            if not seller_id or not shop_name:
                logger.warning(f"⚠️ [分页获取] 缺少必要的分页信息 - seller_id: {seller_id}, shop_name: {shop_name}")
                return all_products

            logger.info(f"📋 [分页获取] 分页参数 - seller_id: {seller_id}, shop_name: {shop_name}")

            # 收集已获取的产品ID，用于exclude_product_ids
            exclude_product_ids = []
            for product in all_products:
                product_id = str(product.get('product_id', ''))
                if product_id:
                    exclude_product_ids.append(product_id)

            # 执行无限制分页请求，直到遇到低销量商品或无更多数据
            page_count = 0
            max_pages = 100  # 设置一个合理的上限防止无限循环

            logger.info(f"📊 [分页获取] 开始无限制分页，销量阈值: {sales_min_threshold}")

            while page_count < max_pages:
                page_count += 1
                logger.info(f"📄 [分页获取] 执行第 {page_count} 次分页请求")

                try:
                    page_products = await self._fetch_page_products(
                        shop_name, seller_id, search_params, exclude_product_ids
                    )

                    if page_products:
                        logger.info(f"📦 [分页获取] 第 {page_count} 页获取到 {len(page_products)} 个产品")

                        # 检查新获取的产品是否满足销量条件
                        should_stop = False
                        for product in page_products:
                            sold_count = product.get('sold_count', 0)
                            try:
                                sold_count = int(sold_count) if sold_count else 0
                            except (ValueError, TypeError):
                                sold_count = 0

                            if sold_count < sales_min_threshold:
                                logger.info(f"🛑 [分页获取] 发现销量低于阈值的商品: sold_count={sold_count} < {sales_min_threshold}，停止分页")
                                should_stop = True
                                break

                        all_products.extend(page_products)

                        # 更新exclude_product_ids
                        for product in page_products:
                            product_id = str(product.get('product_id', ''))
                            if product_id:
                                exclude_product_ids.append(product_id)

                        # 如果遇到低销量商品，停止分页
                        if should_stop:
                            break
                    else:
                        logger.warning(f"⚠️ [分页获取] 第 {page_count} 页未获取到产品，停止分页")
                        break

                except Exception as page_error:
                    logger.error(f"❌ [分页获取] 第 {page_count} 页请求失败: {str(page_error)}")
                    # 继续尝试下一页
                    continue

            logger.info(f"🎉 [分页获取] 分页获取完成，总共获取 {len(all_products)} 个产品")
            return all_products

        except Exception as e:
            logger.error(f"💥 [分页获取] 分页获取失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return initial_products

    async def _fetch_page_products(self, shop_name: str, seller_id: str, search_params: str,
                                 exclude_product_ids: List[str]) -> List[Dict[str, Any]]:
        """获取单页产品数据"""
        try:
            logger.info(f"📄 [单页获取] 开始获取分页产品数据")
            logger.info(f"   - shop_name: {shop_name}")
            logger.info(f"   - seller_id: {seller_id}")
            logger.info(f"   - exclude_product_ids数量: {len(exclude_product_ids)}")

            # 构造POST请求URL
            base_url = f"https://www.tiktok.com/api/shop/{shop_name}/store/product_list"

            # 构造请求参数（这些参数通常需要从页面中提取或生成）
            params = {
                "msToken": "G6FAyBc0xG5rJUbOLZDMy6Gxc6uDp6MB-mtN6_ayraMqftCkA2gVfnPFRALlaUgOcWJJ71B5RzJ-0OxxTSiixdN-4rjxqtvwJaOCYNRGqjHvzjuahJ_OEyZN63EK",
                "X-Bogus": "DFSzswSuL5DLutcMCSO3rmRyMCGp",
                "_signature": "_02B4Z6wo00001EUeykAAAIDAq72XCtTwzFBFHs7AAHlB64"
            }

            # 构造请求负载
            component_info = {
                "component_type": "feed_list",
                "component_name": "feed_list_store_list_product",
                "fe_config": {
                    "is_fmp_significant": False,
                    "show_structure_data": True,
                    "show_empty_page": True,
                    "empty_text": {
                        "title": "No results found",
                        "sub_title": "Try another search"
                    },
                    "data_source": {
                        "type": "store_list_product",
                        "params": {
                            "count": 30,
                            "clamp": False
                        }
                    }
                }
            }

            load_more_params = {
                "search_params": search_params or "30_WzE1MywzNjg5MDAwMCwxNzI2OTIxMzI0Nzg4LCIxNzI5NjU2MTQ5MDUyMDAyNjQ1Il0=",
                "seller_id": seller_id,
                "search_word": shop_name,
                "exclude_product_ids": exclude_product_ids
            }

            payload = {
                "component_info": json.dumps(component_info),
                "load_more_params": json.dumps(load_more_params)
            }

            logger.info(f"📋 [单页获取] 构造POST请求")
            logger.info(f"   - URL: {base_url}")
            logger.debug(f"   - Payload: {payload}")

            # 获取增强的Cookie
            enhanced_cookie = self.get_tiktok_session_cookie()
            logger.info(f"🍪 [单页获取] 使用增强Cookie，长度: {len(enhanced_cookie) if enhanced_cookie else 0}")

            # 构造增强的请求头（模拟真实浏览器）
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': self.get_random_user_agent(),
                'Referer': f'https://www.tiktok.com/shop/store/{shop_name}/{seller_id}',
                'Origin': 'https://www.tiktok.com',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest',
            }

            # 添加Cookie到请求头
            if enhanced_cookie:
                headers['Cookie'] = enhanced_cookie

            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    base_url,
                    params=params,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    logger.info(f"📡 [单页获取] POST请求响应状态: {response.status}")

                    if response.status == 200:
                        response_data = await response.json()
                        logger.info(f"✅ [单页获取] 成功获取响应数据")
                        logger.info(f"{response_data}")

                        # 检测验证码或反爬虫响应
                        if self._detect_captcha_or_verification(response_data):
                            logger.warning(f"🚫 [单页获取] 检测到验证码或反爬虫响应")
                            return []

                        # 检测URL不匹配错误
                        if self._detect_url_mismatch_error(response_data):
                            logger.warning(f"⚠️ [单页获取] 检测到URL不匹配错误，可能需要调整请求参数")
                            return []

                        # 从响应中提取产品数据
                        products = self._extract_products_from_response(response_data)
                        logger.info(f"📦 [单页获取] 从响应中提取到 {len(products)} 个产品")

                        return products
                    else:
                        logger.error(f"❌ [单页获取] POST请求失败，状态码: {response.status}")
                        response_text = await response.text()
                        logger.error(f"   - 响应内容: {response_text[:500]}...")

                        # 检测是否是验证码页面
                        if self._detect_captcha_in_html(response_text):
                            logger.warning(f"🚫 [单页获取] 响应包含验证码页面")

                        return []

        except Exception as e:
            logger.error(f"💥 [单页获取] 获取单页产品数据失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return []

    def _extract_products_from_response(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从分页API响应中提取产品数据"""
        try:
            logger.info(f"📦 [响应解析] 开始从API响应中提取产品数据")
            logger.info(f"{response_data}")

            products = []

            # 尝试多种可能的数据结构
            possible_paths = [
                ['data', 'products'],
                ['products'],
                ['data', 'items'],
                ['items'],
                ['data', 'list'],
                ['list'],
                ['data', 'product_list'],
                ['product_list']
            ]

            for path in possible_paths:
                try:
                    current_data = response_data
                    for key in path:
                        if isinstance(current_data, dict) and key in current_data:
                            current_data = current_data[key]
                        else:
                            current_data = None
                            break

                    if current_data and isinstance(current_data, list):
                        logger.info(f"🎯 [响应解析] 在路径 {' -> '.join(path)} 找到产品数组，包含 {len(current_data)} 个产品")
                        products.extend(current_data)
                        break

                except Exception as path_error:
                    logger.debug(f"⚠️ [响应解析] 路径 {' -> '.join(path)} 解析失败: {str(path_error)}")
                    continue

            # 如果上述路径都没找到，尝试递归搜索
            if not products:
                logger.info(f"🔍 [响应解析] 使用递归搜索查找产品数据")
                products = self._extract_products_from_json(response_data)

            logger.info(f"📊 [响应解析] 最终提取到 {len(products)} 个产品")
            return products

        except Exception as e:
            logger.error(f"💥 [响应解析] 从API响应提取产品数据失败: {str(e)}")
            return []

    def _parse_tiktok_product_data(self, product_data: Dict[str, Any], original_url: str) -> Optional[Product]:
        """解析TikTok格式的产品数据"""
        try:
            logger.info(f"📦 [产品解析] 开始解析TikTok产品数据")
            logger.debug(f"📊 [产品解析] 产品数据结构: {list(product_data.keys()) if isinstance(product_data, dict) else type(product_data)}")

            # 基本产品信息
            product_id = str(product_data.get('product_id', ''))
            title = product_data.get('title', '')

            logger.info(f"🆔 [产品解析] 产品ID: {product_id}")
            logger.info(f"📋 [产品解析] 产品标题: {title}")

            # 图片信息
            images = []
            image_data = product_data.get('image', {})
            if isinstance(image_data, dict):
                url_list = image_data.get('url_list', [])
                if url_list:
                    # 解码Unicode转义字符
                    import codecs
                    for url in url_list:
                        try:
                            decoded_url = codecs.decode(url, 'unicode_escape')
                            images.append(decoded_url)
                        except Exception:
                            images.append(url)  # 如果解码失败，使用原始URL

                    logger.info(f"🖼️ [产品解析] 找到 {len(images)} 张图片")

            # 价格信息
            price = 0.0
            currency = 'USD'
            price_info = product_data.get('product_price_info', {})
            if isinstance(price_info, dict):
                # 获取售价
                sale_price = price_info.get('sale_price_decimal', '0')
                if isinstance(sale_price, str):
                    try:
                        price = float(sale_price)
                        logger.info(f"💰 [产品解析] 售价: {price}")
                    except (ValueError, TypeError):
                        logger.warning(f"⚠️ [产品解析] 价格解析失败: {sale_price}")

                # 获取货币
                currency = price_info.get('currency_name', currency)
                logger.info(f"💱 [产品解析] 货币: {currency}")

            # 评分信息
            rating = 0.0
            review_count = 0
            rate_info = product_data.get('rate_info', {})
            if isinstance(rate_info, dict):
                score = rate_info.get('score', 0)
                if isinstance(score, (int, float)):
                    rating = float(score)
                    logger.info(f"⭐ [产品解析] 评分: {rating}")

                review_count_str = rate_info.get('review_count', '0')
                if isinstance(review_count_str, str):
                    try:
                        review_count = int(review_count_str)
                        logger.info(f"📝 [产品解析] 评论数: {review_count}")
                    except (ValueError, TypeError):
                        pass

            # 销售信息
            sold_count = 0
            sold_info = product_data.get('sold_info', {})
            if isinstance(sold_info, dict):
                sold_count = sold_info.get('sold_count', 0)
                if isinstance(sold_count, (int, str)):
                    try:
                        sold_count = int(sold_count)
                        logger.info(f"🛒 [产品解析] 销售数量: {sold_count}")
                    except (ValueError, TypeError):
                        sold_count = 0

            # 店铺信息
            shop_name = ''
            seller_info = product_data.get('seller_info', {})
            if isinstance(seller_info, dict):
                shop_name = seller_info.get('shop_name', '')
                logger.info(f"🏪 [产品解析] 店铺名称: {shop_name}")

            # 构建产品URL
            product_url = original_url
            seo_url_data = product_data.get('seo_url', {})
            if isinstance(seo_url_data, dict):
                canonical_url = seo_url_data.get('canonical_url', '')
                if canonical_url:
                    # 解码Unicode转义字符
                    import codecs
                    try:
                        product_url = codecs.decode(canonical_url, 'unicode_escape')
                        logger.info(f"🔗 [产品解析] 产品URL: {product_url}")
                    except Exception:
                        product_url = canonical_url

            # 创建Product对象
            from src.models.product import Product, ProductStatus, ProductRating, ProductImage, Currency
            from decimal import Decimal

            # 创建评分对象
            product_rating = ProductRating(
                average_rating=rating,
                total_reviews=review_count
            )

            # 创建图片对象列表
            product_images = []
            for i, img_url in enumerate(images):
                product_image = ProductImage(
                    url=img_url,
                    is_main=(i == 0),  # 第一张图片设为主图
                    order=i
                )
                product_images.append(product_image)

            # 转换货币
            try:
                currency_enum = Currency(currency)
            except ValueError:
                currency_enum = Currency.USD  # 默认使用USD

            product = Product(
                product_id=product_id,
                title=title,
                description='',  # TikTok数据中没有单独的描述字段
                category='',  # TikTok数据中没有分类信息
                price=Decimal(str(price)),
                currency=currency_enum,
                images=product_images,
                product_url=product_url,
                shop_name=shop_name,
                rating=product_rating,
                status=ProductStatus.ACTIVE,
                sold_count=sold_count
            )

            logger.info(f"🎉 [产品解析] 成功创建Product对象")
            logger.info(f"   - ID: {product.product_id}")
            logger.info(f"   - 标题: {product.title[:50]}...")
            logger.info(f"   - 价格: {product.price} {product.currency}")
            logger.info(f"   - 评分: {product.rating.average_rating} ({product.rating.total_reviews} 评论)")
            logger.info(f"   - 销量: {product.sold_count}")
            logger.info(f"   - 图片数量: {len(product.images)}")

            return product

        except Exception as e:
            logger.error(f"💥 [产品解析] 解析TikTok产品数据失败: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            return None

    def _parse_schema_org_products(self, soup, original_url: str) -> Optional[Product]:
        """解析Schema.org格式的产品数据（备用方法）"""
        try:
            logger.info(f"📦 [Schema解析] 开始解析Schema.org产品数据")

            # 查找Schema.org的ItemList
            script_tags = soup.find_all('script', type='application/ld+json')
            logger.info(f"📊 [Schema解析] 找到 {len(script_tags)} 个LD+JSON script标签")

            # 如果没有找到LD+JSON，尝试查找其他可能的JSON数据
            if len(script_tags) == 0:
                logger.warning(f"⚠️ [Schema解析] 未找到LD+JSON标签，尝试查找其他JSON数据")
                all_scripts = soup.find_all('script')
                for i, script in enumerate(all_scripts):
                    if script.string and script.string.strip():
                        content = script.string.strip()
                        if content.startswith('{') and '@type' in content:
                            logger.info(f"🔍 [Schema解析] 在普通script {i+1}中发现可能的Schema.org数据")
                            script_tags.append(script)

            for i, script in enumerate(script_tags):
                if script.string:
                    try:
                        data = json.loads(script.string)
                        logger.debug(f"📋 [Schema解析] 第{i+1}个script解析成功")

                        # 检查是否是ItemList
                        if isinstance(data, dict) and data.get('@type') == 'ItemList':
                            logger.info(f"🎯 [Schema解析] 发现ItemList数据")

                            item_list = data.get('itemListElement', [])
                            if item_list and len(item_list) > 0:
                                first_item = item_list[0]
                                logger.info(f"🔍 [Schema解析] 解析第一个产品")

                                return self._parse_schema_product_data(first_item, original_url)

                        # 检查是否是单个Product
                        elif isinstance(data, dict) and data.get('@type') == 'Product':
                            logger.info(f"🎯 [Schema解析] 发现单个Product数据")
                            return self._parse_schema_product_data(data, original_url)

                    except json.JSONDecodeError as e:
                        logger.debug(f"⚠️ [Schema解析] 第{i+1}个script JSON解析失败: {str(e)}")
                        continue

            logger.warning(f"❌ [Schema解析] 未找到Schema.org产品数据")
            return None

        except Exception as e:
            logger.error(f"💥 [Schema解析] 解析Schema.org产品数据失败: {str(e)}")
            return None

    def _parse_schema_product_data(self, schema_data: Dict[str, Any], original_url: str) -> Optional[Product]:
        """解析Schema.org格式的单个产品数据"""
        try:
            logger.info(f"📦 [Schema解析] 开始解析Schema.org产品数据")

            # 基本产品信息
            product_name = schema_data.get('name', '')
            product_url = schema_data.get('url', original_url)
            description = schema_data.get('description', '')

            logger.info(f"📋 [Schema解析] 产品名称: {product_name}")

            # 提取产品ID（从URL中提取）
            product_id = ''
            if product_url:
                import re
                id_match = re.search(r'/(\d+)/?$', product_url)
                if id_match:
                    product_id = id_match.group(1)
                    logger.info(f"🆔 [Schema解析] 提取到产品ID: {product_id}")

            # 价格信息
            price_info = schema_data.get('offers', {})
            if isinstance(price_info, list) and price_info:
                price_info = price_info[0]

            price = 0.0
            currency = 'USD'
            if isinstance(price_info, dict):
                price_value = price_info.get('price', price_info.get('lowPrice', 0))
                if isinstance(price_value, (str, int, float)):
                    try:
                        price = float(price_value)
                        logger.info(f"💰 [Schema解析] 价格: {price}")
                    except (ValueError, TypeError):
                        pass

                currency = price_info.get('priceCurrency', currency)

            # 图片信息
            images = []
            image_data = schema_data.get('image', [])
            if isinstance(image_data, str):
                images = [image_data]
            elif isinstance(image_data, list):
                images = image_data

            logger.info(f"🖼️ [Schema解析] 找到 {len(images)} 张图片")

            # 评分信息
            rating = 0.0
            review_count = 0
            rating_data = schema_data.get('aggregateRating', {})
            if isinstance(rating_data, dict):
                rating_value = rating_data.get('ratingValue', 0)
                if isinstance(rating_value, (str, int, float)):
                    try:
                        rating = float(rating_value)
                        logger.info(f"⭐ [Schema解析] 评分: {rating}")
                    except (ValueError, TypeError):
                        pass

                review_count_value = rating_data.get('reviewCount', 0)
                if isinstance(review_count_value, (str, int)):
                    try:
                        review_count = int(review_count_value)
                        logger.info(f"📝 [Schema解析] 评论数: {review_count}")
                    except (ValueError, TypeError):
                        pass

            # 品牌信息
            brand = ''
            brand_data = schema_data.get('brand', {})
            if isinstance(brand_data, dict):
                brand = brand_data.get('name', '')
            elif isinstance(brand_data, str):
                brand = brand_data

            # 创建Product对象
            from src.models.product import Product, ProductStatus, ProductRating, ProductImage, Currency
            from decimal import Decimal

            # 创建评分对象
            product_rating = ProductRating(
                average_rating=rating,
                total_reviews=review_count
            )

            # 创建图片对象列表
            product_images = []
            for i, img_url in enumerate(images):
                product_image = ProductImage(
                    url=img_url,
                    is_main=(i == 0),
                    order=i
                )
                product_images.append(product_image)

            # 转换货币
            try:
                currency_enum = Currency(currency)
            except ValueError:
                currency_enum = Currency.USD

            product = Product(
                product_id=product_id,
                title=product_name,
                description=description,
                category='',
                price=Decimal(str(price)),
                currency=currency_enum,
                images=product_images,
                product_url=product_url,
                shop_name=brand,
                rating=product_rating,
                status=ProductStatus.ACTIVE
            )

            logger.info(f"🎉 [Schema解析] 成功创建Product对象")
            return product

        except Exception as e:
            logger.error(f"💥 [Schema解析] 解析Schema.org产品数据失败: {str(e)}")
            return None

    def _detect_captcha_or_verification(self, response_data: Dict[str, Any]) -> bool:
        """检测响应中是否包含验证码或反爬虫验证"""
        try:
            # 检查常见的验证码响应模式
            captcha_indicators = [
                'captcha',
                'verification',
                'slider',
                'challenge',
                'robot',
                'verify',
                'blocked',
                'forbidden',
                'access_denied',
                'rate_limit',
                'too_many_requests'
            ]

            # 转换响应为字符串进行检查
            response_str = str(response_data).lower()

            for indicator in captcha_indicators:
                if indicator in response_str:
                    logger.warning(f"🚫 [验证码检测] 发现验证码指示器: {indicator}")
                    return True

            # 检查特定的TikTok错误响应
            if isinstance(response_data, dict):
                status_msg = response_data.get('status_msg', '').lower()
                if any(msg in status_msg for msg in ['url doesn\'t match', 'access denied', 'forbidden']):
                    logger.warning(f"🚫 [验证码检测] TikTok错误响应: {status_msg}")
                    return True

                # 检查状态码
                status_code = response_data.get('status_code', 0)
                if status_code != 0:
                    logger.warning(f"🚫 [验证码检测] 非正常状态码: {status_code}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"⚠️ [验证码检测] 检测过程异常: {str(e)}")
            return False

    def _detect_url_mismatch_error(self, response_data: Dict[str, Any]) -> bool:
        """检测URL不匹配错误"""
        try:
            if isinstance(response_data, dict):
                status_msg = response_data.get('status_msg', '')
                if "url doesn't match" in status_msg.lower():
                    logger.warning(f"⚠️ [URL检测] URL不匹配错误: {status_msg}")
                    return True
            return False
        except:
            return False

    def _detect_captcha_in_html(self, html_content: str) -> bool:
        """检测HTML内容中是否包含验证码"""
        try:
            if not html_content:
                return False

            html_lower = html_content.lower()

            # HTML验证码指示器
            html_captcha_indicators = [
                'captcha',
                'slider',
                'verification',
                'challenge',
                'robot check',
                'access denied',
                'blocked',
                'forbidden',
                'rate limit',
                'verify you are human',
                'security check',
                'unusual traffic'
            ]

            for indicator in html_captcha_indicators:
                if indicator in html_lower:
                    logger.warning(f"🚫 [HTML验证码检测] 发现验证码指示器: {indicator}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"⚠️ [HTML验证码检测] 检测过程异常: {str(e)}")
            return False
