"""
网络拦截爬虫管理器
集成网络请求拦截功能的完整爬虫解决方案
支持自动点击"加载更多"按钮并捕获API响应数据
"""

import time
from typing import Dict, List, Any, Optional
from pathlib import Path
from loguru import logger

from ..utils.enhanced_browser_manager import EnhancedBrowserManager
from ..utils.intercepted_data_processor import InterceptedDataProcessor
from ..utils.memory_storage import MemoryStorage


class NetworkCrawlerManager:
    """网络拦截爬虫管理器"""
    
    def __init__(self, memory_storage: Optional[MemoryStorage] = None, 
                 output_dir: Optional[Path] = None):
        """
        初始化网络拦截爬虫管理器
        
        Args:
            memory_storage: 内存存储实例，如果为None则创建新实例
            output_dir: 网络数据输出目录
        """
        self.memory_storage = memory_storage or MemoryStorage()
        self.browser_manager = EnhancedBrowserManager(output_dir)
        self.data_processor = InterceptedDataProcessor(self.memory_storage)
        
        # 爬取统计
        self.crawl_stats = {
            'pages_crawled': 0,
            'load_more_clicks': 0,
            'api_responses_captured': 0,
            'products_extracted': 0,
            'errors': 0
        }
        
        logger.info("🚀 [网络拦截爬虫] 初始化完成")
    
    def setup_crawler(self, enable_interception: bool = True) -> bool:
        """
        设置爬虫环境
        
        Args:
            enable_interception: 是否启用网络拦截
            
        Returns:
            bool: 设置是否成功
        """
        try:
            logger.info("🔧 [网络拦截爬虫] 设置爬虫环境...")
            
            # 设置浏览器
            success = self.browser_manager.setup_browser_with_interception(enable_interception)
            if not success:
                logger.error("❌ [网络拦截爬虫] 浏览器设置失败")
                return False
            
            # 添加TikTok Shop特定的拦截模式
            self.browser_manager.add_interception_pattern("product_list")
            self.browser_manager.add_interception_pattern("brandy_desktop/store")
            self.browser_manager.add_interception_pattern("api/shop")
            
            # 添加响应处理回调
            self.browser_manager.add_response_callback(self._on_response_intercepted)
            
            logger.info("✅ [网络拦截爬虫] 爬虫环境设置完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ [网络拦截爬虫] 设置爬虫环境失败: {str(e)}")
            return False
    
    def _on_response_intercepted(self, response_data: Dict[str, Any]):
        """响应拦截回调函数"""
        try:
            url = response_data.get('url', '')
            logger.debug(f"🎯 [网络拦截爬虫] 拦截到响应: {url}")
            self.crawl_stats['api_responses_captured'] += 1
            
        except Exception as e:
            logger.error(f"❌ [网络拦截爬虫] 响应回调处理失败: {str(e)}")
    
    def crawl_shop_with_load_more(self, shop_url: str, max_load_more: int = 5, 
                                 wait_time: int = 3) -> Dict[str, Any]:
        """
        爬取店铺页面并自动点击"加载更多"按钮
        
        Args:
            shop_url: 店铺URL
            max_load_more: 最大点击"加载更多"次数
            wait_time: 每次点击后的等待时间（秒）
            
        Returns:
            Dict: 爬取结果
        """
        try:
            logger.info(f"🕷️ [网络拦截爬虫] 开始爬取店铺: {shop_url}")
            
            # 导航到店铺页面
            self.browser_manager.navigate_to(shop_url)
            time.sleep(3)  # 等待页面加载
            
            self.crawl_stats['pages_crawled'] += 1
            
            # 开始网络监控
            self.browser_manager.start_network_monitoring()
            
            # 多次点击"加载更多"按钮
            load_more_count = 0
            for i in range(max_load_more):
                logger.info(f"🔄 [网络拦截爬虫] 尝试第 {i+1} 次点击加载更多...")
                
                # 等待按钮出现
                if not self.browser_manager.wait_for_load_more_button(timeout=10):
                    logger.info("ℹ️ [网络拦截爬虫] 未找到加载更多按钮，可能已加载完所有数据")
                    break
                
                # 点击按钮
                if self.browser_manager.click_load_more_button():
                    load_more_count += 1
                    self.crawl_stats['load_more_clicks'] += 1
                    logger.info(f"✅ [网络拦截爬虫] 成功点击加载更多 ({load_more_count}/{max_load_more})")
                    
                    # 等待数据加载
                    time.sleep(wait_time)
                else:
                    logger.warning(f"⚠️ [网络拦截爬虫] 第 {i+1} 次点击失败")
                    break
            
            # 停止监控并提取数据
            network_data = self.browser_manager.stop_and_extract_network_data()
            
            # 处理拦截的数据
            processing_results = self.data_processor.process_intercepted_responses(
                network_data['responses']
            )
            
            self.crawl_stats['products_extracted'] += processing_results['processed_products']
            self.crawl_stats['errors'] += processing_results['errors']
            
            # 保存原始网络数据
            saved_file = self.browser_manager.save_intercepted_data()
            
            results = {
                'success': True,
                'shop_url': shop_url,
                'load_more_clicks': load_more_count,
                'api_responses': len(network_data['responses']),
                'products_extracted': processing_results['processed_products'],
                'shops_extracted': processing_results['processed_shops'],
                'errors': processing_results['errors'],
                'saved_file': str(saved_file) if saved_file else None,
                'crawl_stats': self.crawl_stats.copy()
            }
            
            logger.info(f"✅ [网络拦截爬虫] 爬取完成: {results}")
            return results
            
        except Exception as e:
            logger.error(f"❌ [网络拦截爬虫] 爬取失败: {str(e)}")
            self.crawl_stats['errors'] += 1
            
            return {
                'success': False,
                'error': str(e),
                'shop_url': shop_url,
                'crawl_stats': self.crawl_stats.copy()
            }
    
    def crawl_multiple_shops(self, shop_urls: List[str], max_load_more: int = 5) -> List[Dict[str, Any]]:
        """
        批量爬取多个店铺
        
        Args:
            shop_urls: 店铺URL列表
            max_load_more: 每个店铺最大点击"加载更多"次数
            
        Returns:
            List[Dict]: 每个店铺的爬取结果
        """
        results = []
        
        logger.info(f"🚀 [网络拦截爬虫] 开始批量爬取 {len(shop_urls)} 个店铺")
        
        for i, shop_url in enumerate(shop_urls):
            logger.info(f"📍 [网络拦截爬虫] 爬取进度: {i+1}/{len(shop_urls)}")
            
            # 清空之前的拦截数据
            self.browser_manager.clear_intercepted_data()
            
            # 爬取单个店铺
            result = self.crawl_shop_with_load_more(shop_url, max_load_more)
            results.append(result)
            
            # 短暂休息，避免请求过于频繁
            if i < len(shop_urls) - 1:
                time.sleep(2)
        
        logger.info(f"✅ [网络拦截爬虫] 批量爬取完成，总计处理 {len(results)} 个店铺")
        return results
    
    def get_crawl_statistics(self) -> Dict[str, Any]:
        """获取爬取统计信息"""
        memory_stats = self.memory_storage.get_stats()
        
        return {
            'crawl_stats': self.crawl_stats.copy(),
            'memory_storage_stats': memory_stats,
            'browser_info': self.browser_manager.get_browser_info()
        }
    
    def export_crawled_data(self, format: str = 'json', filename: Optional[str] = None) -> Optional[Path]:
        """
        导出爬取的数据
        
        Args:
            format: 导出格式 ('json', 'csv', 'excel')
            filename: 文件名
            
        Returns:
            Path: 导出文件路径
        """
        try:
            from ..utils.data_exporter import DataExporter
            
            exporter = DataExporter(self.memory_storage)
            
            if format.lower() == 'json':
                return exporter.export_products_to_json(filename)
            elif format.lower() == 'csv':
                return exporter.export_products_to_csv(filename)
            elif format.lower() == 'excel':
                return exporter.export_products_to_excel(filename)
            else:
                logger.error(f"❌ [网络拦截爬虫] 不支持的导出格式: {format}")
                return None
                
        except Exception as e:
            logger.error(f"❌ [网络拦截爬虫] 导出数据失败: {str(e)}")
            return None
    
    def reset_statistics(self):
        """重置爬取统计"""
        self.crawl_stats = {
            'pages_crawled': 0,
            'load_more_clicks': 0,
            'api_responses_captured': 0,
            'products_extracted': 0,
            'errors': 0
        }
        logger.info("🔄 [网络拦截爬虫] 统计信息已重置")
    
    def close(self):
        """关闭爬虫管理器"""
        try:
            self.browser_manager.close()
            logger.info("🔒 [网络拦截爬虫] 爬虫管理器已关闭")
            
        except Exception as e:
            logger.error(f"❌ [网络拦截爬虫] 关闭失败: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
