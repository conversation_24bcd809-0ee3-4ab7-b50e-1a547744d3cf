/* TikTok Shop Tool - 浅色主题样式 */

/* 主窗口 */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
}

/* 通用组件 */
QWidget {
    background-color: #f5f5f5;
    color: #333333;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
}

/* 按钮样式 */
QPushButton {
    background-color: #4CAF50;
    border: none;
    color: white;
    padding: 8px 16px;
    text-align: center;
    font-size: 9pt;
    font-weight: bold;
    border-radius: 4px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #45a049;
}

QPushButton:pressed {
    background-color: #3d8b40;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #666666;
}

/* 危险按钮 */
QPushButton[class="danger"] {
    background-color: #f44336;
}

QPushButton[class="danger"]:hover {
    background-color: #d32f2f;
}

/* 成功按钮 */
QPushButton[class="success"] {
    background-color: #4CAF50;
}

QPushButton[class="success"]:hover {
    background-color: #45a049;
}

/* 输入框 */
QLineEdit {
    background-color: #ffffff;
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    color: #333333;
    font-size: 9pt;
}

QLineEdit:focus {
    border-color: #4CAF50;
}

QLineEdit:disabled {
    background-color: #f0f0f0;
    color: #999999;
}

/* 文本编辑器 */
QTextEdit {
    background-color: #ffffff;
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    color: #333333;
    font-size: 9pt;
}

QTextEdit:focus {
    border-color: #4CAF50;
}

/* 组合框 */
QComboBox {
    background-color: #ffffff;
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
    color: #333333;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #4CAF50;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(resources/icons/arrow_down.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    border: 1px solid #ddd;
    selection-background-color: #4CAF50;
    color: #333333;
}

/* 数值输入框 */
QSpinBox, QDoubleSpinBox {
    background-color: #ffffff;
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 6px;
    color: #333333;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #4CAF50;
}

/* 复选框 */
QCheckBox {
    color: #333333;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-radius: 3px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #4CAF50;
    border-color: #4CAF50;
    image: url(resources/icons/check.png);
}

QCheckBox::indicator:hover {
    border-color: #4CAF50;
}

/* 单选按钮 */
QRadioButton {
    color: #333333;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-radius: 8px;
    background-color: #ffffff;
}

QRadioButton::indicator:checked {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

/* 进度条 */
QProgressBar {
    background-color: #ffffff;
    border: 2px solid #ddd;
    border-radius: 5px;
    text-align: center;
    color: #333333;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #4CAF50;
    border-radius: 3px;
}

/* 滑块 */
QSlider::groove:horizontal {
    background-color: #ddd;
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #4CAF50;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #45a049;
}

/* 表格 */
QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f9f9f9;
    gridline-color: #ddd;
    color: #333333;
    border: 1px solid #ddd;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #4CAF50;
    color: #ffffff;
}

QHeaderView::section {
    background-color: #e0e0e0;
    color: #333333;
    padding: 8px;
    border: 1px solid #ddd;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #d0d0d0;
}

/* 列表 */
QListWidget {
    background-color: #ffffff;
    border: 1px solid #ddd;
    color: #333333;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

QListWidget::item:selected {
    background-color: #4CAF50;
    color: #ffffff;
}

QListWidget::item:hover {
    background-color: #f0f0f0;
}

/* 树形视图 */
QTreeWidget {
    background-color: #ffffff;
    border: 1px solid #ddd;
    color: #333333;
}

QTreeWidget::item {
    padding: 4px;
}

QTreeWidget::item:selected {
    background-color: #4CAF50;
    color: #ffffff;
}

/* 选项卡 */
QTabWidget::pane {
    border: 1px solid #cccccc;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #e1e1e1;
    color: #333333;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    border-bottom: 2px solid #4CAF50;
}

QTabBar::tab:hover {
    background-color: #d0d0d0;
}

/* 分组框 */
QGroupBox {
    color: #333333;
    font-weight: bold;
    border: 2px solid #cccccc;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #4CAF50;
}

/* 分割器 */
QSplitter::handle {
    background-color: #cccccc;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #cccccc;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #aaaaaa;
}

QScrollBar:horizontal {
    background-color: #f0f0f0;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #cccccc;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #aaaaaa;
}

/* 菜单 */
QMenuBar {
    background-color: #ffffff;
    color: #333333;
    border-bottom: 1px solid #ddd;
}

QMenuBar::item {
    padding: 8px 12px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #4CAF50;
    color: #ffffff;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #ddd;
    color: #333333;
}

QMenu::item {
    padding: 8px 24px;
}

QMenu::item:selected {
    background-color: #4CAF50;
    color: #ffffff;
}

/* 状态栏 */
QStatusBar {
    background-color: #ffffff;
    color: #333333;
    border-top: 1px solid #ddd;
}

/* 工具提示 */
QToolTip {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #ddd;
    padding: 4px;
    border-radius: 3px;
}
