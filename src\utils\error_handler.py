"""
错误处理和异常管理
"""

import sys
import traceback
from typing import Optional, Dict, Any, Callable
from functools import wraps
from loguru import logger
from PyQt6.QtWidgets import QMessageBox, QWidget


class TikTokShopError(Exception):
    """TikTok Shop工具基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details
        }


class CrawlerError(TikTokShopError):
    """爬虫相关错误"""
    pass


class NetworkError(CrawlerError):
    """网络请求错误"""
    pass


class ParseError(CrawlerError):
    """数据解析错误"""
    pass


class ValidationError(TikTokShopError):
    """数据验证错误"""
    pass


class ConfigError(TikTokShopError):
    """配置错误"""
    pass


class DatabaseError(TikTokShopError):
    """数据库错误"""
    pass


class ExportError(TikTokShopError):
    """导出错误"""
    pass


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_callbacks: Dict[str, Callable] = {}
        self.setup_exception_hook()
    
    def setup_exception_hook(self):
        """设置全局异常钩子"""
        def exception_hook(exc_type, exc_value, exc_traceback):
            """全局异常处理"""
            if issubclass(exc_type, KeyboardInterrupt):
                # 用户中断，正常退出
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            # 记录异常
            logger.error(
                "未捕获的异常",
                exc_info=(exc_type, exc_value, exc_traceback)
            )
            
            # 显示错误对话框
            try:
                error_msg = str(exc_value)
                if not error_msg:
                    error_msg = f"{exc_type.__name__}: 未知错误"
                
                QMessageBox.critical(
                    None,
                    "程序错误",
                    f"程序发生未处理的错误:\n\n{error_msg}\n\n"
                    f"详细信息已记录到日志文件中。\n"
                    f"如果问题持续存在，请联系技术支持。"
                )
            except:
                # 如果GUI不可用，输出到控制台
                print(f"Critical Error: {exc_type.__name__}: {exc_value}")
            
            # 调用原始异常钩子
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
        
        sys.excepthook = exception_hook
    
    def register_callback(self, error_type: str, callback: Callable):
        """注册错误回调"""
        self.error_callbacks[error_type] = callback
    
    def handle_error(self, error: Exception, context: Optional[str] = None, 
                    show_dialog: bool = True, parent: Optional[QWidget] = None) -> bool:
        """处理错误"""
        try:
            # 记录错误
            context_msg = f" (上下文: {context})" if context else ""
            logger.error(f"错误处理{context_msg}: {str(error)}", exc_info=True)
            
            # 获取错误信息
            error_info = self._get_error_info(error)
            
            # 调用注册的回调
            error_type = error.__class__.__name__
            if error_type in self.error_callbacks:
                try:
                    self.error_callbacks[error_type](error, context)
                except Exception as callback_error:
                    logger.error(f"错误回调执行失败: {callback_error}")
            
            # 显示错误对话框
            if show_dialog:
                self._show_error_dialog(error_info, parent)
            
            return True
            
        except Exception as handler_error:
            logger.error(f"错误处理器本身发生错误: {handler_error}")
            return False
    
    def _get_error_info(self, error: Exception) -> Dict[str, Any]:
        """获取错误信息"""
        if isinstance(error, TikTokShopError):
            return error.to_dict()
        
        return {
            "error_type": error.__class__.__name__,
            "error_code": "SYSTEM_ERROR",
            "message": str(error),
            "details": {
                "traceback": traceback.format_exc()
            }
        }
    
    def _show_error_dialog(self, error_info: Dict[str, Any], parent: Optional[QWidget] = None):
        """显示错误对话框"""
        try:
            error_type = error_info.get("error_type", "Unknown")
            message = error_info.get("message", "未知错误")
            
            # 根据错误类型选择图标和标题
            if error_type in ["NetworkError", "CrawlerError"]:
                title = "网络错误"
                icon = QMessageBox.Icon.Warning
            elif error_type in ["ValidationError", "ParseError"]:
                title = "数据错误"
                icon = QMessageBox.Icon.Warning
            elif error_type in ["ConfigError", "DatabaseError"]:
                title = "系统错误"
                icon = QMessageBox.Icon.Critical
            else:
                title = "程序错误"
                icon = QMessageBox.Icon.Critical
            
            # 创建消息框
            msg_box = QMessageBox(parent)
            msg_box.setIcon(icon)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            
            # 添加详细信息
            details = error_info.get("details", {})
            if details:
                detail_text = "\n".join([f"{k}: {v}" for k, v in details.items()])
                msg_box.setDetailedText(detail_text)
            
            msg_box.exec()
            
        except Exception as dialog_error:
            logger.error(f"显示错误对话框失败: {dialog_error}")


# 全局错误处理器实例
error_handler = ErrorHandler()


def handle_exceptions(show_dialog: bool = True, context: Optional[str] = None):
    """异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(e, context or func.__name__, show_dialog)
                return None
        return wrapper
    return decorator


def handle_async_exceptions(show_dialog: bool = True, context: Optional[str] = None):
    """异步异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(e, context or func.__name__, show_dialog)
                return None
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default=None, context: Optional[str] = None, **kwargs):
    """安全执行函数"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_handler.handle_error(e, context or func.__name__, show_dialog=False)
        return default


async def safe_execute_async(func: Callable, *args, default=None, context: Optional[str] = None, **kwargs):
    """安全执行异步函数"""
    try:
        return await func(*args, **kwargs)
    except Exception as e:
        error_handler.handle_error(e, context or func.__name__, show_dialog=False)
        return default


class RetryHandler:
    """重试处理器"""
    
    @staticmethod
    def retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0, 
             exceptions: tuple = (Exception,)):
        """重试装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                attempt = 0
                current_delay = delay
                
                while attempt < max_attempts:
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        attempt += 1
                        if attempt >= max_attempts:
                            logger.error(f"函数 {func.__name__} 重试 {max_attempts} 次后仍然失败")
                            raise e
                        
                        logger.warning(f"函数 {func.__name__} 第 {attempt} 次尝试失败，{current_delay}秒后重试: {str(e)}")
                        
                        import time
                        time.sleep(current_delay)
                        current_delay *= backoff
                
                return None
            return wrapper
        return decorator
    
    @staticmethod
    def async_retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0,
                   exceptions: tuple = (Exception,)):
        """异步重试装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                attempt = 0
                current_delay = delay
                
                while attempt < max_attempts:
                    try:
                        return await func(*args, **kwargs)
                    except exceptions as e:
                        attempt += 1
                        if attempt >= max_attempts:
                            logger.error(f"异步函数 {func.__name__} 重试 {max_attempts} 次后仍然失败")
                            raise e
                        
                        logger.warning(f"异步函数 {func.__name__} 第 {attempt} 次尝试失败，{current_delay}秒后重试: {str(e)}")
                        
                        import asyncio
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                
                return None
            return wrapper
        return decorator


# 便捷的重试装饰器
retry = RetryHandler.retry
async_retry = RetryHandler.async_retry


def validate_url(url: str) -> str:
    """验证URL"""
    if not url or not isinstance(url, str):
        raise ValidationError("URL不能为空", "INVALID_URL")
    
    url = url.strip()
    if not url.startswith(('http://', 'https://')):
        raise ValidationError("URL必须以http://或https://开头", "INVALID_URL_SCHEME")
    
    return url


def validate_positive_number(value: Any, name: str = "数值") -> float:
    """验证正数"""
    try:
        num = float(value)
        if num <= 0:
            raise ValidationError(f"{name}必须大于0", "INVALID_NUMBER")
        return num
    except (ValueError, TypeError):
        raise ValidationError(f"{name}必须是有效的数字", "INVALID_NUMBER")


def validate_non_empty_string(value: Any, name: str = "字符串") -> str:
    """验证非空字符串"""
    if not value or not isinstance(value, str) or not value.strip():
        raise ValidationError(f"{name}不能为空", "EMPTY_STRING")
    return value.strip()
