"""
URL解析器
"""

import re
import requests
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse, parse_qs, unquote
from loguru import logger

from ..config.settings import TikTokShopConstants


class URLParser:
    """URL解析器"""
    
    def __init__(self):
        self.supported_domains = TikTokShopConstants.DOMAINS
        
        # URL模式匹配 - 支持三种格式
        self.patterns = {
            # Type 1: TikTok Shop PDP (Product Detail Page) 格式
            "shop_pdp": [
                r"tiktok\.com/shop/pdp/([^/\?]+)/(\d+)",
                r"www\.tiktok\.com/shop/pdp/([^/\?]+)/(\d+)",
            ],
            # Type 2: Desktop Shop URLs
            "desktop_shop": [
                r"shop\.tiktok\.com/view/product/(\d+)",
                r"www\.tiktok\.com/view/product/(\d+)",  # 重定向后的格式
                r"tiktok\.com/view/product/(\d+)",       # 无www的格式
            ],
            # Type 3: Mobile Share URLs (短链接)
            "mobile_share": [
                r"tiktok\.com/t/([A-Za-z0-9]+)/?",
                r"www\.tiktok\.com/t/([A-Za-z0-9]+)/?",
            ]
        }

    def follow_redirect(self, url: str) -> str:
        """
        跟随重定向获取最终URL
        主要用于处理移动端分享链接
        """
        try:
            logger.debug(f"🔄 [URL解析] 跟随重定向: {url}")

            # 设置请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
            }

            # 设置Cookie - 模拟真实浏览器访问
            cookie_string = self.generate_default_tiktok_cookie()

            # 将Cookie字符串转换为字典
            cookies = {}
            if cookie_string:
                for cookie_pair in cookie_string.split(';'):
                    if '=' in cookie_pair:
                        key, value = cookie_pair.strip().split('=', 1)
                        cookies[key] = value

            # 创建session以保持Cookie
            session = requests.Session()
            session.headers.update(headers)
            session.cookies.update(cookies)

            # 直接使用GET请求跟随重定向（移动短链接需要GET请求）
            logger.debug(f"🔄 [URL解析] 使用GET请求跟随重定向")
            response = session.get(url, allow_redirects=True, timeout=15)
            final_url = response.url

            if final_url != url and final_url:
                logger.info(f"✅ [URL解析] GET请求重定向成功: {url} -> {final_url}")
                return final_url
            else:
                logger.warning(f"⚠️ [URL解析] 未检测到重定向: {url}")
                return url

        except requests.exceptions.Timeout:
            logger.warning(f"⚠️ [URL解析] 重定向请求超时: {url}")
            return url
        except requests.exceptions.ConnectionError:
            logger.warning(f"⚠️ [URL解析] 重定向连接失败: {url}")
            return url
        except Exception as e:
            logger.warning(f"⚠️ [URL解析] 重定向失败: {url} - {str(e)}")
            return url  # 如果重定向失败，返回原URL
        

    def generate_default_tiktok_cookie(self) -> str:
        """生成默认的TikTok Cookie"""
        try:
            import time
            import random
            import hashlib

            # 生成基础的会话标识
            timestamp = str(int(time.time()))
            random_id = ''.join(random.choices('0123456789abcdef', k=32))

            # 构建基础Cookie
            cookie_parts = [
                f"ttwid=1%7C{random_id}%7C{timestamp}%7C{hashlib.md5(random_id.encode()).hexdigest()}",
                "i18next=en",
                f"msToken={''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_', k=107))}=="
            ]

            default_cookie = ';'.join(cookie_parts)
            logger.debug(f"🍪 [Cookie] 生成默认Cookie: {default_cookie[:100]}...")

            return default_cookie

        except Exception as e:
            logger.error(f"💥 [Cookie] 生成默认Cookie失败: {str(e)}")
            return None

    def parse_url(self, url: str) -> Dict[str, Any]:
        """
        解析TikTok产品URL并返回结构化信息
        支持三种URL格式：
        1. PDP URLs: https://www.tiktok.com/shop/pdp/product-name/product-id
        2. Desktop Shop URLs: https://shop.tiktok.com/view/product/product-id
        3. Mobile Share URLs: https://www.tiktok.com/t/short-code/

        Returns:
            {
                "url_type": "shop_pdp" | "desktop_shop" | "mobile_share" | "unknown",
                "product_id": str | None,
                "product_slug": str | None,
                "share_code": str | None,
                "original_url": str,
                "normalized_url": str,
                "final_url": str | None,  # 重定向后的最终URL
                "domain": str,
                "is_valid": bool,
                "error": str | None
            }
        """

        result = {
            "url_type": "unknown",
            "product_id": None,
            "product_slug": None,
            "share_code": None,
            "original_url": url,
            "normalized_url": url,
            "final_url": None,
            "clean_url": None,  # 新增：用于爬取的干净URL
            "domain": None,
            "is_valid": False,
            "error": None
        }
        
        try:
            # 标准化URL
            normalized_url = self.normalize_url(url)
            result["normalized_url"] = normalized_url
            
            # 解析域名
            parsed = urlparse(normalized_url)
            domain = parsed.netloc.lower()
            result["domain"] = domain
            
            # 检查是否为支持的域名
            if not any(supported_domain in domain for supported_domain in self.supported_domains):
                result["error"] = f"不支持的域名: {domain}"
                return result
            
            # 尝试匹配各种URL模式
            url_content = normalized_url.lower()
            logger.debug(f"🔍 [URL解析] 开始匹配URL格式: {url_content}")

            # 1. 尝试匹配Mobile Share URLs (需要重定向)
            for pattern in self.patterns["mobile_share"]:
                match = re.search(pattern, url_content)
                if match:
                    share_code = match.group(1)
                    logger.info(f"🔍 [URL解析] 检测到移动分享链接: {share_code}")

                    # 跟随重定向获取最终URL
                    final_url = self.follow_redirect(normalized_url)
                    result["final_url"] = final_url

                    # 从重定向URL中提取产品ID并构造干净的PDP URL
                    if final_url != normalized_url:
                        logger.debug(f"🔄 [URL解析] 从重定向URL提取产品ID: {final_url}")

                        # 尝试从重定向URL中提取产品ID
                        product_id = self.extract_product_id_from_redirect_url(final_url)

                        if product_id:
                            # 构造干净的PDP URL
                            clean_pdp_url = f"https://www.tiktok.com/shop/pdp/product/{product_id}"

                            result.update({
                                "url_type": "mobile_share",
                                "share_code": share_code,
                                "product_id": product_id,
                                "product_slug": "product",  # 使用通用slug
                                "final_url": final_url,
                                "clean_url": clean_pdp_url,  # 新增：用于爬取的干净URL
                                "is_valid": True
                            })

                            logger.info(f"✅ [URL解析] 成功解析移动分享链接")
                            logger.info(f"   - 分享代码: {share_code}")
                            logger.info(f"   - 重定向URL: {final_url[:100]}...")
                            logger.info(f"   - 提取产品ID: {product_id}")
                            logger.info(f"   - 干净PDP URL: {clean_pdp_url}")
                            return result
                        else:
                            # 如果无法提取产品ID，尝试递归解析
                            logger.debug(f"🔄 [URL解析] 无法提取产品ID，尝试递归解析")
                            redirected_result = self.parse_url(final_url)

                            if redirected_result["is_valid"]:
                                result.update({
                                    "url_type": "mobile_share",
                                    "share_code": share_code,
                                    "product_id": redirected_result["product_id"],
                                    "product_slug": redirected_result["product_slug"],
                                    "final_url": final_url,
                                    "is_valid": True
                                })

                                logger.info(f"✅ [URL解析] 通过递归解析成功")
                                logger.info(f"   - 产品ID: {redirected_result['product_id']}")
                                return result

                    # 如果重定向失败，标记为无效
                    result.update({
                        "url_type": "mobile_share",
                        "share_code": share_code,
                        "error": "无法解析重定向后的URL"
                    })
                    return result

            # 2. 尝试匹配Desktop Shop URLs
            for pattern in self.patterns["desktop_shop"]:
                match = re.search(pattern, url_content)
                if match:
                    product_id = match.group(1)

                    result.update({
                        "url_type": "desktop_shop",
                        "product_id": product_id,
                        "is_valid": True
                    })

                    logger.info(f"✅ [URL解析] 成功匹配桌面商店格式")
                    logger.info(f"   - 产品ID: {product_id}")
                    return result

            # 3. 尝试匹配PDP URLs
            for pattern in self.patterns["shop_pdp"]:
                match = re.search(pattern, url_content)
                if match:
                    product_slug = match.group(1)
                    product_id = match.group(2)

                    result.update({
                        "url_type": "shop_pdp",
                        "product_slug": product_slug,
                        "product_id": product_id,
                        "is_valid": True
                    })

                    logger.info(f"✅ [URL解析] 成功匹配PDP格式")
                    logger.info(f"   - 产品Slug: {product_slug}")
                    logger.info(f"   - 产品ID: {product_id}")
                    return result

            # 如果没有匹配到任何模式
            logger.warning(f"❌ [URL解析] 无法识别的URL格式")
            result["error"] = "无法识别的URL格式，支持的格式：\n1. PDP: https://www.tiktok.com/shop/pdp/product-name/product-id\n2. 桌面商店: https://shop.tiktok.com/view/product/product-id\n3. 移动分享: https://www.tiktok.com/t/share-code/"
            
        except Exception as e:
            result["error"] = f"URL解析异常: {str(e)}"
            logger.error(f"URL解析失败: {url} - {str(e)}")
        
        return result
    
    def normalize_url(self, url: str) -> str:
        """标准化URL"""
        try:
            # 移除首尾空格
            url = url.strip()
            
            # 如果没有协议，添加https
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # URL解码
            url = unquote(url)
            
            # 移除片段标识符
            if '#' in url:
                url = url.split('#')[0]
            
            # 移除常见的跟踪参数
            tracking_params = [
                'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'fbclid', 'gclid', 'ref', 'source', 'from'
            ]
            
            parsed = urlparse(url)
            if parsed.query:
                query_params = parse_qs(parsed.query)
                # 过滤掉跟踪参数
                filtered_params = {k: v for k, v in query_params.items() 
                                 if k not in tracking_params}
                
                if filtered_params:
                    from urllib.parse import urlencode
                    new_query = urlencode(filtered_params, doseq=True)
                    url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}?{new_query}"
                else:
                    url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            
            return url
            
        except Exception as e:
            logger.error(f"URL标准化失败: {url} - {str(e)}")
            return url
    
    def extract_product_info_from_url(self, url: str) -> Dict[str, Optional[str]]:
        """从TikTok产品URL中提取商品信息"""
        parsed_info = self.parse_url(url)

        return {
            "product_id": parsed_info.get("product_id"),
            "product_slug": parsed_info.get("product_slug"),
            "share_code": parsed_info.get("share_code"),
            "url_type": parsed_info.get("url_type"),
            "final_url": parsed_info.get("final_url"),
            "clean_url": parsed_info.get("clean_url")  # 新增：干净的爬取URL
        }

    def is_product_url(self, url: str) -> bool:
        """判断是否为TikTok商品URL（支持三种格式）"""
        parsed_info = self.parse_url(url)
        return parsed_info["url_type"] in ["shop_pdp", "desktop_shop", "mobile_share"]

    def is_pdp_url(self, url: str) -> bool:
        """判断是否为PDP URL"""
        parsed_info = self.parse_url(url)
        return parsed_info["url_type"] == "shop_pdp"

    def is_desktop_shop_url(self, url: str) -> bool:
        """判断是否为桌面商店URL"""
        parsed_info = self.parse_url(url)
        return parsed_info["url_type"] == "desktop_shop"

    def is_mobile_share_url(self, url: str) -> bool:
        """判断是否为移动分享URL"""
        parsed_info = self.parse_url(url)
        return parsed_info["url_type"] == "mobile_share"

    def is_valid_tiktok_url(self, url: str) -> bool:
        """判断是否为有效的TikTok产品URL（支持三种格式）"""
        parsed_info = self.parse_url(url)
        return parsed_info["is_valid"]
    
    def get_supported_url_examples(self) -> List[str]:
        """获取支持的TikTok产品URL示例"""
        return [
            # Type 1: PDP URLs
            "https://www.tiktok.com/shop/pdp/product-name/1234567890123456789",
            "https://tiktok.com/shop/pdp/another-product/9876543210987654321",
            # Type 2: Desktop Shop URLs
            "https://shop.tiktok.com/view/product/1731376180867011050",
            # Type 3: Mobile Share URLs
            "https://www.tiktok.com/t/ZT6FPPx77/",
            "https://tiktok.com/t/ABC123def/",
        ]
    
    def validate_and_suggest(self, url: str) -> Dict[str, Any]:
        """验证URL并提供建议"""
        parsed_info = self.parse_url(url)
        
        result = {
            "is_valid": parsed_info["is_valid"],
            "url_type": parsed_info["url_type"],
            "error": parsed_info.get("error"),
            "suggestions": []
        }
        
        if not parsed_info["is_valid"]:
            # 提供修正建议
            if not any(domain in url.lower() for domain in self.supported_domains):
                result["suggestions"].append("请确保URL来自TikTok Shop")

            if not url.startswith(('http://', 'https://')):
                result["suggestions"].append("URL应该以 http:// 或 https:// 开头")

            # 检查是否包含支持的URL模式
            url_lower = url.lower()
            has_supported_pattern = (
                "/shop/pdp/" in url_lower or
                "shop.tiktok.com/view/product/" in url_lower or
                "/t/" in url_lower
            )

            if not has_supported_pattern:
                result["suggestions"].append("支持三种URL格式：")
                result["suggestions"].append("1. PDP格式: /shop/pdp/product-name/product-id")
                result["suggestions"].append("2. 桌面商店格式: shop.tiktok.com/view/product/product-id")
                result["suggestions"].append("3. 移动分享格式: /t/share-code/")

            # 提供示例URL
            result["examples"] = self.get_supported_url_examples()
        
        return result

    def extract_product_id_from_redirect_url(self, redirect_url: str) -> Optional[str]:
        """
        从重定向URL中提取产品ID
        支持多种重定向URL格式
        """
        try:
            logger.debug(f"🔍 [URL解析] 提取产品ID: {redirect_url}")

            # 常见的重定向URL格式模式
            redirect_patterns = [
                # https://www.tiktok.com/view/product/1731376180867011050?...
                r"/view/product/(\d+)",
                # https://shop.tiktok.com/view/product/1731376180867011050?...
                r"shop\.tiktok\.com/view/product/(\d+)",
                # https://www.tiktok.com/shop/pdp/product-name/1731376180867011050?...
                r"/shop/pdp/[^/]+/(\d+)",
                # 其他可能的格式
                r"product[/_](\d+)",
                r"item[/_](\d+)",
            ]

            for pattern in redirect_patterns:
                match = re.search(pattern, redirect_url, re.IGNORECASE)
                if match:
                    product_id = match.group(1)
                    logger.info(f"✅ [URL解析] 成功提取产品ID: {product_id} (模式: {pattern})")
                    return product_id

            logger.warning(f"⚠️ [URL解析] 无法从重定向URL提取产品ID: {redirect_url}")
            return None

        except Exception as e:
            logger.error(f"❌ [URL解析] 提取产品ID时出错: {str(e)}")
            return None

    def get_crawling_url(self, url: str) -> str:
        """
        获取用于爬取的最终URL
        对于移动分享链接，返回干净的PDP URL（如果可用）
        对于其他类型，返回标准化后的URL
        """
        parsed_info = self.parse_url(url)

        if parsed_info["url_type"] == "mobile_share":
            # 优先使用干净的PDP URL
            if parsed_info.get("clean_url"):
                logger.debug(f"🎯 [URL解析] 使用干净的PDP URL进行爬取: {parsed_info['clean_url']}")
                return parsed_info["clean_url"]
            # 降级使用重定向后的URL
            elif parsed_info.get("final_url"):
                logger.debug(f"🎯 [URL解析] 使用重定向URL进行爬取: {parsed_info['final_url']}")
                return parsed_info["final_url"]

        return parsed_info["normalized_url"]

    def get_product_id_for_crawling(self, url: str) -> Optional[str]:
        """
        获取用于爬取的产品ID
        支持所有三种URL格式
        """
        parsed_info = self.parse_url(url)
        return parsed_info.get("product_id")
