"""
爬虫接口规范 - 定义统一的爬虫接口和数据格式
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum
import time


class CrawlerStatus(Enum):
    """爬虫状态枚举"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    CLOSED = "closed"


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    CAPTCHA_DETECTED = "captcha_detected"
    RATE_LIMITED = "rate_limited"
    INVALID_URL = "invalid_url"
    PARSING_ERROR = "parsing_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class CrawlerError:
    """爬虫错误信息"""
    error_type: ErrorType
    message: str
    url: Optional[str] = None
    timestamp: float = None
    details: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class CrawlerResult:
    """爬虫结果数据结构"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[CrawlerError] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class ProductData:
    """产品数据结构"""
    product_id: str
    title: str
    price: float
    currency: str = "USD"
    sold_count: int = 0
    rating: float = 0.0
    review_count: int = 0
    images: List[str] = None
    description: str = ""
    seller_id: Optional[str] = None
    seller_name: Optional[str] = None
    shop_id: Optional[str] = None
    shop_name: Optional[str] = None
    category: Optional[str] = None
    shipping_fee: float = 0.0
    availability: bool = True
    url: Optional[str] = None
    
    def __post_init__(self):
        if self.images is None:
            self.images = []


@dataclass
class ShopData:
    """店铺数据结构"""
    shop_id: str
    shop_name: str
    seller_id: Optional[str] = None
    seller_name: Optional[str] = None
    product_count: int = 0
    rating: float = 0.0
    follower_count: int = 0
    description: str = ""
    avatar_url: Optional[str] = None
    cover_url: Optional[str] = None
    url: Optional[str] = None
    products: List[ProductData] = None
    
    def __post_init__(self):
        if self.products is None:
            self.products = []


class ICrawler(ABC):
    """爬虫接口规范"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化爬虫
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """关闭爬虫，清理资源"""
        pass
    
    @abstractmethod
    async def get_product_data(self, url: str) -> CrawlerResult:
        """
        获取产品数据
        
        Args:
            url: 产品URL
            
        Returns:
            CrawlerResult: 包含ProductData的结果
        """
        pass
    
    @abstractmethod
    async def get_shop_data(self, url: str, load_products: bool = False) -> CrawlerResult:
        """
        获取店铺数据
        
        Args:
            url: 店铺URL
            load_products: 是否加载店铺商品列表
            
        Returns:
            CrawlerResult: 包含ShopData的结果
        """
        pass
    
    @abstractmethod
    async def get_page_content(self, url: str, **kwargs) -> CrawlerResult:
        """
        获取页面内容
        
        Args:
            url: 页面URL
            **kwargs: 其他参数
            
        Returns:
            CrawlerResult: 包含页面内容的结果
        """
        pass
    
    @abstractmethod
    def get_status(self) -> CrawlerStatus:
        """获取爬虫当前状态"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        pass
    
    @abstractmethod
    def set_user_interaction_callback(self, callback: Optional[Callable]) -> None:
        """设置用户交互回调函数"""
        pass


class CrawlerConfig:
    """爬虫配置基类"""
    
    def __init__(self):
        # 基础配置
        self.timeout = 30
        self.max_retries = 3
        self.request_delay = 1.0
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        
        # 错误处理配置
        self.retry_on_error = True
        self.ignore_ssl_errors = False
        
        # 回调配置
        self.progress_callback = None
        self.error_callback = None
        
        # 日志配置
        self.enable_debug_logging = False
        self.log_requests = False


class DirectCrawlerConfig(CrawlerConfig):
    """直连爬虫配置"""
    
    def __init__(self):
        super().__init__()
        
        # HTTP配置
        self.max_concurrent_requests = 5
        self.connection_pool_size = 10
        self.keep_alive = True
        
        # 代理配置
        self.proxy_url = None
        self.proxy_auth = None
        
        # Cookie配置
        self.use_cookies = True
        self.cookie_jar = None
        
        # 请求头配置
        self.custom_headers = {}
        self.rotate_user_agents = False


class SeleniumCrawlerConfig(CrawlerConfig):
    """Selenium爬虫配置"""
    
    def __init__(self):
        super().__init__()
        
        # 浏览器配置
        self.headless = False
        self.window_size = (1920, 1080)
        self.browser_executable_path = None
        self.driver_executable_path = None
        
        # 等待配置
        self.page_load_timeout = 30
        self.element_wait_timeout = 10
        self.implicit_wait = 5
        
        # 浏览器池配置
        self.use_browser_pool = True
        self.max_pool_size = 3
        self.pool_initialization_timeout = 60
        
        # 验证码处理配置
        self.captcha_detection_enabled = True
        self.captcha_wait_timeout = 300
        self.auto_handle_captcha = False
        
        # 页面加载配置
        self.wait_for_load = True
        self.load_more_enabled = False
        self.max_load_more_attempts = 5


class CrawlerFactory:
    """爬虫工厂类"""
    
    @staticmethod
    def create_direct_crawler(config: Optional[DirectCrawlerConfig] = None) -> 'DirectCrawler':
        """创建直连爬虫实例"""
        from .direct_crawler import DirectCrawler
        return DirectCrawler(config or DirectCrawlerConfig())
    
    @staticmethod
    def create_selenium_crawler(config: Optional[SeleniumCrawlerConfig] = None) -> 'SeleniumCrawler':
        """创建Selenium爬虫实例"""
        from .selenium_crawler_new import SeleniumCrawler
        return SeleniumCrawler(config or SeleniumCrawlerConfig())
    
    @staticmethod
    def create_crawler(crawler_type: str, config: Optional[CrawlerConfig] = None) -> ICrawler:
        """
        根据类型创建爬虫实例
        
        Args:
            crawler_type: 爬虫类型 ("direct" 或 "selenium")
            config: 爬虫配置
            
        Returns:
            ICrawler: 爬虫实例
        """
        if crawler_type.lower() == "direct":
            return CrawlerFactory.create_direct_crawler(config)
        elif crawler_type.lower() == "selenium":
            return CrawlerFactory.create_selenium_crawler(config)
        else:
            raise ValueError(f"不支持的爬虫类型: {crawler_type}")


# 工具函数
def create_success_result(data: Any, metadata: Dict[str, Any] = None) -> CrawlerResult:
    """创建成功结果"""
    return CrawlerResult(
        success=True,
        data=data,
        metadata=metadata or {}
    )


def create_error_result(error_type: ErrorType, message: str, 
                       url: str = None, details: Dict[str, Any] = None) -> CrawlerResult:
    """创建错误结果"""
    error = CrawlerError(
        error_type=error_type,
        message=message,
        url=url,
        details=details or {}
    )
    return CrawlerResult(
        success=False,
        error=error
    )
