"""
商品数据表格组件
"""

from typing import List, Optional, Dict, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QPushButton, QLineEdit, QComboBox, QLabel, QCheckBox,
    QMessageBox, QMenu, QApplication, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QPixmap, QIcon, QFont
from loguru import logger

from ..models.product import Product
from ..utils.memory_storage import MemoryStorage



class ProductTableWidget(QTableWidget):
    """自定义商品表格"""
    
    product_selected = pyqtSignal(Product)
    
    def __init__(self):
        super().__init__()
        self.products: List[Product] = []
        self.setup_table()
        self.setup_context_menu()
    
    def setup_table(self):
        """设置表格"""
        # 设置列
        self.columns = [
            ("商品ID", 120),
            ("商品名称", 200),
            ("分类", 100),
            ("价格", 80),
            ("原价", 80),
            ("折扣", 60),
            ("库存", 60),
            ("销量", 80),
            ("评分", 60),
            ("评论数", 80),
            ("店铺", 120),
            ("状态", 80),
            ("爬取时间", 120)
        ]
        
        self.setColumnCount(len(self.columns))
        self.setHorizontalHeaderLabels([col[0] for col in self.columns])
        
        # 设置列宽
        for i, (_, width) in enumerate(self.columns):
            self.setColumnWidth(i, width)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.setSortingEnabled(True)
        
        # 设置表头
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 商品名称列自适应
        
        # 连接信号
        self.itemSelectionChanged.connect(self.on_selection_changed)
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
    
    def setup_context_menu(self):
        """设置右键菜单"""
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if not self.itemAt(position):
            return
        
        menu = QMenu(self)
        
        # 查看详情
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(self.view_product_detail)
        menu.addAction(view_action)
        
        # 复制链接
        copy_url_action = QAction("复制商品链接", self)
        copy_url_action.triggered.connect(self.copy_product_url)
        menu.addAction(copy_url_action)
        
        menu.addSeparator()
        
        # 删除商品
        delete_action = QAction("删除商品", self)
        delete_action.triggered.connect(self.delete_product)
        menu.addAction(delete_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def load_products(self, products: List[Product]):
        """加载商品数据"""
        self.products = products
        self.setRowCount(len(products))
        
        for row, product in enumerate(products):
            self.populate_row(row, product)
        
        logger.info(f"加载了 {len(products)} 个商品")
    
    def populate_row(self, row: int, product: Product):
        """填充行数据"""
        try:
            # 商品ID
            self.setItem(row, 0, QTableWidgetItem(product.product_id))
            
            # 商品名称
            title_item = QTableWidgetItem(product.title)
            title_item.setToolTip(product.title)
            self.setItem(row, 1, title_item)
            
            # 分类
            self.setItem(row, 2, QTableWidgetItem(product.category))
            
            # 价格
            price_item = QTableWidgetItem(f"{product.currency.value} {product.price}")
            price_item.setData(Qt.ItemDataRole.UserRole, float(product.price))
            self.setItem(row, 3, price_item)
            
            # 原价
            if product.original_price:
                original_price_item = QTableWidgetItem(f"{product.currency.value} {product.original_price}")
                original_price_item.setData(Qt.ItemDataRole.UserRole, float(product.original_price))
            else:
                original_price_item = QTableWidgetItem("-")
                original_price_item.setData(Qt.ItemDataRole.UserRole, 0)
            self.setItem(row, 4, original_price_item)
            
            # 折扣
            if product.discount_percentage:
                discount_item = QTableWidgetItem(f"{product.discount_percentage:.1f}%")
                discount_item.setData(Qt.ItemDataRole.UserRole, product.discount_percentage)
            else:
                discount_item = QTableWidgetItem("-")
                discount_item.setData(Qt.ItemDataRole.UserRole, 0)
            self.setItem(row, 5, discount_item)
            
            # 库存
            stock_item = QTableWidgetItem(str(product.stock) if product.stock is not None else "-")
            stock_item.setData(Qt.ItemDataRole.UserRole, product.stock or 0)
            self.setItem(row, 6, stock_item)
            
            # 销量
            sales_item = QTableWidgetItem(str(product.sold_count))
            sales_item.setData(Qt.ItemDataRole.UserRole, product.sold_count)
            self.setItem(row, 7, sales_item)
            
            # 评分
            rating_item = QTableWidgetItem(f"{product.rating.average_rating:.1f}")
            rating_item.setData(Qt.ItemDataRole.UserRole, product.rating.average_rating)
            self.setItem(row, 8, rating_item)
            
            # 评论数
            reviews_item = QTableWidgetItem(str(product.rating.total_reviews))
            reviews_item.setData(Qt.ItemDataRole.UserRole, product.rating.total_reviews)
            self.setItem(row, 9, reviews_item)
            
            # 店铺
            shop_item = QTableWidgetItem(product.shop_name)
            shop_item.setToolTip(product.shop_name)
            self.setItem(row, 10, shop_item)
            
            # 状态
            status_item = QTableWidgetItem(product.status.value)
            self.setItem(row, 11, status_item)
            
            # 爬取时间
            scraped_time = product.scraped_at.strftime("%Y-%m-%d %H:%M")
            self.setItem(row, 12, QTableWidgetItem(scraped_time))
            
        except Exception as e:
            logger.error(f"填充行数据失败: {str(e)}")
    
    def get_selected_product(self) -> Optional[Product]:
        """获取选中的商品"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.products):
            return self.products[current_row]
        return None
    
    def on_selection_changed(self):
        """选择改变事件"""
        product = self.get_selected_product()
        if product:
            self.product_selected.emit(product)
    
    def on_item_double_clicked(self, item):
        """双击事件"""
        self.view_product_detail()
    
    def view_product_detail(self):
        """查看商品详情"""
        product = self.get_selected_product()
        if product:
            # 这里可以打开商品详情对话框
            QMessageBox.information(self, "商品详情", f"商品: {product.title}\n描述: {product.description}")
    
    def copy_product_url(self):
        """复制商品链接"""
        product = self.get_selected_product()
        if product:
            clipboard = QApplication.clipboard()
            clipboard.setText(product.product_url)
            QMessageBox.information(self, "成功", "商品链接已复制到剪贴板")
    
    def delete_product(self):
        """删除商品"""
        product = self.get_selected_product()
        if product:
            reply = QMessageBox.question(
                self, "确认删除", f"确定要删除商品 '{product.title}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 这里添加删除逻辑
                QMessageBox.information(self, "成功", "商品已删除")
    
    def filter_products(self, keyword: str = "", category: str = "", min_price: float = 0, max_price: float = 0):
        """过滤商品"""
        filtered_products = []
        
        for product in self.products:
            # 关键词过滤
            if keyword and keyword.lower() not in product.title.lower():
                continue
            
            # 分类过滤
            if category and category != "全部" and product.category != category:
                continue
            
            # 价格过滤
            price = float(product.price)
            if min_price > 0 and price < min_price:
                continue
            if max_price > 0 and price > max_price:
                continue
            
            filtered_products.append(product)
        
        self.load_products(filtered_products)


class ProductTable(QWidget):
    """商品表格组件"""
    
    def __init__(self, memory_storage: Optional[MemoryStorage] = None):
        super().__init__()
        self.memory_storage = memory_storage or MemoryStorage()
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 过滤器区域
        filter_layout = QHBoxLayout()
        
        # 搜索框
        filter_layout.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入商品名称...")
        self.search_input.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.search_input)
        
        # 分类过滤
        filter_layout.addWidget(QLabel("分类:"))
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部")
        self.category_combo.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.category_combo)
        
        # 价格范围
        filter_layout.addWidget(QLabel("价格:"))
        self.min_price_input = QLineEdit()
        self.min_price_input.setPlaceholderText("最低价")
        self.min_price_input.setMaximumWidth(80)
        self.min_price_input.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.min_price_input)
        
        filter_layout.addWidget(QLabel("-"))
        
        self.max_price_input = QLineEdit()
        self.max_price_input.setPlaceholderText("最高价")
        self.max_price_input.setMaximumWidth(80)
        self.max_price_input.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.max_price_input)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.load_data)
        filter_layout.addWidget(self.refresh_button)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # 商品表格
        self.table = ProductTableWidget()
        layout.addWidget(self.table)
        
        # 统计信息和刷新按钮
        stats_layout = QHBoxLayout()
        self.stats_label = QLabel("商品总数: 0")
        stats_layout.addWidget(self.stats_label)

        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.clicked.connect(self.load_data)
        stats_layout.addWidget(self.refresh_button)
        stats_layout.addStretch()

        layout.addLayout(stats_layout)
    
    def load_data(self):
        """加载数据"""
        try:
            # 从内存存储加载数据
            products = self.memory_storage.list_products(limit=1000)
            self.table.load_products(products)
            self.update_stats(products)
            self.update_categories(products)

        except Exception as e:
            logger.error(f"加载商品数据失败: {str(e)}")
            # 避免在初始化时显示错误对话框
            try:
                from PyQt5.QtWidgets import QApplication
                if QApplication.instance() is not None:
                    QMessageBox.critical(self, "错误", f"加载数据失败: {str(e)}")
            except Exception:
                pass  # 忽略对话框显示错误
    
    def update_stats(self, products: List[Product]):
        """更新统计信息"""
        total_count = len(products)
        self.stats_label.setText(f"商品总数: {total_count}")
    
    def update_categories(self, products: List[Product]):
        """更新分类列表"""
        categories = set(product.category for product in products if product.category)
        
        self.category_combo.clear()
        self.category_combo.addItem("全部")
        for category in sorted(categories):
            self.category_combo.addItem(category)
    
    def apply_filters(self):
        """应用过滤器"""
        keyword = self.search_input.text().strip()
        category = self.category_combo.currentText()
        
        try:
            min_price = float(self.min_price_input.text()) if self.min_price_input.text() else 0
        except ValueError:
            min_price = 0
        
        try:
            max_price = float(self.max_price_input.text()) if self.max_price_input.text() else 0
        except ValueError:
            max_price = 0
        
        self.table.filter_products(keyword, category, min_price, max_price)

    def refresh_data(self):
        """刷新数据 - 主窗口调用的方法"""
        try:
            logger.info("🔄 [UI] 刷新产品表格数据")
            self.load_data()
            logger.info("✅ [UI] 产品表格数据刷新完成")
        except Exception as e:
            logger.error(f"💥 [UI] 刷新产品表格数据失败: {str(e)}")
            # 避免在初始化时显示错误对话框
            try:
                from PyQt5.QtWidgets import QApplication
                if QApplication.instance() is not None:
                    QMessageBox.warning(self, "刷新失败", f"刷新数据时出现错误: {str(e)}")
            except Exception:
                pass  # 忽略对话框显示错误
