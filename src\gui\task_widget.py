"""
任务管理组件
"""

from typing import List, Optional, Dict, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QProgressBar, QTextEdit, QSplitter,
    QGroupBox, QMessageBox, QMenu, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from loguru import logger

from ..models.scraping_task import ScrapingTask, TaskStatus


class TaskItemWidget(QWidget):
    """任务项组件"""
    
    def __init__(self, task: ScrapingTask):
        super().__init__()
        self.task = task
        self.setup_ui()
        self.update_display()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 任务标题行
        title_layout = QHBoxLayout()
        
        self.name_label = QLabel()
        self.name_label.setFont(QFont("", 10, QFont.Weight.Bold))
        title_layout.addWidget(self.name_label)
        
        title_layout.addStretch()
        
        self.status_label = QLabel()
        title_layout.addWidget(self.status_label)
        
        layout.addLayout(title_layout)
        
        # 任务描述
        self.description_label = QLabel()
        self.description_label.setWordWrap(True)
        self.description_label.setStyleSheet("color: #666666; font-size: 9pt;")
        layout.addWidget(self.description_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumHeight(6)
        layout.addWidget(self.progress_bar)
        
        # 统计信息
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: #888888; font-size: 8pt;")
        layout.addWidget(self.stats_label)
    
    def update_display(self):
        """更新显示"""
        # 任务名称
        self.name_label.setText(self.task.name)
        
        # 任务描述
        self.description_label.setText(self.task.description or "")
        
        # 状态
        status_colors = {
            TaskStatus.PENDING: "#FFA500",
            TaskStatus.RUNNING: "#4CAF50",
            TaskStatus.COMPLETED: "#2196F3",
            TaskStatus.FAILED: "#F44336",
            TaskStatus.CANCELLED: "#9E9E9E",
            TaskStatus.PAUSED: "#FF9800"
        }
        
        status_texts = {
            TaskStatus.PENDING: "等待中",
            TaskStatus.RUNNING: "运行中",
            TaskStatus.COMPLETED: "已完成",
            TaskStatus.FAILED: "失败",
            TaskStatus.CANCELLED: "已取消",
            TaskStatus.PAUSED: "已暂停"
        }
        
        status_color = status_colors.get(self.task.status, "#000000")
        status_text = status_texts.get(self.task.status, self.task.status.value)
        
        self.status_label.setText(status_text)
        self.status_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")
        
        # 进度条
        if self.task.progress.total > 0:
            self.progress_bar.setMaximum(self.task.progress.total)
            self.progress_bar.setValue(self.task.progress.completed)
            self.progress_bar.setVisible(True)
        else:
            self.progress_bar.setVisible(False)
        
        # 统计信息
        stats_parts = []
        
        if self.task.progress.total > 0:
            stats_parts.append(f"进度: {self.task.progress.completed}/{self.task.progress.total}")
        
        if self.task.progress.failed > 0:
            stats_parts.append(f"失败: {self.task.progress.failed}")
        
        if self.task.started_at:
            duration = self.task.get_duration()
            if duration:
                if duration < 60:
                    stats_parts.append(f"用时: {duration:.1f}秒")
                else:
                    stats_parts.append(f"用时: {duration/60:.1f}分钟")
        
        self.stats_label.setText(" | ".join(stats_parts))


class TaskListWidget(QListWidget):
    """任务列表组件"""
    
    task_selected = pyqtSignal(ScrapingTask)
    
    def __init__(self):
        super().__init__()
        self.tasks: Dict[str, ScrapingTask] = {}
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QListWidget.SingleSelection)
        
        # 设置右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
        # 连接信号
        self.itemSelectionChanged.connect(self.on_selection_changed)
    
    def add_task(self, task: ScrapingTask):
        """添加任务"""
        self.tasks[task.task_id] = task
        
        # 创建列表项
        item = QListWidgetItem()
        item.setData(Qt.UserRole, task.task_id)
        
        # 创建任务组件
        task_widget = TaskItemWidget(task)
        
        # 添加到列表
        self.addItem(item)
        item.setSizeHint(task_widget.sizeHint())
        self.setItemWidget(item, task_widget)
    
    def update_task(self, task: ScrapingTask):
        """更新任务"""
        self.tasks[task.task_id] = task
        
        # 找到对应的列表项并更新
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.UserRole) == task.task_id:
                widget = self.itemWidget(item)
                if isinstance(widget, TaskItemWidget):
                    widget.task = task
                    widget.update_display()
                break
    
    def remove_task(self, task_id: str):
        """移除任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
        
        # 从列表中移除
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.UserRole) == task_id:
                self.takeItem(i)
                break
    
    def get_selected_task(self) -> Optional[ScrapingTask]:
        """获取选中的任务"""
        current_item = self.currentItem()
        if current_item:
            task_id = current_item.data(Qt.UserRole)
            return self.tasks.get(task_id)
        return None
    
    def on_selection_changed(self):
        """选择改变事件"""
        task = self.get_selected_task()
        if task:
            self.task_selected.emit(task)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        if not item:
            return
        
        task = self.get_selected_task()
        if not task:
            return
        
        menu = QMenu(self)
        
        # 根据任务状态显示不同的菜单项
        if task.status == TaskStatus.RUNNING:
            pause_action = QAction("暂停任务", self)
            pause_action.triggered.connect(lambda: self.pause_task(task.task_id))
            menu.addAction(pause_action)
            
            cancel_action = QAction("取消任务", self)
            cancel_action.triggered.connect(lambda: self.cancel_task(task.task_id))
            menu.addAction(cancel_action)
            
        elif task.status == TaskStatus.PAUSED:
            resume_action = QAction("恢复任务", self)
            resume_action.triggered.connect(lambda: self.resume_task(task.task_id))
            menu.addAction(resume_action)
            
            cancel_action = QAction("取消任务", self)
            cancel_action.triggered.connect(lambda: self.cancel_task(task.task_id))
            menu.addAction(cancel_action)
            
        elif task.status == TaskStatus.PENDING:
            start_action = QAction("开始任务", self)
            start_action.triggered.connect(lambda: self.start_task(task.task_id))
            menu.addAction(start_action)
            
            cancel_action = QAction("取消任务", self)
            cancel_action.triggered.connect(lambda: self.cancel_task(task.task_id))
            menu.addAction(cancel_action)
        
        menu.addSeparator()
        
        # 查看详情
        detail_action = QAction("查看详情", self)
        detail_action.triggered.connect(lambda: self.show_task_detail(task.task_id))
        menu.addAction(detail_action)
        
        # 删除任务
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            delete_action = QAction("删除任务", self)
            delete_action.triggered.connect(lambda: self.delete_task(task.task_id))
            menu.addAction(delete_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def start_task(self, task_id: str):
        """开始任务"""
        # 这里应该调用爬虫管理器的方法
        QMessageBox.information(self, "提示", "开始任务功能开发中...")
    
    def pause_task(self, task_id: str):
        """暂停任务"""
        QMessageBox.information(self, "提示", "暂停任务功能开发中...")
    
    def resume_task(self, task_id: str):
        """恢复任务"""
        QMessageBox.information(self, "提示", "恢复任务功能开发中...")
    
    def cancel_task(self, task_id: str):
        """取消任务"""
        reply = QMessageBox.question(
            self, "确认", "确定要取消这个任务吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "提示", "取消任务功能开发中...")
    
    def delete_task(self, task_id: str):
        """删除任务"""
        reply = QMessageBox.question(
            self, "确认", "确定要删除这个任务吗？此操作不可撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.remove_task(task_id)
            QMessageBox.information(self, "成功", "任务已删除")
    
    def show_task_detail(self, task_id: str):
        """显示任务详情"""
        task = self.tasks.get(task_id)
        if task:
            detail_text = f"""
任务ID: {task.task_id}
任务名称: {task.name}
任务类型: {task.task_type.value}
目标URL: {task.target_url}
状态: {task.status.value}
创建时间: {task.created_at.strftime('%Y-%m-%d %H:%M:%S')}
"""
            if task.started_at:
                detail_text += f"开始时间: {task.started_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            if task.completed_at:
                detail_text += f"完成时间: {task.completed_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            QMessageBox.information(self, "任务详情", detail_text)


class TaskWidget(QWidget):
    """任务管理组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：任务列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 任务列表标题
        list_group = QGroupBox("任务列表")
        list_layout = QVBoxLayout(list_group)
        
        # 任务列表
        self.task_list = TaskListWidget()
        self.task_list.task_selected.connect(self.on_task_selected)
        list_layout.addWidget(self.task_list)
        
        # 任务操作按钮
        button_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.refresh_tasks)
        button_layout.addWidget(self.refresh_button)
        
        self.clear_button = QPushButton("清理完成")
        self.clear_button.clicked.connect(self.clear_completed_tasks)
        button_layout.addWidget(self.clear_button)
        
        button_layout.addStretch()
        list_layout.addLayout(button_layout)
        
        left_layout.addWidget(list_group)
        
        # 右侧：任务详情
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 任务详情
        detail_group = QGroupBox("任务详情")
        detail_layout = QVBoxLayout(detail_group)
        
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        detail_layout.addWidget(self.detail_text)
        
        right_layout.addWidget(detail_group)
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
    
    def setup_timer(self):
        """设置定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_tasks)
        self.update_timer.start(2000)  # 每2秒更新一次
    
    def add_task(self, task: ScrapingTask):
        """添加任务到列表"""
        try:
            logger.info(f"📋 [任务组件] 添加任务到列表: {task.name}")
            self.task_list.add_task(task)
        except Exception as e:
            logger.error(f"💥 [任务组件] 添加任务失败: {str(e)}")

    def refresh_tasks(self):
        """刷新任务列表"""
        # 这里应该从爬虫管理器获取任务列表
        # 为了演示，创建一些示例任务
        pass
    
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        reply = QMessageBox.question(
            self, "确认", "确定要清理所有已完成的任务吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 移除已完成的任务
            completed_task_ids = []
            for task_id, task in self.task_list.tasks.items():
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed_task_ids.append(task_id)
            
            for task_id in completed_task_ids:
                self.task_list.remove_task(task_id)
            
            QMessageBox.information(self, "完成", f"已清理 {len(completed_task_ids)} 个任务")
    
    def update_tasks(self):
        """更新任务状态"""
        # 这里应该从爬虫管理器获取最新的任务状态
        pass
    
    def on_task_selected(self, task: ScrapingTask):
        """任务选择事件"""
        # 显示任务详情
        detail_html = f"""
        <h3>{task.name}</h3>
        <p><b>描述:</b> {task.description or '无'}</p>
        <p><b>类型:</b> {task.task_type.value}</p>
        <p><b>状态:</b> {task.status.value}</p>
        <p><b>目标URL:</b> <a href="{task.target_url}">{task.target_url}</a></p>
        
        <h4>进度信息</h4>
        <p><b>总数:</b> {task.progress.total}</p>
        <p><b>已完成:</b> {task.progress.completed}</p>
        <p><b>失败:</b> {task.progress.failed}</p>
        <p><b>跳过:</b> {task.progress.skipped}</p>
        <p><b>完成率:</b> {task.progress.percentage:.1f}%</p>
        
        <h4>时间信息</h4>
        <p><b>创建时间:</b> {task.created_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
        """
        
        if task.started_at:
            detail_html += f"<p><b>开始时间:</b> {task.started_at.strftime('%Y-%m-%d %H:%M:%S')}</p>"
        
        if task.completed_at:
            detail_html += f"<p><b>完成时间:</b> {task.completed_at.strftime('%Y-%m-%d %H:%M:%S')}</p>"
        
        if task.last_error:
            detail_html += f"<h4>错误信息</h4><p style='color: red;'>{task.last_error}</p>"
        
        self.detail_text.setHtml(detail_html)
