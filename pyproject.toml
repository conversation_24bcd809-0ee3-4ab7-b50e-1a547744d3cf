[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tiktok-shop-tool"
version = "1.0.0"
description = "专业的TikTok Shop商品数据采集和分析工具"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "TikTokShopTool Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
    "Topic :: Office/Business",
]
requires-python = ">=3.8"
dependencies = [
    "PyQt6>=6.6.1",
    "pandas>=2.1.4",
    "openpyxl>=3.1.2",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.2",
    "loguru>=0.7.2",
    "pyyaml>=6.0.1",
    "python-dotenv>=1.0.0",
    "fake-useragent>=1.4.0",
    "aiohttp>=3.9.1",
    "pydantic>=2.5.2",
    "tqdm>=4.66.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "black>=23.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
]
selenium = [
    "selenium>=4.16.0",
    "undetected-chromedriver>=3.5.4",
]

[project.scripts]
tiktok-shop-tool = "src.main:main"

[project.gui-scripts]
tiktok-shop-tool-gui = "src.main:main"

[project.urls]
Homepage = "https://github.com/tiktokshoptool/tiktok-shop-tool"
Repository = "https://github.com/tiktokshoptool/tiktok-shop-tool"
Documentation = "https://tiktokshoptool.readthedocs.io/"
"Bug Reports" = "https://github.com/tiktokshoptool/tiktok-shop-tool/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.setuptools.package-data]
"src" = ["resources/icons/*", "resources/styles/*", "resources/templates/*"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [".git", "__pycache__", "build", "dist", ".eggs"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
