"""
设置对话框
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
    QComboBox, QPushButton, QGroupBox, QFormLayout,
    QMessageBox, QFileDialog, QTextEdit, QSlider
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from loguru import logger

from ..config.settings import config


class CrawlerSettingsTab(QWidget):
    """爬虫设置选项卡"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 请求设置组
        request_group = QGroupBox("请求设置")
        request_layout = QFormLayout(request_group)
        
        # 请求延迟
        self.request_delay_spin = QDoubleSpinBox()
        self.request_delay_spin.setRange(0.1, 10.0)
        self.request_delay_spin.setSingleStep(0.1)
        self.request_delay_spin.setSuffix(" 秒")
        request_layout.addRow("请求延迟:", self.request_delay_spin)
        
        # 超时设置
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 300)
        self.timeout_spin.setSuffix(" 秒")
        request_layout.addRow("请求超时:", self.timeout_spin)
        
        # 重试次数
        self.max_retries_spin = QSpinBox()
        self.max_retries_spin.setRange(0, 10)
        request_layout.addRow("最大重试次数:", self.max_retries_spin)
        
        # 并发数
        self.max_concurrent_spin = QSpinBox()
        self.max_concurrent_spin.setRange(1, 20)
        request_layout.addRow("最大并发数:", self.max_concurrent_spin)
        
        layout.addWidget(request_group)
        
        # 反爬虫设置组
        anti_bot_group = QGroupBox("反爬虫设置")
        anti_bot_layout = QFormLayout(anti_bot_group)
        
        # 随机延迟
        self.random_delay_check = QCheckBox("启用随机延迟")
        anti_bot_layout.addRow(self.random_delay_check)
        
        # 随机延迟范围
        delay_range_layout = QHBoxLayout()
        self.min_delay_spin = QDoubleSpinBox()
        self.min_delay_spin.setRange(0.1, 5.0)
        self.min_delay_spin.setSingleStep(0.1)
        self.min_delay_spin.setSuffix(" 秒")
        
        delay_range_layout.addWidget(QLabel("从"))
        delay_range_layout.addWidget(self.min_delay_spin)
        delay_range_layout.addWidget(QLabel("到"))
        
        self.max_delay_spin = QDoubleSpinBox()
        self.max_delay_spin.setRange(0.1, 10.0)
        self.max_delay_spin.setSingleStep(0.1)
        self.max_delay_spin.setSuffix(" 秒")
        delay_range_layout.addWidget(self.max_delay_spin)
        
        anti_bot_layout.addRow("随机延迟范围:", delay_range_layout)
        
        # User-Agent轮换
        self.rotate_ua_check = QCheckBox("启用User-Agent轮换")
        anti_bot_layout.addRow(self.rotate_ua_check)
        
        layout.addWidget(anti_bot_group)
        
        layout.addStretch()
    
    def load_settings(self):
        """加载设置"""
        self.request_delay_spin.setValue(config.crawler.request_delay)
        self.timeout_spin.setValue(config.crawler.timeout)
        self.max_retries_spin.setValue(config.crawler.max_retries)
        self.max_concurrent_spin.setValue(config.crawler.max_concurrent)
        
        self.min_delay_spin.setValue(config.crawler.random_delay_range[0])
        self.max_delay_spin.setValue(config.crawler.random_delay_range[1])
        
        self.random_delay_check.setChecked(True)
        self.rotate_ua_check.setChecked(True)
    
    def save_settings(self):
        """保存设置"""
        config.crawler.request_delay = self.request_delay_spin.value()
        config.crawler.timeout = self.timeout_spin.value()
        config.crawler.max_retries = self.max_retries_spin.value()
        config.crawler.max_concurrent = self.max_concurrent_spin.value()
        
        config.crawler.random_delay_range = (
            self.min_delay_spin.value(),
            self.max_delay_spin.value()
        )


class ExportSettingsTab(QWidget):
    """导出设置选项卡"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 导出格式组
        format_group = QGroupBox("导出格式")
        format_layout = QFormLayout(format_group)
        
        # 默认格式
        self.default_format_combo = QComboBox()
        self.default_format_combo.addItems(["xlsx", "csv", "json"])
        format_layout.addRow("默认格式:", self.default_format_combo)
        
        # 输出目录
        output_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setReadOnly(True)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_output_dir)
        
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(self.browse_button)
        format_layout.addRow("输出目录:", output_layout)
        
        layout.addWidget(format_group)
        
        # Excel设置组
        excel_group = QGroupBox("Excel设置")
        excel_layout = QFormLayout(excel_group)
        
        # 每个工作表最大行数
        self.max_rows_spin = QSpinBox()
        self.max_rows_spin.setRange(1000, 1000000)
        self.max_rows_spin.setSingleStep(1000)
        excel_layout.addRow("每个工作表最大行数:", self.max_rows_spin)
        
        # 包含图片
        self.include_images_check = QCheckBox("导出时包含商品图片")
        excel_layout.addRow(self.include_images_check)
        
        layout.addWidget(excel_group)
        
        layout.addStretch()
    
    def load_settings(self):
        """加载设置"""
        self.default_format_combo.setCurrentText(config.export.default_format)
        self.output_dir_edit.setText(str(config.export.output_dir))
        self.max_rows_spin.setValue(config.export.max_rows_per_sheet)
        self.include_images_check.setChecked(config.export.include_images)
    
    def save_settings(self):
        """保存设置"""
        config.export.default_format = self.default_format_combo.currentText()
        config.export.output_dir = self.output_dir_edit.text()
        config.export.max_rows_per_sheet = self.max_rows_spin.value()
        config.export.include_images = self.include_images_check.isChecked()
    
    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", str(config.export.output_dir)
        )
        if dir_path:
            self.output_dir_edit.setText(dir_path)


class UISettingsTab(QWidget):
    """界面设置选项卡"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 外观设置组
        appearance_group = QGroupBox("外观设置")
        appearance_layout = QFormLayout(appearance_group)
        
        # 主题
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["light", "dark"])
        appearance_layout.addRow("主题:", self.theme_combo)
        
        # 语言
        self.language_combo = QComboBox()
        self.language_combo.addItems(["zh_CN", "en_US"])
        appearance_layout.addRow("语言:", self.language_combo)
        
        layout.addWidget(appearance_group)
        
        # 窗口设置组
        window_group = QGroupBox("窗口设置")
        window_layout = QFormLayout(window_group)
        
        # 窗口大小
        size_layout = QHBoxLayout()
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2000)
        self.window_width_spin.setSuffix(" px")
        
        size_layout.addWidget(self.window_width_spin)
        size_layout.addWidget(QLabel("×"))
        
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1500)
        self.window_height_spin.setSuffix(" px")
        size_layout.addWidget(self.window_height_spin)
        
        window_layout.addRow("默认窗口大小:", size_layout)
        
        layout.addWidget(window_group)
        
        layout.addStretch()
    
    def load_settings(self):
        """加载设置"""
        self.theme_combo.setCurrentText(config.gui.theme)
        self.language_combo.setCurrentText(config.gui.language)
        self.window_width_spin.setValue(config.gui.window_width)
        self.window_height_spin.setValue(config.gui.window_height)
    
    def save_settings(self):
        """保存设置"""
        config.gui.theme = self.theme_combo.currentText()
        config.gui.language = self.language_combo.currentText()
        config.gui.window_width = self.window_width_spin.value()
        config.gui.window_height = self.window_height_spin.value()


class LogSettingsTab(QWidget):
    """日志设置选项卡"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 日志级别组
        level_group = QGroupBox("日志级别")
        level_layout = QFormLayout(level_group)
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        level_layout.addRow("日志级别:", self.log_level_combo)
        
        layout.addWidget(level_group)
        
        # 日志文件组
        file_group = QGroupBox("日志文件")
        file_layout = QFormLayout(file_group)
        
        # 日志目录
        log_dir_layout = QHBoxLayout()
        self.log_dir_edit = QLineEdit()
        self.log_dir_edit.setReadOnly(True)
        
        self.browse_log_button = QPushButton("浏览...")
        self.browse_log_button.clicked.connect(self.browse_log_dir)
        
        log_dir_layout.addWidget(self.log_dir_edit)
        log_dir_layout.addWidget(self.browse_log_button)
        file_layout.addRow("日志目录:", log_dir_layout)
        
        # 日志轮转
        self.log_rotation_edit = QLineEdit()
        self.log_rotation_edit.setPlaceholderText("例如: 10 MB")
        file_layout.addRow("日志轮转大小:", self.log_rotation_edit)
        
        # 日志保留
        self.log_retention_edit = QLineEdit()
        self.log_retention_edit.setPlaceholderText("例如: 30 days")
        file_layout.addRow("日志保留时间:", self.log_retention_edit)
        
        layout.addWidget(file_group)
        
        layout.addStretch()
    
    def load_settings(self):
        """加载设置"""
        self.log_level_combo.setCurrentText(config.log.level)
        self.log_dir_edit.setText(str(config.log.log_dir))
        self.log_rotation_edit.setText(config.log.rotation)
        self.log_retention_edit.setText(config.log.retention)
    
    def save_settings(self):
        """保存设置"""
        config.log.level = self.log_level_combo.currentText()
        config.log.log_dir = self.log_dir_edit.text()
        config.log.rotation = self.log_rotation_edit.text()
        config.log.retention = self.log_retention_edit.text()
    
    def browse_log_dir(self):
        """浏览日志目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择日志目录", str(config.log.log_dir)
        )
        if dir_path:
            self.log_dir_edit.setText(dir_path)


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setModal(True)
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("设置")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 爬虫设置
        self.crawler_tab = CrawlerSettingsTab()
        self.tab_widget.addTab(self.crawler_tab, "爬虫设置")
        
        # 导出设置
        self.export_tab = ExportSettingsTab()
        self.tab_widget.addTab(self.export_tab, "导出设置")
        
        # 界面设置
        self.ui_tab = UISettingsTab()
        self.tab_widget.addTab(self.ui_tab, "界面设置")
        
        # 日志设置
        self.log_tab = LogSettingsTab()
        self.tab_widget.addTab(self.log_tab, "日志设置")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.reset_button = QPushButton("重置默认")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept_settings)
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
    
    def accept_settings(self):
        """接受设置"""
        try:
            # 保存所有选项卡的设置
            self.crawler_tab.save_settings()
            self.export_tab.save_settings()
            self.ui_tab.save_settings()
            self.log_tab.save_settings()
            
            # 这里可以添加保存配置到文件的逻辑
            
            QMessageBox.information(self, "成功", "设置已保存")
            self.accept()
            
        except Exception as e:
            logger.error(f"保存设置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存设置失败: {str(e)}")
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置所有设置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 重新加载默认设置
            self.crawler_tab.load_settings()
            self.export_tab.load_settings()
            self.ui_tab.load_settings()
            self.log_tab.load_settings()
            
            QMessageBox.information(self, "完成", "设置已重置为默认值")
