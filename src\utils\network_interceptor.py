"""
网络请求拦截器
使用Chrome DevTools Protocol (CDP)拦截和捕获HTTP请求/响应
支持Chrome和Edge浏览器，与现有架构无缝集成
"""

import json
import time
import asyncio
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path
from datetime import datetime
from loguru import logger
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
# from selenium.webdriver.common.desired_capabilities import DesiredCapabilities  # 不再需要


class NetworkInterceptor:
    """网络请求拦截器"""
    
    def __init__(self, output_dir: Optional[Path] = None):
        """
        初始化网络拦截器
        
        Args:
            output_dir: 响应数据保存目录，默认为 intercepted_data
        """
        self.output_dir = output_dir or Path("intercepted_data")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 拦截配置
        self.target_patterns = [
            "product_list",  # TikTok Shop商品列表API
            "brandy_desktop/store",  # TikTok Shop店铺API
        ]
        
        # 存储拦截的数据
        self.intercepted_requests: List[Dict[str, Any]] = []
        self.intercepted_responses: List[Dict[str, Any]] = []
        
        # 回调函数
        self.request_callbacks: List[Callable] = []
        self.response_callbacks: List[Callable] = []
        
        # CDP相关
        self.driver: Optional[webdriver.Chrome] = None
        self.cdp_enabled = False
        
        logger.info(f"🕸️ [网络拦截器] 初始化完成，输出目录: {self.output_dir}")
    
    def add_target_pattern(self, pattern: str):
        """添加目标请求匹配模式"""
        if pattern not in self.target_patterns:
            self.target_patterns.append(pattern)
            logger.info(f"📡 [网络拦截器] 添加目标模式: {pattern}")
    
    def add_request_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加请求拦截回调函数"""
        self.request_callbacks.append(callback)
        logger.debug("🔗 [网络拦截器] 添加请求回调函数")
    
    def add_response_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加响应拦截回调函数"""
        self.response_callbacks.append(callback)
        logger.debug("🔗 [网络拦截器] 添加响应回调函数")
    
    def setup_driver_with_cdp(self, driver_path: str, browser_path: str,
                             browser_name: str = "Chrome") -> webdriver.Chrome:
        """
        设置支持CDP的WebDriver

        Args:
            driver_path: ChromeDriver路径
            browser_path: 浏览器路径
            browser_name: 浏览器名称

        Returns:
            配置好的WebDriver实例
        """
        try:
            # 设置Chrome选项
            options = webdriver.ChromeOptions()
            options.binary_location = browser_path

            # 启用CDP和网络域
            options.add_argument("--enable-network-service-logging")
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_argument("--disable-blink-features=AutomationControlled")

            # 设置性能日志 (Selenium 4.x 兼容方式)
            options.set_capability('goog:loggingPrefs', {
                'performance': 'ALL',
                'browser': 'ALL'
            })

            # 创建服务
            service = Service(driver_path)

            # 创建WebDriver (Selenium 4.x 兼容)
            self.driver = webdriver.Chrome(
                service=service,
                options=options
            )

            # 启用CDP网络域
            self.driver.execute_cdp_cmd('Network.enable', {})
            self.driver.execute_cdp_cmd('Runtime.enable', {})

            # 隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.cdp_enabled = True
            logger.info(f"✅ [网络拦截器] {browser_name}浏览器CDP配置完成")

            return self.driver

        except Exception as e:
            logger.error(f"❌ [网络拦截器] CDP配置失败: {str(e)}")
            raise
    
    def is_target_request(self, url: str) -> bool:
        """检查是否为目标请求"""
        return any(pattern in url for pattern in self.target_patterns)
    
    def extract_network_logs(self) -> List[Dict[str, Any]]:
        """从浏览器性能日志中提取网络请求"""
        if not self.driver:
            return []
        
        try:
            # 获取性能日志
            logs = self.driver.get_log('performance')
            network_events = []
            
            for log in logs:
                message = json.loads(log['message'])
                
                # 过滤网络相关事件
                if message.get('message', {}).get('method', '').startswith('Network.'):
                    network_events.append({
                        'timestamp': log['timestamp'],
                        'level': log['level'],
                        'message': message
                    })
            
            return network_events
            
        except Exception as e:
            logger.error(f"❌ [网络拦截器] 提取网络日志失败: {str(e)}")
            return []
    
    def process_network_events(self, events: List[Dict[str, Any]]) -> Dict[str, List[Dict]]:
        """处理网络事件，提取请求和响应，并获取响应体内容"""
        requests = {}
        responses = []
        completed_responses = {}  # 存储已完成的响应

        # 第一遍：收集请求和响应基本信息
        for event in events:
            message = event.get('message', {}).get('message', {})
            method = message.get('method', '')
            params = message.get('params', {})

            if method == 'Network.requestWillBeSent':
                # 请求发送事件
                request_id = params.get('requestId')
                request_data = params.get('request', {})
                url = request_data.get('url', '')

                if self.is_target_request(url):
                    requests[request_id] = {
                        'requestId': request_id,
                        'url': url,
                        'method': request_data.get('method', ''),
                        'headers': request_data.get('headers', {}),
                        'timestamp': event['timestamp'],
                        'postData': request_data.get('postData')
                    }

                    # 触发请求回调
                    for callback in self.request_callbacks:
                        try:
                            callback(requests[request_id])
                        except Exception as e:
                            logger.error(f"❌ [网络拦截器] 请求回调执行失败: {str(e)}")

            elif method == 'Network.responseReceived':
                # 响应接收事件
                request_id = params.get('requestId')
                response_data = params.get('response', {})
                url = response_data.get('url', '')

                if request_id in requests and self.is_target_request(url):
                    response_info = {
                        'requestId': request_id,
                        'url': url,
                        'status': response_data.get('status'),
                        'statusText': response_data.get('statusText'),
                        'headers': response_data.get('headers', {}),
                        'mimeType': response_data.get('mimeType'),
                        'timestamp': event['timestamp'],
                        'request': requests[request_id],
                        'body': None  # 初始化为None，稍后获取
                    }
                    completed_responses[request_id] = response_info

            elif method == 'Network.loadingFinished':
                # 响应加载完成事件
                request_id = params.get('requestId')
                if request_id in completed_responses:
                    # 标记响应已完成，可以获取响应体
                    completed_responses[request_id]['loading_finished'] = True

        # 第二遍：获取响应体内容
        for request_id, response_info in completed_responses.items():
            if response_info.get('loading_finished', False):
                logger.debug(f"🔍 [网络拦截器] 尝试获取响应体: {request_id}")

                # 获取响应体内容
                body = self.get_response_body(request_id)
                if body:
                    response_info['body'] = body
                    response_info['body_length'] = len(body)
                    logger.info(f"✅ [网络拦截器] 成功获取响应体: {request_id} ({len(body)} 字符)")
                else:
                    response_info['body'] = None
                    response_info['body_length'] = 0
                    logger.warning(f"⚠️ [网络拦截器] 响应体获取失败: {request_id}")

                # 移除临时标记
                response_info.pop('loading_finished', None)

                # 添加到最终响应列表
                responses.append(response_info)

                # 触发响应回调
                for callback in self.response_callbacks:
                    try:
                        callback(response_info)
                    except Exception as e:
                        logger.error(f"❌ [网络拦截器] 响应回调执行失败: {str(e)}")

        logger.info(f"📊 [网络拦截器] 处理完成 - 请求: {len(requests)}, 响应: {len(responses)}")
        return {'requests': list(requests.values()), 'responses': responses}
    
    def get_response_body(self, request_id: str) -> Optional[str]:
        """获取响应体内容（同步版本）"""
        if not self.driver or not self.cdp_enabled:
            return None

        try:
            # 使用CDP获取响应体
            response = self.driver.execute_cdp_cmd('Network.getResponseBody', {
                'requestId': request_id
            })

            body = response.get('body')
            base64_encoded = response.get('base64Encoded', False)

            if base64_encoded:
                import base64
                body = base64.b64decode(body).decode('utf-8')

            logger.debug(f"✅ [网络拦截器] 成功获取响应体 {request_id}: {len(body) if body else 0} 字符")
            return body

        except Exception as e:
            logger.debug(f"⚠️ [网络拦截器] 获取响应体失败 {request_id}: {str(e)}")
            return None
    
    def save_intercepted_data(self, data: Dict[str, Any], filename: Optional[str] = None) -> Path:
        """保存拦截的数据到JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"intercepted_data_{timestamp}.json"

        output_path = self.output_dir / filename

        try:
            # 添加保存时的元数据
            save_data = {
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_requests': len(data.get('requests', [])),
                    'total_responses': len(data.get('responses', [])),
                    'responses_with_body': len([r for r in data.get('responses', []) if r.get('body')]),
                    'target_patterns': self.target_patterns
                },
                'data': data
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            # 详细的保存统计
            responses = data.get('responses', [])
            responses_with_body = [r for r in responses if r.get('body')]

            logger.info(f"💾 [网络拦截器] 数据已保存: {output_path}")
            logger.info(f"📊 [网络拦截器] 保存统计:")
            logger.info(f"   总请求数: {len(data.get('requests', []))}")
            logger.info(f"   总响应数: {len(responses)}")
            logger.info(f"   包含响应体的响应数: {len(responses_with_body)}")

            # 显示每个响应的详细信息
            for i, response in enumerate(responses_with_body):
                url = response.get('url', 'Unknown')
                body_length = response.get('body_length', 0)
                logger.info(f"   响应 {i+1}: {url} ({body_length} 字符)")

            return output_path

        except Exception as e:
            logger.error(f"❌ [网络拦截器] 保存数据失败: {str(e)}")
            raise
    
    def start_monitoring(self):
        """开始网络监控"""
        if not self.driver or not self.cdp_enabled:
            logger.error("❌ [网络拦截器] 未配置WebDriver或CDP未启用")
            return
        
        logger.info("🚀 [网络拦截器] 开始网络监控...")
        
        # 清空之前的日志
        self.driver.get_log('performance')
    
    def stop_monitoring_and_extract(self) -> Dict[str, Any]:
        """停止监控并提取数据"""
        if not self.driver:
            return {'requests': [], 'responses': []}
        
        logger.info("🛑 [网络拦截器] 停止监控并提取数据...")
        
        # 提取网络日志
        events = self.extract_network_logs()
        
        # 处理事件
        processed_data = self.process_network_events(events)
        
        # 保存到实例变量
        self.intercepted_requests.extend(processed_data['requests'])
        self.intercepted_responses.extend(processed_data['responses'])
        
        logger.info(f"📊 [网络拦截器] 提取完成 - 请求: {len(processed_data['requests'])}, 响应: {len(processed_data['responses'])}")
        
        return processed_data
    
    def get_intercepted_data(self) -> Dict[str, Any]:
        """获取所有拦截的数据"""
        return {
            'requests': self.intercepted_requests,
            'responses': self.intercepted_responses,
            'summary': {
                'total_requests': len(self.intercepted_requests),
                'total_responses': len(self.intercepted_responses),
                'target_patterns': self.target_patterns
            }
        }
    
    def clear_data(self):
        """清空拦截的数据"""
        self.intercepted_requests.clear()
        self.intercepted_responses.clear()
        logger.info("🧹 [网络拦截器] 数据已清空")
    
    def close(self):
        """关闭拦截器"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("🔒 [网络拦截器] WebDriver已关闭")
            except Exception as e:
                logger.error(f"❌ [网络拦截器] 关闭WebDriver失败: {str(e)}")
        
        self.driver = None
        self.cdp_enabled = False
