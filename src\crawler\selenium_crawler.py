"""
Selenium浏览器模式爬虫 - 用于处理滑块验证等反爬虫措施
"""

import time
import json
import asyncio
import threading
import re
from typing import Dict, List, Optional, Any, Callable
from urllib.parse import urlparse
from loguru import logger
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError as FutureTimeoutError

# Selenium相关导入（可选依赖）
try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.common.exceptions import TimeoutException, WebDriverException
    HAS_SELENIUM = True
except ImportError:
    logger.warning("⚠️ [Selenium] 未安装Selenium相关依赖，浏览器模式将不可用")
    HAS_SELENIUM = False
    uc = None
    WebDriverWait = None
    EC = None
    ActionChains = None
    TimeoutException = Exception
    WebDriverException = Exception

from ..config.settings import config
from ..utils.browser_config import BrowserConfigValidator, ChromeDriverManager
from ..utils.sales_detector import SalesDetector, ContentIncrementDetector
from ..utils.pdp_page_loader import PDPPageLoader


class BrowserPool:
    """浏览器实例池管理器"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.pool = []
            self.max_pool_size = 3
            self.initialization_timeout = 60  # 60秒超时
            self.executor = ThreadPoolExecutor(max_workers=2)
            self.initialized = True
            logger.info("🏊 [浏览器池] 初始化完成")

    def _create_optimized_chrome_options(self, headless: bool = False) -> 'uc.ChromeOptions':
        """创建优化的Chrome选项"""
        options = uc.ChromeOptions()

        # 基础性能优化选项（增加渲染器稳定性选项）
        performance_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-software-rasterizer',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-background-networking',
            '--disable-sync',
            '--disable-default-apps',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-preconnect',
            '--disable-prefetch',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-logging',
            '--disable-log-file',
            '--silent',
            '--disable-component-extensions-with-background-pages',
            '--disable-background-mode',
            '--window-size=1920,1080',
            '--start-maximized',
            # 增加渲染器稳定性选项
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-blink-features=AutomationControlled',
            '--disable-dev-tools',
            '--disable-infobars',
            '--disable-notifications',
            '--disable-popup-blocking',
            '--disable-prompt-on-repost',
            '--disable-hang-monitor',
            '--disable-client-side-phishing-detection',
            '--disable-component-update',
            '--disable-domain-reliability',
            '--disable-features=AudioServiceOutOfProcess',
            '--renderer-process-limit=1',  # 限制渲染器进程数量
            '--max_old_space_size=4096'   # 增加内存限制
        ]

        for arg in performance_args:
            options.add_argument(arg)

        if headless:
            options.add_argument('--headless=new')  # 使用新的headless模式

        # 内存优化
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=4096')

        # 网络优化
        options.add_argument('--aggressive-cache-discard')
        options.add_argument('--disable-hang-monitor')

        return options

    def _create_browser_sync(self, headless: bool = False) -> Optional['uc.Chrome']:
        """同步创建浏览器实例（增强版）"""
        try:
            logger.info("🚀 [浏览器池] 开始创建浏览器实例，请耐心等待...")
            logger.info("⏳ [浏览器池] 首次创建可能需要15-30秒，正在进行环境检测...")
            start_time = time.time()

            # 首先验证浏览器环境
            logger.info("🔍 [浏览器池] 正在检测Chrome浏览器环境...")
            validator = BrowserConfigValidator()
            env_result = validator.validate_browser_environment()

            if not env_result["chrome_installed"]:
                logger.error("❌ [浏览器池] Chrome浏览器未安装")
                for recommendation in env_result["recommendations"]:
                    logger.error(f"💡 [浏览器池] 建议: {recommendation}")
                return None

            logger.info(f"✅ [浏览器池] Chrome环境检测完成")
            logger.info(f"   📍 Chrome路径: {env_result['chrome_path']}")
            logger.info(f"   🏷️ Chrome版本: {env_result['chrome_version']}")

            logger.info("⚙️ [浏览器池] 正在配置浏览器选项...")
            options = self._create_optimized_chrome_options(headless)

            # 尝试使用本地ChromeDriver（如果可用）
            driver_path = env_result.get("chromedriver_path")
            chrome_path = env_result.get("chrome_path")

            logger.info("🔧 [浏览器池] 正在启动Chrome浏览器...")
            logger.info("⏳ [浏览器池] 这可能需要10-20秒，请耐心等待...")

            try:
                # 方案1: 使用本地ChromeDriver和Chrome路径
                if driver_path and chrome_path:
                    logger.info(f"� [浏览器池] 使用本地ChromeDriver: {driver_path}")
                    logger.info("🚀 [浏览器池] 正在启动浏览器实例（方案1：本地模式）...")

                    driver = uc.Chrome(
                        options=options,
                        driver_executable_path=driver_path,
                        browser_executable_path=chrome_path,
                        user_data_dir=None,
                        headless=headless,
                        use_subprocess=True,
                        debug=False
                    )
                    logger.info("✅ [浏览器池] 本地模式启动成功")
                else:
                    raise Exception("本地ChromeDriver不可用，尝试自动下载模式")

            except Exception as local_error:
                logger.warning(f"⚠️ [浏览器池] 本地ChromeDriver模式失败: {str(local_error)}")

                # 方案2: 使用undetected-chromedriver的自动下载模式
                try:
                    logger.info("🔄 [浏览器池] 尝试自动下载模式...")
                    logger.info("📥 [浏览器池] 正在自动下载ChromeDriver，首次可能较慢...")
                    logger.info("⏳ [浏览器池] 请耐心等待，这可能需要20-30秒...")

                    driver = uc.Chrome(
                        options=options,
                        version_main=None,  # 自动检测版本
                        driver_executable_path=None,  # 自动下载
                        browser_executable_path=chrome_path if chrome_path else None,
                        user_data_dir=None,
                        headless=headless,
                        use_subprocess=True,
                        debug=False
                    )
                    logger.info("✅ [浏览器池] 自动下载模式启动成功")

                except Exception as auto_error:
                    logger.error(f"❌ [浏览器池] 自动下载模式也失败: {str(auto_error)}")

                    # 方案3: 最简配置模式
                    try:
                        logger.info("🔄 [浏览器池] 尝试最简配置模式...")
                        logger.info("🚀 [浏览器池] 正在启动浏览器实例（方案3：最简模式）...")

                        driver = uc.Chrome(
                            options=options,
                            headless=headless,
                            use_subprocess=False,  # 禁用子进程
                            debug=True  # 启用调试模式获取更多信息
                        )
                        logger.info("✅ [浏览器池] 最简配置模式启动成功")
                    except Exception as simple_error:
                        logger.error(f"❌ [浏览器池] 所有创建方案都失败")
                        logger.error(f"   - 本地模式: {str(local_error)}")
                        logger.error(f"   - 自动下载: {str(auto_error)}")
                        logger.error(f"   - 最简模式: {str(simple_error)}")

                        # 提供详细的故障排除建议
                        self._provide_troubleshooting_advice(env_result)
                        return None

            # 快速设置超时
            logger.info("⚙️ [浏览器池] 正在配置浏览器参数...")
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)

            # 执行必要的反检测脚本
            logger.info("🛡️ [浏览器池] 正在执行反检测脚本...")
            try:
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                logger.info("✅ [浏览器池] 反检测脚本执行成功")
            except Exception as script_error:
                logger.warning(f"⚠️ [浏览器池] 反检测脚本执行失败: {str(script_error)}")

            elapsed = time.time() - start_time
            logger.info("🎉 [浏览器池] 浏览器实例创建完成！")
            logger.info(f"⏱️ [浏览器池] 总耗时: {elapsed:.2f}秒")
            logger.info("🚀 [浏览器池] 浏览器已就绪，可以开始爬取任务")

            return driver

        except Exception as e:
            logger.error(f"❌ [浏览器池] 浏览器实例创建失败: {str(e)}")
            return None

    def _provide_troubleshooting_advice(self, env_result: dict):
        """提供故障排除建议"""
        logger.error("🔧 [浏览器池] 故障排除建议:")

        if not env_result.get("network_available"):
            logger.error("   1. 检查网络连接是否正常")
            logger.error("   2. 如果使用代理，请确保代理设置正确")

        if not env_result.get("download_service_available"):
            logger.error("   3. ChromeDriver下载服务不可访问，可能需要:")
            logger.error("      - 使用VPN或代理")
            logger.error("      - 手动下载ChromeDriver并放置到指定目录")

        if env_result.get("chrome_version"):
            logger.error(f"   4. 当前Chrome版本: {env_result['chrome_version']}")
            logger.error("      - 可以尝试更新Chrome到最新版本")
            logger.error("      - 或手动下载对应版本的ChromeDriver")

        logger.error("   5. 临时解决方案: 可以尝试使用直连模式（不使用浏览器）")

        for recommendation in env_result.get("recommendations", []):
            logger.error(f"   💡 {recommendation}")

    async def get_browser(self, headless: bool = False) -> Optional['uc.Chrome']:
        """获取浏览器实例（异步，增强版会话验证）"""
        try:
            # 检查池中是否有可用且有效的实例
            while self.pool:
                driver = self.pool.pop(0)
                if self._validate_browser_session(driver):
                    logger.info("♻️ [浏览器池] 复用现有浏览器实例，立即可用")
                    return driver
                else:
                    logger.warning("⚠️ [浏览器池] 池中的浏览器会话已失效，移除并继续查找")
                    self._force_quit_browser(driver)

            # 创建新实例（在线程池中执行以避免阻塞）
            logger.info("🔧 [浏览器池] 池中无可用实例，开始创建新浏览器...")
            logger.info("⏳ [浏览器池] 预计需要15-30秒，请耐心等待...")

            loop = asyncio.get_event_loop()
            future = self.executor.submit(self._create_browser_sync, headless)

            try:
                driver = await asyncio.wait_for(
                    loop.run_in_executor(None, future.result, self.initialization_timeout),
                    timeout=self.initialization_timeout + 10
                )
                if driver:
                    logger.info("🎉 [浏览器池] 新浏览器实例获取成功，可以开始使用")
                return driver
            except asyncio.TimeoutError:
                logger.error(f"⏰ [浏览器池] 浏览器初始化超时（{self.initialization_timeout}秒）")
                logger.error("💡 [浏览器池] 建议检查网络连接或增加超时时间")
                future.cancel()
                return None

        except Exception as e:
            logger.error(f"❌ [浏览器池] 获取浏览器实例失败: {str(e)}")
            return None

    def return_browser(self, driver: 'uc.Chrome'):
        """归还浏览器实例到池中（增强版会话验证）"""
        try:
            if not driver:
                logger.warning("⚠️ [浏览器池] 尝试归还空的浏览器实例")
                return

            # 验证浏览器会话是否仍然有效
            if not self._validate_browser_session(driver):
                logger.warning("⚠️ [浏览器池] 浏览器会话已失效，直接关闭")
                self._force_quit_browser(driver)
                return

            if len(self.pool) < self.max_pool_size:
                # 清理浏览器状态
                if self._clean_browser_state(driver):
                    self.pool.append(driver)
                    logger.info("♻️ [浏览器池] 浏览器实例已归还到池中")
                else:
                    logger.warning("⚠️ [浏览器池] 浏览器状态清理失败，直接关闭")
                    self._force_quit_browser(driver)
            else:
                # 池已满，直接关闭
                logger.info("🔒 [浏览器池] 池已满，关闭浏览器实例")
                self._force_quit_browser(driver)
        except Exception as e:
            logger.error(f"❌ [浏览器池] 归还浏览器实例失败: {str(e)}")
            # 发生异常时强制关闭浏览器
            self._force_quit_browser(driver)

    def _validate_browser_session(self, driver: 'uc.Chrome') -> bool:
        """验证浏览器会话是否有效"""
        try:
            # 尝试获取当前URL来验证会话
            current_url = driver.current_url
            # 尝试执行简单的JavaScript来验证会话
            driver.execute_script("return document.readyState;")
            logger.debug("✅ [浏览器池] 浏览器会话验证通过")
            return True
        except Exception as e:
            logger.warning(f"⚠️ [浏览器池] 浏览器会话验证失败: {str(e)}")
            return False

    def _clean_browser_state(self, driver: 'uc.Chrome') -> bool:
        """清理浏览器状态（增强版容错处理）"""
        try:
            # 清理cookies
            try:
                driver.delete_all_cookies()
                logger.debug("✅ [浏览器池] Cookies清理完成")
            except Exception as e:
                logger.debug(f"⚠️ [浏览器池] Cookies清理失败: {str(e)}")

            # 清理本地存储（分别尝试，避免一个失败影响其他）
            try:
                driver.execute_script("try { window.localStorage.clear(); } catch(e) { console.log('localStorage清理失败:', e); }")
                logger.debug("✅ [浏览器池] localStorage清理完成")
            except Exception as e:
                logger.debug(f"⚠️ [浏览器池] localStorage清理失败: {str(e)}")

            try:
                driver.execute_script("try { window.sessionStorage.clear(); } catch(e) { console.log('sessionStorage清理失败:', e); }")
                logger.debug("✅ [浏览器池] sessionStorage清理完成")
            except Exception as e:
                logger.debug(f"⚠️ [浏览器池] sessionStorage清理失败: {str(e)}")

            # 导航到空白页面
            try:
                driver.get("about:blank")
                logger.debug("✅ [浏览器池] 导航到空白页面完成")
            except Exception as e:
                logger.debug(f"⚠️ [浏览器池] 导航到空白页面失败: {str(e)}")

            logger.debug("✅ [浏览器池] 浏览器状态清理完成")
            return True
        except Exception as e:
            logger.warning(f"⚠️ [浏览器池] 浏览器状态清理失败: {str(e)}")
            return False

    def _force_quit_browser(self, driver: 'uc.Chrome'):
        """强制关闭浏览器实例"""
        try:
            if driver:
                driver.quit()
                logger.debug("🔒 [浏览器池] 浏览器实例已强制关闭")
        except Exception as e:
            logger.debug(f"⚠️ [浏览器池] 强制关闭浏览器时出现异常: {str(e)}")
            # 即使quit()失败，也尝试终止进程
            try:
                import psutil
                import os
                # 查找并终止相关的Chrome进程
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if 'chrome' in proc.info['name'].lower():
                            proc.terminate()
                    except:
                        pass
            except ImportError:
                logger.debug("⚠️ [浏览器池] psutil未安装，无法强制终止Chrome进程")
            except Exception as cleanup_error:
                logger.debug(f"⚠️ [浏览器池] 进程清理异常: {str(cleanup_error)}")

    def close_all(self):
        """关闭所有浏览器实例（增强版彻底清理）"""
        try:
            logger.info("🔒 [浏览器池] 开始关闭所有浏览器实例...")

            # 关闭池中的所有浏览器
            for i, driver in enumerate(self.pool):
                try:
                    logger.debug(f"🔒 [浏览器池] 关闭第 {i+1} 个浏览器实例")
                    self._force_quit_browser(driver)
                except Exception as e:
                    logger.debug(f"⚠️ [浏览器池] 关闭第 {i+1} 个浏览器时出现异常: {str(e)}")

            self.pool.clear()

            # 关闭线程池
            try:
                self.executor.shutdown(wait=True, timeout=5)
                logger.debug("✅ [浏览器池] 线程池已关闭")
            except Exception as e:
                logger.debug(f"⚠️ [浏览器池] 关闭线程池时出现异常: {str(e)}")

            # 强制清理残留的Chrome进程
            self._cleanup_chrome_processes()

            logger.info("🔒 [浏览器池] 所有浏览器实例已关闭")
        except Exception as e:
            logger.error(f"❌ [浏览器池] 关闭浏览器池失败: {str(e)}")

    def _cleanup_chrome_processes(self):
        """清理残留的Chrome进程"""
        try:
            import psutil
            import time

            chrome_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        # 检查是否是我们启动的Chrome进程
                        cmdline = proc.info.get('cmdline', [])
                        if any('--remote-debugging-port' in arg for arg in cmdline):
                            chrome_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if chrome_processes:
                logger.info(f"🧹 [浏览器池] 发现 {len(chrome_processes)} 个残留Chrome进程，开始清理")

                # 先尝试优雅终止
                for proc in chrome_processes:
                    try:
                        proc.terminate()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                # 等待进程终止
                time.sleep(2)

                # 强制杀死仍然存在的进程
                for proc in chrome_processes:
                    try:
                        if proc.is_running():
                            proc.kill()
                            logger.debug(f"🔪 [浏览器池] 强制终止Chrome进程 PID: {proc.pid}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                logger.info("✅ [浏览器池] Chrome进程清理完成")
            else:
                logger.debug("✅ [浏览器池] 未发现残留Chrome进程")

        except ImportError:
            logger.debug("⚠️ [浏览器池] psutil未安装，跳过进程清理")
        except Exception as e:
            logger.debug(f"⚠️ [浏览器池] 进程清理异常: {str(e)}")


class CaptchaDetectionResult:
    """验证码检测结果"""
    
    def __init__(self, has_captcha: bool = False, captcha_type: str = "", 
                 element_selector: str = "", confidence: float = 0.0):
        self.has_captcha = has_captcha
        self.captcha_type = captcha_type  # 'slider', 'image', 'text', etc.
        self.element_selector = element_selector
        self.confidence = confidence
        self.detected_at = time.time()


class SeleniumCrawler:
    """Selenium浏览器模式爬虫（优化版）"""

    def __init__(self, headless: bool = False, user_interaction_callback: Optional[Callable] = None):
        """
        初始化Selenium爬虫

        Args:
            headless: 是否使用无头模式（默认False，显示浏览器窗口）
            user_interaction_callback: 用户交互回调函数
        """
        if not HAS_SELENIUM:
            raise ImportError("Selenium相关依赖未安装，请运行: pip install selenium undetected-chromedriver")

        self.driver = None
        self.headless = headless
        self.user_interaction_callback = user_interaction_callback
        self.session_cookies = {}
        self.is_initialized = False

        # 浏览器池实例
        self.browser_pool = BrowserPool()

        # 初始化进度回调
        self.initialization_progress_callback = None
        
        # 验证码检测配置 - 增强版
        self.captcha_selectors = {
            'captcha': [
                # 通用验证码选择器
                'div[class*="captcha"]',
                'div[id*="captcha"]',
                '[data-testid*="captcha"]',
                '.captcha-container',
                '.captcha-wrapper',
                '.secsdk-captcha-wrapper',
                # 滑块验证码
                'div[class*="slider"]',
                'div[id*="slider"]',
                '.slider-container',
                '.slider-wrapper',
                # 验证相关
                'div[class*="verify"]',
                'div[id*="verify"]',
                '.verify-container',
                '.verify-wrapper',
                # 挑战相关
                'div[class*="challenge"]',
                'div[id*="challenge"]',
                '.challenge-container',
                # 其他常见验证码元素
                '[class*="verification"]',
                '[id*="verification"]',
                '[class*="robot"]',
                '[id*="robot"]',
                '[class*="human"]',
                '[id*="human"]'
            ],
            'image': [
                'div[class*="image-captcha"]',
                'div[class*="puzzle"]',
                '.image-verify'
            ]
        }
        
        # 超时配置（优化以减少渲染器超时）
        self.page_load_timeout = 20  # 减少页面加载超时
        self.element_wait_timeout = 15  # 减少元素等待超时
        self.captcha_wait_timeout = 300  # 5分钟等待用户完成验证
        self.page_source_timeout = 10  # 页面内容获取超时

        # 加载更多配置和检测器
        self.load_more_config = config.load_more
        self.sales_detector = SalesDetector(self.load_more_config.sales_detection_patterns)
        self.content_detector = ContentIncrementDetector(
            min_increase=self.load_more_config.min_content_increase,
            increase_ratio=self.load_more_config.content_increase_ratio
        )

        # PDP页面加载器（延迟初始化）
        self.pdp_loader = None
    
    async def _report_progress(self, message: str, progress: int = 0):
        """报告初始化进度"""
        logger.info(f"🔄 [Selenium] {message}")
        if self.initialization_progress_callback:
            try:
                await self.initialization_progress_callback({
                    'type': 'initialization_progress',
                    'message': message,
                    'progress': progress
                })
            except Exception as e:
                logger.debug(f"进度回调失败: {str(e)}")

    async def initialize(self) -> bool:
        """优化的浏览器初始化方法"""
        try:
            if self.is_initialized:
                return True

            await self._report_progress("开始初始化浏览器...", 10)
            start_time = time.time()

            # 使用浏览器池获取实例
            await self._report_progress("从浏览器池获取实例...", 30)
            self.driver = await self.browser_pool.get_browser(self.headless)

            if not self.driver:
                logger.error("❌ [Selenium] 无法从浏览器池获取实例")
                return False

            await self._report_progress("配置浏览器参数...", 60)

            # 快速配置超时（已在池中预设）
            try:
                self.driver.set_page_load_timeout(self.page_load_timeout)
                self.driver.implicitly_wait(self.element_wait_timeout)
            except Exception as e:
                logger.debug(f"设置超时参数失败: {str(e)}")

            await self._report_progress("执行反检测脚本...", 80)

            # 执行反检测脚本（如果尚未执行）
            try:
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            except Exception as e:
                logger.debug(f"反检测脚本执行失败: {str(e)}")

            await self._report_progress("初始化完成", 100)

            self.is_initialized = True
            elapsed = time.time() - start_time
            logger.info(f"✅ [Selenium] 浏览器初始化成功，总耗时: {elapsed:.2f}秒")

            # 通知用户初始化完成
            if self.user_interaction_callback:
                try:
                    await self.user_interaction_callback({
                        'type': 'browser_initialized',
                        'message': f'浏览器初始化完成，耗时: {elapsed:.2f}秒',
                        'elapsed_time': elapsed
                    })
                except Exception as e:
                    logger.debug(f"用户回调失败: {str(e)}")

            return True

        except Exception as e:
            logger.error(f"❌ [Selenium] 浏览器初始化失败: {str(e)}")
            await self._report_progress(f"初始化失败: {str(e)}", 0)
            return False
    
    async def close(self, force_quit: bool = False):
        """关闭浏览器（增强版会话处理）"""
        try:
            if self.driver:
                if force_quit:
                    # 强制关闭，不归还到池中
                    self.browser_pool._force_quit_browser(self.driver)
                    logger.info("🔒 [Selenium] 浏览器已强制关闭")
                else:
                    # 归还到浏览器池（会自动验证会话有效性）
                    self.browser_pool.return_browser(self.driver)
                    logger.info("♻️ [Selenium] 浏览器已归还到池中")

                self.driver = None
                self.is_initialized = False

                # 清理PDP加载器
                if hasattr(self, 'pdp_loader'):
                    self.pdp_loader = None

        except Exception as e:
            logger.error(f"❌ [Selenium] 关闭浏览器失败: {str(e)}")
            # 发生异常时强制清理
            if self.driver:
                self.browser_pool._force_quit_browser(self.driver)
                self.driver = None
                self.is_initialized = False

    def set_initialization_progress_callback(self, callback: Callable):
        """设置初始化进度回调函数"""
        self.initialization_progress_callback = callback


    
    def detect_captcha(self) -> CaptchaDetectionResult:
        """检测页面中的验证码 - 增强版"""
        try:
            logger.debug("🔍 [Selenium] 检测验证码...")

            # 1. 检测页面源码中的验证码内容
            try:
                page_source = self.driver.page_source.lower()
                captcha_content_keywords = [
                    'captcha', 'verify', 'challenge', 'puzzle',
                    'human',
                    '验证码', '滑块', '验证', '挑战'
                ]

                for keyword in captcha_content_keywords:
                    if keyword in page_source:
                        logger.warning(f"🚫 [Selenium] 在页面内容中检测到验证码关键词: {keyword}")
                        return CaptchaDetectionResult(
                            has_captcha=True,
                            captcha_type='content',
                            element_selector='',
                            confidence=0.95
                        )
            except:
                pass

            # 2. 检测验证码元素
            for selector in self.captcha_selectors['captcha']:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and any(elem.is_displayed() for elem in elements):
                        logger.warning(f"🚫 [Selenium] 检测到验证码元素: {selector}")
                        return CaptchaDetectionResult(
                            has_captcha=True,
                            captcha_type='element',
                            element_selector=selector,
                            confidence=0.9
                        )
                except:
                    continue

            # 3. 检测图片验证码
            for selector in self.captcha_selectors['image']:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and any(elem.is_displayed() for elem in elements):
                        logger.warning(f"🖼️ [Selenium] 检测到图片验证码: {selector}")
                        return CaptchaDetectionResult(
                            has_captcha=True,
                            captcha_type='image',
                            element_selector=selector,
                            confidence=0.8
                        )
                except:
                    continue

            # 4. 检测页面标题和URL中的验证码关键词
            try:
                title = self.driver.title.lower()
                url = self.driver.current_url.lower()
                captcha_keywords = ['captcha', 'verify', 'challenge']

                for keyword in captcha_keywords:
                    if keyword in title or keyword in url:
                        logger.warning(f"🔍 [Selenium] 在页面标题/URL中检测到验证码关键词: {keyword}")
                        return CaptchaDetectionResult(
                            has_captcha=True,
                            captcha_type='url',
                            element_selector='',
                            confidence=0.7
                        )
            except:
                pass

            logger.debug("✅ [Selenium] 未检测到验证码")
            return CaptchaDetectionResult(has_captcha=False)

        except Exception as e:
            logger.error(f"❌ [Selenium] 验证码检测失败: {str(e)}")
            return CaptchaDetectionResult(has_captcha=False)
    
    async def wait_for_captcha_completion(self, max_wait_time: int = 300) -> bool:
        """静默等待验证码完成 - 自动检测模式"""
        try:
            logger.info(f"⏳ [Selenium] 静默等待验证码完成（最多等待{max_wait_time}秒）...")

            start_time = time.time()
            check_interval = 2  # 每2秒检查一次
            check_count = 0

            while time.time() - start_time < max_wait_time:
                check_count += 1

                # 刷新页面内容并重新检测
                try:
                    # 重新获取页面状态
                    self.driver.execute_script("return document.readyState")

                    # 检查验证码是否还存在
                    captcha_result = self.detect_captcha()

                    if not captcha_result.has_captcha:
                        logger.info(f"✅ [Selenium] 验证码已自动完成（检测{check_count}次）")
                        return True

                    # 记录检测进度（每10次记录一次）
                    if check_count % 10 == 0:
                        elapsed = int(time.time() - start_time)
                        logger.debug(f"🔄 [Selenium] 继续等待验证码完成... 已检测{check_count}次，耗时{elapsed}秒")

                except Exception as e:
                    logger.debug(f"检测过程中出现异常: {str(e)}")

                # 等待一段时间后再次检查
                await asyncio.sleep(check_interval)

            logger.warning(f"⏰ [Selenium] 验证码等待超时（{max_wait_time}秒），共检测{check_count}次")
            return False

        except Exception as e:
            logger.error(f"❌ [Selenium] 等待验证码完成失败: {str(e)}")
            return False
    
    async def get_page_content(self, url: str, wait_for_load: bool = True, load_more: bool = True, filter_conditions: Dict[str, Any] = None) -> Optional[str]:
        """
        获取页面内容 - 基于页面状态的智能判断机制

        Args:
            url: 要访问的页面URL
            wait_for_load: 是否等待页面加载完成
            load_more: 是否自动点击"加载更多"按钮获取更多数据
            filter_conditions: 过滤条件，用于智能加载更多数据时的销量检测

        Returns:
            页面内容字符串，如果失败则返回None
        """
        try:
            if not self.is_initialized:
                if not await self.initialize():
                    return None

            # 检查是否是PDP页面，如果是则使用简化的加载逻辑
            if self._is_pdp_url(url):
                logger.info(f"🔍 [Selenium] 检测到PDP页面，使用简化加载逻辑")
                return await self._get_pdp_page_content_simplified(url)

            logger.info(f"🌐 [Selenium] 访问页面: {url}")

            # 访问页面
            self.driver.get(url)

            if wait_for_load:
                # 初始等待阶段：等待3秒让页面进行基础加载
                logger.debug("⏳ [Selenium] 初始等待3秒，让页面进行基础加载...")
                await asyncio.sleep(3.5)

                # 开始智能页面状态检测和处理
                return await self._handle_page_content_with_smart_detection(url, load_more, filter_conditions)
            else:
                # 不等待加载，直接返回当前页面内容
                content = self.driver.page_source
                logger.info(f"✅ [Selenium] 直接获取页面内容，长度: {len(content)}")
                return content

        except TimeoutException:
            logger.error(f"⏰ [Selenium] 页面加载超时: {url}")
            return None
        except WebDriverException as e:
            logger.error(f"🌐 [Selenium] 浏览器错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ [Selenium] 获取页面内容失败: {str(e)}")
            return None

    def _is_pdp_url(self, url: str) -> bool:
        """检查是否是PDP页面URL"""
        try:
            return '/shop/pdp/' in url.lower()
        except:
            return False

    async def _get_pdp_page_content_simplified(self, url: str) -> Optional[str]:
        """
        获取PDP页面内容（简化版）

        使用固定等待时间和智能重试机制，避免复杂的页面状态检测
        """
        try:
            if not self.is_initialized:
                if not await self.initialize():
                    return None

            # 初始化PDP页面加载器（如果还未初始化）
            if not self.pdp_loader:
                self.pdp_loader = PDPPageLoader(
                    driver=self.driver,
                    max_retries=3,
                    fixed_wait_time=3,
                    retry_interval=2
                )

            # 使用PDP页面加载器获取内容
            content = await self.pdp_loader.load_pdp_page(url)

            if content:
                logger.info(f"✅ [Selenium] PDP页面内容获取成功，长度: {len(content)}")

                # 检查是否需要处理验证码
                if self._detect_captcha_in_content(content):
                    logger.warning(f"🚫 [Selenium] 检测到验证码，进入验证码处理流程")
                    # 使用现有的验证码处理逻辑
                    captcha_result = self.detect_captcha()
                    if captcha_result.has_captcha:
                        logger.info(f"🔄 [Selenium] 检测到验证码类型: {captcha_result.captcha_type}")
                        logger.info(f"🔄 [Selenium] 等待用户完成验证码...")
                        # 等待用户处理验证码
                        captcha_completed = await self.wait_for_captcha_completion(max_wait_time=60)
                        if captcha_completed:
                            try:
                                content = self.driver.page_source
                                logger.info(f"✅ [Selenium] 验证码处理后重新获取内容，长度: {len(content)}")
                            except Exception as e:
                                logger.error(f"❌ [Selenium] 验证码处理后获取内容失败: {str(e)}")
                        else:
                            logger.warning(f"⏰ [Selenium] 验证码处理超时，返回当前内容")
                    else:
                        logger.debug(f"🔍 [Selenium] 内容检测到验证码但元素检测未发现，继续处理")

                return content
            else:
                logger.error(f"❌ [Selenium] PDP页面内容获取失败")
                return None

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ [Selenium] PDP页面简化加载失败: {error_msg}")

            # 检查是否是会话失效错误
            if self._is_session_invalid_error(error_msg):
                logger.warning("⚠️ [Selenium] 检测到会话失效错误，建议重新创建爬虫实例")

            return None

    def _is_session_invalid_error(self, error_msg: str) -> bool:
        """检查是否是会话失效错误"""
        session_error_keywords = [
            'invalid session id',
            'session not created',
            'chrome not reachable',
            'session deleted because of page crash',
            'no such session',
            'session timed out',
            'disconnected: not connected to devtools',
            'not connected to devtools',
            'chrome has crashed',
            'session deleted',
            'target window already closed'
        ]

        error_lower = error_msg.lower()
        for keyword in session_error_keywords:
            if keyword in error_lower:
                return True
        return False

    def _detect_captcha_in_content(self, content: str) -> bool:
        """检测页面内容中是否包含验证码"""
        try:
            captcha_indicators = [
                'captcha-config',
                'captcha',
                'challenge'
            ]

            content_lower = content.lower()
            for indicator in captcha_indicators:
                if indicator in content_lower:
                    return True

            return False

        except Exception as e:
            logger.debug(f"验证码检测异常: {str(e)}")
            return False

    def _detect_page_state(self, page_content: str) -> str:
        """检测页面状态基于页面内容"""
        try:
            # 检测正常页面：包含 __MODERN_ROUTER_DATA__ 元素
            if '<script type="application/json" id="__MODERN_ROUTER_DATA__">' in page_content:
                return "normal"

            # 检测验证码页面：包含 captcha-config 元素
            if '<script type="application/json" id="captcha-config">' in page_content:
                return "captcha"

            # 未知页面：两个关键元素都不存在
            return "unknown"

        except Exception as e:
            logger.debug(f"页面状态检测异常: {str(e)}")
            return "unknown"

    def _detect_page_type_for_load_more(self, url: str, page_content: str) -> str:
        """检测页面类型以决定是否需要执行加载更多功能"""
        try:
            # 1. 通过URL路径判断页面类型
            url_lower = url.lower()

            # 店铺页面：包含 /store/ 路径
            if '/store/' in url_lower:
                logger.debug("🏪 [Selenium] 通过URL识别为店铺页面")
                return "shop"

            # 产品详情页面：包含 /product/ 或 /pdp/ 路径
            if '/product/' in url_lower or '/pdp/' in url_lower:
                logger.debug("📦 [Selenium] 通过URL识别为产品详情页面")
                return "product_detail"

            # 2. 通过页面内容判断页面类型
            page_content_lower = page_content.lower()

            # 检测店铺页面特征
            shop_indicators = [
                'product-list',
                'product-grid',
                'shop-products',
                'store-products',
                'product-container',
                'products-wrapper',
                'item-list',
                'goods-list'
            ]

            shop_score = 0
            for indicator in shop_indicators:
                if indicator in page_content_lower:
                    shop_score += 1
                    logger.debug(f"🔍 [Selenium] 发现店铺页面指示器: {indicator}")

            # 检测产品详情页面特征
            product_detail_indicators = [
                'product-detail',
                'product-info',
                'product-description',
                'add-to-cart',
                'buy-now',
                'product-price',
                'product-title',
                'product-images'
            ]

            product_detail_score = 0
            for indicator in product_detail_indicators:
                if indicator in page_content_lower:
                    product_detail_score += 1
                    logger.debug(f"🔍 [Selenium] 发现产品详情页面指示器: {indicator}")

            # 3. 根据评分判断页面类型
            if shop_score >= 2:
                logger.debug(f"🏪 [Selenium] 通过内容识别为店铺页面 (评分: {shop_score})")
                return "shop"
            elif product_detail_score >= 3:
                logger.debug(f"📦 [Selenium] 通过内容识别为产品详情页面 (评分: {product_detail_score})")
                return "product_detail"

            # 4. 默认情况：如果无法明确判断，检查是否有"加载更多"相关元素
            load_more_keywords = [
                'view more',
                'load more',
                'show more',
                '查看更多',
                '加载更多',
                'load-more',
                'view-more'
            ]

            has_load_more_content = any(keyword in page_content_lower for keyword in load_more_keywords)

            if has_load_more_content:
                logger.debug("🔍 [Selenium] 发现加载更多相关内容，推测为店铺页面")
                return "shop"
            else:
                logger.debug("❓ [Selenium] 无法明确判断页面类型，默认为产品详情页面")
                return "product_detail"

        except Exception as e:
            logger.debug(f"页面类型检测异常: {str(e)}")
            # 异常情况下默认为产品详情页面，避免不必要的加载更多操作
            return "product_detail"

    def _find_load_more_button(self):
        """查找"加载更多"按钮"""
        try:
            # 可能的按钮选择器和文本
            button_selectors = [
                # 通过文本内容查找按钮
                "//button[contains(text(), 'View more')]",
                "//button[contains(text(), 'Load more')]",
                "//button[contains(text(), '查看更多')]",
                "//button[contains(text(), '加载更多')]",
                "//a[contains(text(), 'View more')]",
                "//a[contains(text(), 'Load more')]",
                # 通过类名查找
                "button[class*='load-more']",
                "button[class*='view-more']",
                "button[class*='show-more']",
                ".load-more-btn",
                ".view-more-btn",
                ".show-more-btn",
                # 通过data属性查找
                "button[data-testid*='load-more']",
                "button[data-testid*='view-more']",
                "[data-action='load-more']",
                "[data-action='view-more']"
            ]

            for selector in button_selectors:
                try:
                    if selector.startswith("//"):
                        # XPath选择器
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        # CSS选择器
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    # 查找可见且可点击的按钮
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            logger.debug(f"🎯 [Selenium] 找到加载更多按钮: {selector}")
                            return element

                except Exception as e:
                    logger.debug(f"查找按钮选择器失败 {selector}: {str(e)}")
                    continue

            logger.debug("🔍 [Selenium] 未找到加载更多按钮")
            return None

        except Exception as e:
            logger.debug(f"查找加载更多按钮异常: {str(e)}")
            return None

    async def _click_load_more_button(self, button) -> bool:
        """点击加载更多按钮"""
        try:
            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
            await asyncio.sleep(0.5)  # 等待滚动完成

            # 检查按钮是否仍然可见和可点击
            if not (button.is_displayed() and button.is_enabled()):
                logger.debug("⚠️ [Selenium] 按钮不可点击")
                return False

            # 尝试点击按钮
            try:
                button.click()
                logger.info("✅ [Selenium] 成功点击加载更多按钮")
                return True
            except Exception as e:
                # 如果普通点击失败，尝试JavaScript点击
                logger.debug(f"普通点击失败，尝试JavaScript点击: {str(e)}")
                self.driver.execute_script("arguments[0].click();", button)
                logger.info("✅ [Selenium] 通过JavaScript成功点击加载更多按钮")
                return True

        except Exception as e:
            logger.debug(f"点击加载更多按钮失败: {str(e)}")
            return False

    async def _wait_for_content_increment_with_sales_detection(self, baseline_content: str, filter_conditions: Dict[str, Any] = None) -> Dict:
        """
        等待内容增量并检测销量状态（优化版）

        Args:
            baseline_content: 基准页面内容

        Returns:
            Dict: 包含加载结果、销量检测结果等信息
        """
        try:
            baseline_length = len(baseline_content)
            start_time = time.time()

            logger.info(f"⏳ [加载更多] 开始等待内容增量，基准长度: {baseline_length}字符")

            # 步骤1: 初始等待
            await asyncio.sleep(self.load_more_config.initial_wait_time)

            # 步骤2: 检测内容增量，最多重试指定次数
            for attempt in range(self.load_more_config.max_retry_attempts):
                try:
                    current_content = self.driver.page_source
                    current_length = len(current_content)

                    # 使用内容增量检测器
                    has_increase, increment_info = self.content_detector.detect_content_increase(
                        baseline_length, current_length
                    )

                    if has_increase:
                        elapsed = time.time() - start_time
                        logger.info(f"✅ [加载更多] 检测到内容增量: +{increment_info['increment']}字符 "
                                  f"({increment_info['increment_ratio']*100:.1f}%)，耗时: {elapsed:.2f}秒")

                        # 步骤3: 检测销量状态（如果启用）
                        sales_result = {'has_low_sales': False, 'should_stop': False}

                        if self.load_more_config.enable_sales_detection:
                            # 获取销量阈值
                            sales_min_threshold = 0
                            if filter_conditions:
                                sales_min_threshold = filter_conditions.get('sales_min', 0)

                            logger.info(f"🔍 [加载更多] 开始检测新增内容中的销量状态，阈值: {sales_min_threshold}...")
                            try:
                                # 使用增强的销量分析，支持阈值检测
                                sales_analysis = self._analyze_content_increment_with_threshold(
                                    baseline_content, current_content, sales_min_threshold
                                )
                            except Exception as e:
                                logger.error(f"❌ [加载更多] 销量检测过程中出现异常: {str(e)}")
                                sales_analysis = {
                                    'has_low_sales': False,
                                    'content_increment': len(current_content) - len(baseline_content),
                                    'error': str(e)
                                }

                            sales_result = {
                                'has_low_sales': sales_analysis.get('has_low_sales', False),
                                'should_stop': (
                                    sales_analysis.get('has_low_sales', False) and
                                    self.load_more_config.stop_on_zero_sales
                                ),
                                'analysis': sales_analysis,
                                'threshold': sales_min_threshold
                            }

                            # 详细的销量检测结果日志
                            logger.info(f"📊 [加载更多] 销量检测结果:")
                            logger.info(f"   - 内容增量: {sales_analysis.get('content_increment', 0)}字符")
                            logger.info(f"   - 销量阈值: {sales_min_threshold}")
                            logger.info(f"   - 低销量检测: {'发现低销量商品' if sales_analysis.get('has_low_sales', False) else '未发现低销量商品'}")
                            logger.info(f"   - 检测到的商品数: {sales_analysis.get('low_sales_count', 0)}")

                            if sales_result['has_low_sales']:
                                logger.warning(f"⚠️ [加载更多] 检测到销量低于{sales_min_threshold}的商品！")
                                # 显示检测到的具体信息
                                if sales_analysis.get('low_sales_products'):
                                    for product in sales_analysis['low_sales_products'][:3]:  # 只显示前3个
                                        logger.info(f"   低销量商品: sold_count={product.get('sold_count', 0)}")

                                if sales_result['should_stop']:
                                    logger.info("🛑 [加载更多] 根据配置，遇到低销量商品时停止加载")
                            else:
                                logger.info(f"✅ [加载更多] 当前增量内容中未发现销量低于{sales_min_threshold}的商品，继续加载")
                        else:
                            logger.info("ℹ️ [加载更多] 销量检测功能未启用")

                        # 内容加载成功

                        return {
                            'success': True,
                            'content_loaded': True,
                            'new_content': current_content,
                            'increment_info': increment_info,
                            'sales_result': sales_result,
                            'elapsed_time': elapsed,
                            'attempts': attempt + 1
                        }

                    else:
                        # 没有检测到增量，等待后重试
                        if attempt < self.load_more_config.max_retry_attempts - 1:
                            logger.debug(f"🔄 [加载更多] 第{attempt + 1}次检测无增量，等待{self.load_more_config.additional_wait_time}秒后重试...")
                            await asyncio.sleep(self.load_more_config.additional_wait_time)
                        else:
                            logger.debug(f"❌ [加载更多] 第{attempt + 1}次检测仍无增量，判定为无更多内容")

                except Exception as e:
                    logger.debug(f"第{attempt + 1}次内容检测异常: {str(e)}")
                    if attempt < self.load_more_config.max_retry_attempts - 1:
                        await asyncio.sleep(self.load_more_config.additional_wait_time)

            # 所有重试都失败
            elapsed = time.time() - start_time
            logger.info(f"⏰ [加载更多] 内容增量检测超时，耗时: {elapsed:.2f}秒，尝试次数: {self.load_more_config.max_retry_attempts}")

            # 获取最终的页面内容
            final_content = self.driver.page_source

            return {
                'success': False,
                'content_loaded': False,
                'new_content': final_content,
                'increment_info': {'increment': 0, 'has_significant_increase': False},
                'sales_result': {'has_zero_sales': False, 'should_stop': False},
                'elapsed_time': elapsed,
                'attempts': self.load_more_config.max_retry_attempts,
                'reason': 'no_content_increment'
            }

        except Exception as e:
            logger.error(f"❌ [加载更多] 内容增量检测失败: {str(e)}")

            # 即使出现异常，也尝试获取当前页面内容
            try:
                current_content = self.driver.page_source
            except:
                current_content = ""  # 如果无法获取内容，使用空字符串

            return {
                'success': False,
                'content_loaded': False,
                'error': str(e),
                'reason': 'detection_error'
            }

    async def _load_more_data_with_smart_detection(self, filter_conditions: Dict[str, Any] = None) -> Dict:
        """
        智能加载更多数据（优化版）

        Returns:
            Dict: 包含加载结果、停止原因、最终内容等信息
        """
        try:
            max_clicks = self.load_more_config.max_load_clicks
            logger.info(f"🔄 [加载更多] 开始智能加载更多数据，最大点击次数: {max_clicks}")

            click_count = 0
            total_start_time = time.time()
            stop_reason = "max_clicks_reached"

            # 获取初始页面内容
            current_content = self.driver.page_source
            initial_length = len(current_content)

            # 保存内容快照（如果启用调试）
            if self.load_more_config.save_content_snapshots:
                self._save_content_snapshot(current_content, f"initial_{int(time.time())}")

            while click_count < max_clicks:
                # 步骤1: 查找加载更多按钮
                load_more_button = self._find_load_more_button()

                if not load_more_button:
                    stop_reason = "no_more_button"
                    logger.info(f"✅ [加载更多] 未找到更多加载按钮，数据加载完成，共点击{click_count}次")
                    break

                # 步骤2: 记录点击前的页面内容
                baseline_content = self.driver.page_source

                # 步骤3: 点击加载更多按钮
                click_success = await self._click_load_more_button(load_more_button)

                if not click_success:
                    stop_reason = "click_failed"
                    logger.warning(f"⚠️ [加载更多] 第{click_count + 1}次点击加载更多按钮失败")
                    break

                click_count += 1
                logger.info(f"📊 [加载更多] 第{click_count}次点击加载更多按钮成功")

                # 步骤4: 等待内容增量并检测销量状态
                load_result = await self._wait_for_content_increment_with_sales_detection(baseline_content, filter_conditions)

                if load_result['success'] and load_result['content_loaded']:
                    # 内容加载成功
                    current_content = load_result['new_content']

                    # 保存内容快照（如果启用调试）
                    if self.load_more_config.save_content_snapshots:
                        self._save_content_snapshot(current_content, f"click_{click_count}_{int(time.time())}")

                    logger.info(f"✅ [加载更多] 第{click_count}次内容加载成功，"
                              f"增量: +{load_result['increment_info']['increment']}字符")

                    # 步骤5: 检查是否因销量状态需要停止
                    if load_result['sales_result']['should_stop']:
                        stop_reason = "zero_sales_detected"
                        logger.info(f"🛑 [加载更多] 检测到零销量商品，智能停止加载，共点击{click_count}次")
                        break

                else:
                    # 内容加载失败或无增量
                    if load_result.get('reason') == 'no_content_increment':
                        stop_reason = "no_content_increment"
                        logger.info(f"⏰ [加载更多] 第{click_count}次无内容增量，可能已无更多数据")
                        # 继续尝试一次，以防网络延迟
                        if click_count < max_clicks:
                            continue
                        else:
                            break
                    else:
                        stop_reason = "detection_error"
                        logger.warning(f"⚠️ [加载更多] 第{click_count}次内容检测异常: {load_result.get('error', '未知错误')}")

                # 步骤6: 点击间隔等待
                if click_count < max_clicks:
                    await asyncio.sleep(self.load_more_config.click_interval)

            # 获取最终的页面内容和统计信息
            final_content = self.driver.page_source
            final_length = len(final_content)
            total_elapsed = time.time() - total_start_time
            total_increment = final_length - initial_length

            result = {
                'success': True,
                'final_content': final_content,
                'click_count': click_count,
                'stop_reason': stop_reason,
                'total_elapsed': total_elapsed,
                'initial_length': initial_length,
                'final_length': final_length,
                'total_increment': total_increment,
                'average_time_per_click': total_elapsed / click_count if click_count > 0 else 0
            }

            logger.info(f"🎉 [加载更多] 智能加载完成！")
            logger.info(f"   - 点击次数: {click_count}")
            logger.info(f"   - 停止原因: {stop_reason}")
            logger.info(f"   - 总耗时: {total_elapsed:.2f}秒")
            logger.info(f"   - 内容增量: +{total_increment}字符")
            logger.info(f"   - 最终长度: {final_length}字符")

            return result

        except Exception as e:
            logger.error(f"❌ [加载更多] 智能加载数据异常: {str(e)}")
            # 即使出现异常，也返回当前页面内容
            try:
                current_content = self.driver.page_source
                return {
                    'success': False,
                    'final_content': current_content,
                    'click_count': click_count,
                    'stop_reason': 'exception',
                    'error': str(e)
                }
            except:
                return {
                    'success': False,
                    'final_content': "",
                    'click_count': 0,
                    'stop_reason': 'exception',
                    'error': str(e)
                }



    def _check_initial_products_sales(self, page_content: str, filter_conditions: Dict[str, Any] = None) -> Dict:
        """
        检查初始页面中的商品销量是否都满足要求

        Args:
            page_content: 页面内容
            filter_conditions: 过滤条件

        Returns:
            Dict: 检查结果，包含是否所有商品都合格、不合格商品数等信息
        """
        try:
            # 获取销量阈值
            sales_threshold = 0
            if filter_conditions:
                sales_threshold = filter_conditions.get('sales_min', 0)

            logger.info(f"🔍 [初始销量检查] 开始检查初始商品销量，阈值: {sales_threshold}")

            # 解析页面中的商品数据
            products_data = self._extract_products_from_content(page_content)

            if not products_data:
                logger.warning("⚠️ [初始销量检查] 未能从页面中提取到商品数据")
                return {
                    'all_products_qualify': True,  # 无法检测时默认通过
                    'total_products': 0,
                    'unqualified_count': 0,
                    'sales_threshold': sales_threshold,
                    'error': '无法提取商品数据'
                }

            # 检查每个商品的销量
            unqualified_products = []
            for product in products_data:
                sold_count = product.get('sold_count', 0)
                if sold_count < sales_threshold:
                    unqualified_products.append({
                        'product_id': product.get('product_id', 'unknown'),
                        'sold_count': sold_count,
                        'title': product.get('title', 'unknown')[:50]  # 截取前50个字符
                    })

            all_qualify = len(unqualified_products) == 0

            result = {
                'all_products_qualify': all_qualify,
                'total_products': len(products_data),
                'unqualified_count': len(unqualified_products),
                'unqualified_products': unqualified_products[:5],  # 只保留前5个用于日志
                'sales_threshold': sales_threshold
            }

            if all_qualify:
                logger.info(f"✅ [初始销量检查] 所有 {len(products_data)} 个商品都满足销量要求 (>= {sales_threshold})")
            else:
                logger.warning(f"⚠️ [初始销量检查] 发现 {len(unqualified_products)} 个不满足销量要求的商品")
                for product in unqualified_products[:3]:  # 只显示前3个
                    logger.info(f"   不合格商品: {product['title']} (销量: {product['sold_count']})")

            return result

        except Exception as e:
            logger.error(f"❌ [初始销量检查] 检查过程中出现异常: {str(e)}")
            return {
                'all_products_qualify': True,  # 异常时默认通过，避免阻塞流程
                'total_products': 0,
                'unqualified_count': 0,
                'sales_threshold': sales_threshold if 'sales_threshold' in locals() else 0,
                'error': str(e)
            }

    def _extract_products_from_content(self, content: str) -> List[Dict]:
        """
        从页面内容中提取商品数据（HTML DOM解析）

        Args:
            content: 页面内容

        Returns:
            List[Dict]: 商品数据列表
        """
        try:
            products = []

            # 使用HTML DOM解析方式提取商品数据
            products = self._extract_products_from_html_dom(content)

            logger.info(f"📊 [商品提取] 从页面中提取到 {len(products)} 个商品")
            return products

        except Exception as e:
            logger.error(f"❌ [商品提取] 提取商品数据时出现异常: {str(e)}")
            return []

    def _extract_products_from_html_dom(self, content: str) -> List[Dict]:
        """
        从HTML DOM中提取商品数据

        Args:
            content: HTML页面内容

        Returns:
            List[Dict]: 商品数据列表
        """
        try:
            import re
            products = []

            logger.debug("🔍 [HTML解析] 开始从HTML DOM中提取商品数据")

            # 商品容器的开始标签模式
            container_start_pattern = r'<div class="w-full cursor-pointer">'

            # 商品容器的结束标签模式
            container_end_pattern = r'<span class="text-color-UIText1">\.\s*<!--\s*-->\s*\d+</span></span></div></div></div></div>'

            # 查找所有商品容器
            container_matches = []
            start_positions = []

            # 找到所有开始位置
            for match in re.finditer(container_start_pattern, content):
                start_positions.append(match.start())

            logger.debug(f"🔍 [HTML解析] 找到 {len(start_positions)} 个商品容器开始标签")

            # 使用更可靠的方法：基于下一个开始标签位置来确定当前容器的结束位置
            for i, start_pos in enumerate(start_positions):
                # 确定当前容器的结束位置
                if i < len(start_positions) - 1:
                    # 不是最后一个容器，使用下一个容器的开始位置作为结束位置
                    end_pos = start_positions[i + 1]
                else:
                    # 最后一个容器，搜索到内容末尾或找到合适的结束标签
                    remaining_content = content[start_pos:]
                    end_match = re.search(container_end_pattern, remaining_content)

                    if end_match:
                        end_pos = start_pos + end_match.end()
                    else:
                        # 如果找不到结束标签，使用一个合理的长度限制
                        max_container_length = 5000  # 假设单个商品容器不会超过5000字符
                        end_pos = min(start_pos + max_container_length, len(content))

                container_html = content[start_pos:end_pos]
                container_matches.append(container_html)
                logger.debug(f"✅ [HTML解析] 商品容器 {i+1}: 提取完成，长度={len(container_html)}字符")

            logger.info(f"📦 [HTML解析] 成功提取 {len(container_matches)} 个商品容器")

            # 从每个容器中提取商品数据
            for i, container_html in enumerate(container_matches):
                try:
                    product_data = self._extract_single_product_from_container(container_html, i)
                    if product_data:
                        products.append(product_data)
                        logger.debug(f"✅ [HTML解析] 成功解析第 {i+1} 个商品: {product_data.get('title', 'unknown')[:30]}")
                    else:
                        logger.debug(f"⚠️ [HTML解析] 第 {i+1} 个商品容器解析失败")
                except Exception as e:
                    logger.debug(f"❌ [HTML解析] 解析第 {i+1} 个商品容器时出现异常: {str(e)}")
                    continue

            return products

        except Exception as e:
            logger.error(f"❌ [HTML解析] HTML DOM解析失败: {str(e)}")
            return []

    def _extract_single_product_from_container(self, container_html: str, index: int) -> Optional[Dict]:
        """
        从单个商品容器HTML中提取商品数据

        Args:
            container_html: 商品容器的HTML内容
            index: 商品索引（用于调试）

        Returns:
            Optional[Dict]: 商品数据字典，如果解析失败则返回None
        """
        try:
            import re

            # 提取商品PDP链接
            pdp_link_pattern = r'<a href="(https://www\.tiktok\.com/shop/pdp/[^"]+)"[^>]*class="[^"]*group no-underline[^"]*"[^>]*>'
            pdp_match = re.search(pdp_link_pattern, container_html)

            if not pdp_match:
                logger.debug(f"⚠️ [商品解析] 第 {index+1} 个商品未找到PDP链接")
                return None

            pdp_url = pdp_match.group(1)

            # 从PDP URL中提取product_id（URL的最后一部分）
            product_id_match = re.search(r'/([^/]+)$', pdp_url)
            product_id = product_id_match.group(1) if product_id_match else f"unknown_{index}"

            # 提取商品标题（从PDP链接的文本内容中获取）
            # 查找链接标签内的文本内容
            link_content_pattern = r'<a href="' + re.escape(pdp_url) + r'"[^>]*>(.*?)</a>'
            link_content_match = re.search(link_content_pattern, container_html, re.DOTALL)

            title = "unknown"
            if link_content_match:
                link_inner_html = link_content_match.group(1)
                # 从链接内容中提取纯文本作为标题
                title_text_pattern = r'>([^<]+)<'
                title_matches = re.findall(title_text_pattern, link_inner_html)
                if title_matches:
                    # 取最长的文本作为标题
                    title = max(title_matches, key=len).strip()

            # 提取商品销量（支持多种格式）
            sold_count = 0

            # 销量模式1: 带HTML注释的格式 "173<!-- --> sold"
            sold_pattern1 = r'<span class="P3-Regular text-ellipsis whitespace-nowrap overflow-hidden">(\d+)<!--\s*-->\s*sold</span>'
            sold_match1 = re.search(sold_pattern1, container_html)

            # 销量模式2: 不带HTML注释的格式 "1 sold"
            sold_pattern2 = r'<span class="P3-Regular text-ellipsis whitespace-nowrap overflow-hidden">(\d+)\s+sold</span>'
            sold_match2 = re.search(sold_pattern2, container_html)

            if sold_match1:
                try:
                    sold_count = int(sold_match1.group(1))
                    logger.debug(f"📊 [销量解析] 使用模式1提取销量: {sold_count}")
                except ValueError:
                    sold_count = 0
            elif sold_match2:
                try:
                    sold_count = int(sold_match2.group(1))
                    logger.debug(f"📊 [销量解析] 使用模式2提取销量: {sold_count}")
                except ValueError:
                    sold_count = 0
            else:
                logger.debug(f"📊 [销量解析] 未找到销量信息，设置为0")

            # 构建商品数据
            product_data = {
                'product_id': product_id,
                'title': title,
                'sold_count': sold_count,
                'pdp_url': pdp_url,
                'price': 0  # 价格信息可能需要从其他地方提取
            }

            logger.debug(f"📦 [商品解析] 商品 {index+1}: ID={product_id}, 标题={title[:20]}..., 销量={sold_count}")

            return product_data

        except Exception as e:
            logger.debug(f"❌ [商品解析] 解析第 {index+1} 个商品容器失败: {str(e)}")
            return None

    def _extract_products_from_json(self, data, path="") -> List[Dict]:
        """
        从JSON数据中递归提取商品信息

        Args:
            data: JSON数据
            path: 当前搜索路径

        Returns:
            List[Dict]: 商品数据列表
        """
        products = []

        try:
            if isinstance(data, dict):
                # 检查是否是商品对象
                if self._is_product_object(data):
                    product_info = {
                        'product_id': data.get('product_id', data.get('id', 'unknown')),
                        'title': data.get('title', data.get('name', 'unknown')),
                        'sold_count': data.get('sold_count', data.get('sales_count', 0)),
                        'price': data.get('price', data.get('current_price', 0))
                    }
                    products.append(product_info)
                    logger.debug(f"🔍 [商品提取] 找到商品: {product_info['title'][:30]} (销量: {product_info['sold_count']})")

                # 递归搜索子对象
                for key, value in data.items():
                    if isinstance(value, (dict, list)):
                        sub_products = self._extract_products_from_json(value, f"{path}.{key}")
                        products.extend(sub_products)

            elif isinstance(data, list):
                # 递归搜索列表中的每个元素
                for i, item in enumerate(data):
                    if isinstance(item, (dict, list)):
                        sub_products = self._extract_products_from_json(item, f"{path}[{i}]")
                        products.extend(sub_products)

        except Exception as e:
            logger.debug(f"🔍 [商品提取] 递归搜索异常 at {path}: {str(e)}")

        return products

    def _is_product_object(self, obj: Dict) -> bool:
        """
        判断一个对象是否是商品对象

        Args:
            obj: 要检查的对象

        Returns:
            bool: 是否是商品对象
        """
        if not isinstance(obj, dict):
            return False

        # 检查是否包含商品的关键字段
        product_indicators = [
            'product_id', 'id', 'title', 'name', 'sold_count', 'sales_count'
        ]

        # 至少包含2个指示器才认为是商品对象
        indicator_count = sum(1 for indicator in product_indicators if indicator in obj)

        # 同时检查sold_count字段是否存在且为数字
        has_sales_data = (
            'sold_count' in obj and isinstance(obj['sold_count'], (int, float)) or
            'sales_count' in obj and isinstance(obj['sales_count'], (int, float))
        )

        return indicator_count >= 2 and has_sales_data

    def _analyze_content_increment_with_threshold(self, old_content: str, new_content: str, sales_threshold: int) -> Dict:
        """
        分析内容增量中的销量状态，支持自定义销量阈值

        Args:
            old_content: 旧的页面内容
            new_content: 新的页面内容
            sales_threshold: 销量阈值，低于此值的商品被认为是低销量

        Returns:
            Dict: 分析结果，包含是否有低销量商品、增量内容等信息
        """
        try:
            # 计算内容增量
            old_length = len(old_content) if old_content else 0
            new_length = len(new_content) if new_content else 0
            increment_length = new_length - old_length

            logger.info(f"🔍 [增量分析] 内容增量: {increment_length}字符")

            # 方法1: 比较新旧内容中的商品数据差异
            old_products = self._extract_products_from_content(old_content) if old_content else []
            new_products = self._extract_products_from_content(new_content) if new_content else []

            # 找出新增的商品（通过product_id比较）
            old_product_ids = {p.get('product_id') for p in old_products}
            increment_products = [p for p in new_products if p.get('product_id') not in old_product_ids]

            logger.info(f"📊 [增量分析] 旧商品数: {len(old_products)}, 新商品数: {len(new_products)}, 增量商品数: {len(increment_products)}")

            # 检测增量商品中的低销量商品
            low_sales_products = []
            for product in increment_products:
                sold_count = product.get('sold_count', 0)
                if sold_count < sales_threshold:
                    low_sales_products.append({
                        'product_id': product.get('product_id', 'unknown'),
                        'sold_count': sold_count,
                        'title': product.get('title', 'unknown')[:50]
                    })

            has_low_sales = len(low_sales_products) > 0

            # 记录检测结果
            if has_low_sales:
                logger.warning(f"⚠️ [增量分析] 检测到 {len(low_sales_products)} 个销量低于{sales_threshold}的新增商品")
                for product in low_sales_products[:3]:  # 只显示前3个
                    logger.info(f"   低销量商品: {product['title']} (销量: {product['sold_count']})")
            else:
                logger.info(f"✅ [增量分析] 新增的 {len(increment_products)} 个商品都满足销量要求 (>= {sales_threshold})")

            result = {
                'content_increment': increment_length,
                'has_content_increase': increment_length > 0,
                'has_low_sales': has_low_sales,
                'low_sales_count': len(low_sales_products),
                'low_sales_products': low_sales_products,
                'sales_threshold': sales_threshold,
                'increment_products_count': len(increment_products),
                'total_new_products': len(new_products),
                'total_old_products': len(old_products)
            }

            return result

        except Exception as e:
            logger.error(f"❌ [销量检测器] 内容增量分析失败: {str(e)}")
            return {
                'content_increment': 0,
                'has_content_increase': False,
                'has_low_sales': False,
                'low_sales_count': 0,
                'low_sales_products': [],
                'sales_threshold': sales_threshold,
                'error': str(e)
            }

    def _extract_products_from_json(self, data: Dict) -> List[Dict]:
        """从JSON数据中提取商品信息"""
        products = []

        def search_products(obj, path=""):
            if isinstance(obj, dict):
                # 检查是否是商品对象
                if 'product_id' in obj and 'sold_count' in obj:
                    products.append(obj)

                # 递归搜索
                for key, value in obj.items():
                    if key in ['products', 'items', 'data', 'list']:
                        search_products(value, f"{path}.{key}")
                    elif isinstance(value, (dict, list)):
                        search_products(value, f"{path}.{key}")

            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_products(item, f"{path}[{i}]")

        search_products(data)
        return products

    async def _handle_page_content_with_smart_detection(self, url: str, load_more: bool = True, filter_conditions: Dict[str, Any] = None) -> Optional[str]:
        """基于页面状态的智能内容处理"""
        max_wait_time = 300  # 最大等待时间300秒
        check_interval = 2   # 检测间隔2秒
        start_time = time.time()
        check_count = 0

        try:
            while time.time() - start_time < max_wait_time:
                check_count += 1

                # 获取当前页面内容（增加超时保护）
                try:
                    # 设置较短的超时时间来获取页面内容
                    page_content = await self._get_page_source_with_timeout(timeout=10)
                    if not page_content:
                        logger.warning(f"⚠️ [Selenium] 第{check_count}次获取页面内容失败，跳过本次检测")
                        continue

                    page_state = self._detect_page_state(page_content)
                    logger.debug(f"🔍 [Selenium] 第{check_count}次检测，页面状态: {page_state}")

                    if page_state == "normal":
                        # 正常页面：检测是否需要加载更多数据
                        elapsed = time.time() - start_time
                        logger.info(f"✅ [Selenium] 检测到正常页面，数据已就绪，耗时: {elapsed:.2f}秒，检测次数: {check_count}")

                        # 检查是否需要加载更多数据
                        if load_more:
                            # 首先判断页面类型
                            page_type = self._detect_page_type_for_load_more(url, page_content)
                            logger.info(f"🔍 [Selenium] 页面类型识别: {page_type}")

                            if page_type == "shop":
                                # 店铺页面：执行加载更多逻辑
                                logger.info("🏪 [Selenium] 店铺页面，开始检查初始商品销量...")

                                # 步骤1: 检查初始页面中的商品销量是否都满足要求
                                initial_sales_check = self._check_initial_products_sales(page_content, filter_conditions)

                                if not initial_sales_check['all_products_qualify']:
                                    logger.info(f"🛑 [Selenium] 初始页面中发现不满足销量要求的商品，无需加载更多")
                                    logger.info(f"   - 不合格商品数: {initial_sales_check['unqualified_count']}")
                                    logger.info(f"   - 销量阈值: {initial_sales_check['sales_threshold']}")
                                    return page_content

                                # 步骤2: 如果初始商品都满足要求，检查是否存在"加载更多"按钮
                                logger.info("✅ [Selenium] 初始商品都满足销量要求，检查是否存在加载更多按钮...")
                                load_more_button = self._find_load_more_button()

                                if load_more_button:
                                    logger.info("🔍 [Selenium] 发现加载更多按钮，开始智能加载更多数据...")

                                    try:
                                        # 执行智能加载更多数据，传递过滤条件
                                        load_result = await self._load_more_data_with_smart_detection(filter_conditions)

                                        if load_result['success']:
                                            logger.info(f"🎉 [Selenium] 智能加载更多数据完成")
                                            logger.info(f"   - 点击次数: {load_result['click_count']}")
                                            logger.info(f"   - 停止原因: {load_result['stop_reason']}")
                                            logger.info(f"   - 最终内容长度: {load_result['final_length']}")
                                            logger.info(f"   - 内容增量: +{load_result['total_increment']}字符")
                                            return load_result['final_content']
                                        else:
                                            logger.warning(f"⚠️ [Selenium] 智能加载更多数据失败: {load_result.get('error', '未知错误')}")
                                            return load_result.get('final_content', page_content)

                                    except Exception as load_more_error:
                                        logger.error(f"❌ [Selenium] 智能加载更多数据异常: {str(load_more_error)}")
                                        logger.info("🔄 [Selenium] 加载更多异常，返回当前页面内容")
                                        return page_content
                                else:
                                    logger.info("ℹ️ [Selenium] 店铺页面未发现加载更多按钮，返回当前页面内容")
                                    return page_content

                            elif page_type == "product_detail":
                                # 产品详情页面：直接返回页面内容，不执行加载更多
                                logger.info("📦 [Selenium] 产品详情页面，跳过加载更多功能，直接返回页面内容")
                                return page_content

                            else:
                                # 未知页面类型：为安全起见，不执行加载更多
                                logger.info("❓ [Selenium] 未知页面类型，跳过加载更多功能，返回当前页面内容")
                                return page_content
                        else:
                            logger.info("ℹ️ [Selenium] load_more参数为False，跳过加载更多数据，返回当前页面内容")
                            return page_content

                    elif page_state == "captcha":
                        # 验证码页面：记录并继续循环等待
                        if check_count == 1:
                            logger.warning("🚫 [Selenium] 检测到验证码页面，进入静默等待模式...")
                        elif check_count % 10 == 0:
                            elapsed = time.time() - start_time
                            logger.debug(f"🔄 [Selenium] 验证码页面等待中... 已检测{check_count}次，耗时{elapsed:.0f}秒")

                    elif page_state == "unknown":
                        # 未知页面：等待2秒后重新检测
                        if check_count == 1:
                            logger.debug("❓ [Selenium] 检测到未知页面状态，等待页面加载...")
                            await asyncio.sleep(2)
                            continue
                        elif check_count % 5 == 0:
                            elapsed = time.time() - start_time
                            logger.debug(f"❓ [Selenium] 未知页面状态持续，已检测{check_count}次，耗时{elapsed:.0f}秒")

                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"❌ [Selenium] 页面内容处理异常: {error_msg}")

                    # 检查是否是渲染器超时错误
                    if "timeout: Timed out receiving message from renderer" in error_msg:
                        logger.warning("⚠️ [Selenium] 检测到Chrome渲染器超时，尝试恢复...")
                        await self._recover_browser_state()

                        # 尝试获取当前页面内容
                        try:
                            current_content = await self._get_page_source_with_timeout(timeout=5)
                            if current_content and len(current_content) > 1000:
                                logger.info(f"✅ [Selenium] 渲染器超时后成功恢复，返回页面内容，长度: {len(current_content)}")
                                return current_content
                        except:
                            pass

                    # 其他异常的处理
                    elif "WebDriverException" in error_msg or "TimeoutException" in error_msg:
                        logger.warning("⚠️ [Selenium] 检测到WebDriver异常，尝试恢复...")
                        await self._recover_browser_state()

                    # 如果是在加载更多数据时出现异常，尝试返回当前页面内容
                    try:
                        current_content = await self._get_page_source_with_timeout(timeout=3)
                        if current_content and len(current_content) > 1000:  # 确保有有效内容
                            logger.warning(f"⚠️ [Selenium] 异常降级处理，返回当前页面内容，长度: {len(current_content)}")
                            return current_content
                    except:
                        pass

                    # 如果无法获取有效内容，继续循环等待
                    logger.debug(f"🔄 [Selenium] 继续等待页面恢复...")

                # 等待检测间隔
                await asyncio.sleep(check_interval)

            # 超时处理
            elapsed = time.time() - start_time
            logger.warning(f"⏰ [Selenium] 页面状态检测超时({elapsed:.0f}秒)，共检测{check_count}次，返回当前页面内容")

            # 超时后返回当前页面内容
            try:
                final_content = self.driver.page_source
                logger.info(f"⚠️ [Selenium] 超时降级处理，返回页面内容，长度: {len(final_content)}")
                return final_content
            except Exception as e:
                logger.error(f"❌ [Selenium] 超时后获取页面内容失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"❌ [Selenium] 智能页面处理异常: {str(e)}")
            return None

    async def _get_page_source_with_timeout(self, timeout: int = 10) -> Optional[str]:
        """
        带超时保护的页面内容获取方法

        Args:
            timeout: 超时时间（秒）

        Returns:
            页面内容字符串，失败时返回None
        """
        try:
            # 使用asyncio.wait_for来限制执行时间
            loop = asyncio.get_event_loop()

            def get_source():
                try:
                    # 首先检查浏览器是否还活着
                    self.driver.execute_script("return document.readyState")
                    # 获取页面内容
                    return self.driver.page_source
                except Exception as e:
                    logger.debug(f"获取页面内容时出现异常: {str(e)}")
                    return None

            # 在线程池中执行，避免阻塞事件循环
            page_content = await asyncio.wait_for(
                loop.run_in_executor(None, get_source),
                timeout=timeout
            )

            if page_content and len(page_content) > 1000:  # 确保获取到有效内容
                return page_content
            else:
                logger.warning(f"⚠️ [Selenium] 获取到的页面内容无效或过短: {len(page_content) if page_content else 0}字符")
                return None

        except asyncio.TimeoutError:
            logger.warning(f"⏰ [Selenium] 获取页面内容超时({timeout}秒)")
            # 尝试恢复浏览器状态
            await self._recover_browser_state()
            return None
        except Exception as e:
            logger.error(f"❌ [Selenium] 获取页面内容异常: {str(e)}")
            return None

    async def _recover_browser_state(self):
        """
        尝试恢复浏览器状态
        """
        try:
            logger.info("🔄 [Selenium] 尝试恢复浏览器状态...")

            # 尝试停止页面加载
            try:
                self.driver.execute_script("window.stop();")
                logger.debug("✅ [Selenium] 页面加载已停止")
            except:
                pass

            # 等待一小段时间让浏览器稳定
            await asyncio.sleep(1)

            # 检查浏览器是否还响应
            try:
                self.driver.execute_script("return document.readyState")
                logger.info("✅ [Selenium] 浏览器状态恢复正常")
            except Exception as e:
                logger.warning(f"⚠️ [Selenium] 浏览器状态检查失败: {str(e)}")
                # 如果浏览器无响应，可能需要重新初始化
                logger.info("🔄 [Selenium] 浏览器可能需要重新初始化")

        except Exception as e:
            logger.error(f"❌ [Selenium] 浏览器状态恢复失败: {str(e)}")

    def get_cookies(self) -> Dict[str, str]:
        """获取当前页面的Cookie"""
        try:
            if not self.driver:
                return {}
            
            cookies = {}
            for cookie in self.driver.get_cookies():
                cookies[cookie['name']] = cookie['value']
            
            logger.debug(f"🍪 [Selenium] 获取到 {len(cookies)} 个Cookie")
            return cookies
            
        except Exception as e:
            logger.error(f"❌ [Selenium] 获取Cookie失败: {str(e)}")
            return {}
    
    def set_cookies(self, cookies: Dict[str, str]):
        """设置Cookie"""
        try:
            if not self.driver:
                return
            
            for name, value in cookies.items():
                try:
                    self.driver.add_cookie({'name': name, 'value': value})
                except Exception as e:
                    logger.debug(f"⚠️ [Selenium] 设置Cookie失败 {name}: {str(e)}")
            
            logger.debug(f"🍪 [Selenium] 设置了 {len(cookies)} 个Cookie")
            
        except Exception as e:
            logger.error(f"❌ [Selenium] 设置Cookie失败: {str(e)}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, _exc_type, _exc_val, _exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        return False  # 不抑制异常
