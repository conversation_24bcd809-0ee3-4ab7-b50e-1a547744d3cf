"""
浏览器预加载器 - 提前初始化浏览器实例以提高响应速度
"""

import asyncio
import time
from typing import Optional, Callable
from loguru import logger

try:
    from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QProgressBar, QPushButton
    from PyQt6.QtCore import QTimer, pyqtSignal, QThread, QObject
    from PyQt6.QtGui import QFont
    HAS_QT = True
except ImportError:
    try:
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QProgressBar, QPushButton
        from PyQt5.QtCore import QTimer, pyqtSignal, QThread, QObject
        from PyQt5.QtGui import QFont
        HAS_QT = True
    except ImportError:
        HAS_QT = False


class BrowserInitializationDialog(QDialog if HAS_QT else object):
    """浏览器初始化进度对话框"""
    
    def __init__(self, parent=None):
        if not HAS_QT:
            return
        
        super().__init__(parent)
        self.setWindowTitle("浏览器初始化")
        self.setModal(True)
        self.setFixedSize(400, 150)
        
        self.setup_ui()
        self.is_cancelled = False
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("🚀 正在初始化浏览器...")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 状态标签
        self.status_label = QLabel("准备开始...")
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_initialization)
        layout.addWidget(self.cancel_button)
        
        self.setLayout(layout)
    
    def update_progress(self, message: str, progress: int):
        """更新进度"""
        self.status_label.setText(message)
        self.progress_bar.setValue(progress)
        
        if progress >= 100:
            self.accept()  # 关闭对话框
    
    def cancel_initialization(self):
        """取消初始化"""
        self.is_cancelled = True
        self.reject()


class BrowserPreloader:
    """浏览器预加载器"""
    
    def __init__(self):
        self.preloaded_browsers = []
        self.is_preloading = False
        self.preload_count = 1  # 预加载数量
        
    async def preload_browsers(self, headless: bool = False, 
                             progress_callback: Optional[Callable] = None) -> bool:
        """预加载浏览器实例"""
        try:
            if self.is_preloading:
                logger.info("⚠️ [预加载] 浏览器预加载已在进行中")
                return True
            
            self.is_preloading = True
            logger.info(f"🚀 [预加载] 开始预加载 {self.preload_count} 个浏览器实例")
            
            from ..crawler.selenium_crawler import BrowserPool
            browser_pool = BrowserPool()
            
            for i in range(self.preload_count):
                if progress_callback:
                    await progress_callback(
                        f"正在预加载第 {i+1}/{self.preload_count} 个浏览器实例...",
                        int((i / self.preload_count) * 80)
                    )
                
                browser = await browser_pool.get_browser(headless)
                if browser:
                    self.preloaded_browsers.append(browser)
                    logger.info(f"✅ [预加载] 第 {i+1} 个浏览器实例预加载完成")
                else:
                    logger.error(f"❌ [预加载] 第 {i+1} 个浏览器实例预加载失败")
            
            if progress_callback:
                await progress_callback("预加载完成", 100)
            
            self.is_preloading = False
            logger.info(f"🎉 [预加载] 浏览器预加载完成，成功预加载 {len(self.preloaded_browsers)} 个实例")
            return len(self.preloaded_browsers) > 0
            
        except Exception as e:
            logger.error(f"❌ [预加载] 浏览器预加载失败: {str(e)}")
            self.is_preloading = False
            return False
    
    def get_preloaded_browser(self):
        """获取预加载的浏览器实例"""
        if self.preloaded_browsers:
            browser = self.preloaded_browsers.pop(0)
            logger.info("♻️ [预加载] 使用预加载的浏览器实例")
            return browser
        return None
    
    def cleanup(self):
        """清理预加载的浏览器实例"""
        try:
            for browser in self.preloaded_browsers:
                try:
                    browser.quit()
                except:
                    pass
            self.preloaded_browsers.clear()
            logger.info("🔒 [预加载] 预加载的浏览器实例已清理")
        except Exception as e:
            logger.error(f"❌ [预加载] 清理预加载浏览器失败: {str(e)}")


class BrowserInitializationManager:
    """浏览器初始化管理器"""
    
    def __init__(self, use_gui: bool = True):
        self.use_gui = use_gui and HAS_QT
        self.preloader = BrowserPreloader()
        self.dialog = None
    
    async def initialize_with_progress(self, headless: bool = False) -> bool:
        """带进度显示的浏览器初始化"""
        try:
            if self.use_gui:
                return await self._initialize_with_gui(headless)
            else:
                return await self._initialize_with_console(headless)
        except Exception as e:
            logger.error(f"❌ [初始化管理器] 浏览器初始化失败: {str(e)}")
            return False
    
    async def _initialize_with_gui(self, headless: bool = False) -> bool:
        """GUI模式初始化"""
        try:
            from ..crawler.selenium_crawler import SeleniumCrawler
            
            # 创建进度对话框
            self.dialog = BrowserInitializationDialog()
            
            # 创建爬虫实例
            crawler = SeleniumCrawler(headless=headless)
            
            # 设置进度回调
            async def progress_callback(data):
                if self.dialog and not self.dialog.is_cancelled:
                    message = data.get('message', '')
                    progress = data.get('progress', 0)
                    self.dialog.update_progress(message, progress)
            
            crawler.set_initialization_progress_callback(progress_callback)
            
            # 显示对话框并开始初始化
            self.dialog.show()
            
            # 在后台执行初始化
            success = await crawler.initialize()
            
            if self.dialog.is_cancelled:
                await crawler.close(force_quit=True)
                return False
            
            if success:
                logger.info("✅ [初始化管理器] GUI模式浏览器初始化成功")
            else:
                logger.error("❌ [初始化管理器] GUI模式浏览器初始化失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ [初始化管理器] GUI模式初始化失败: {str(e)}")
            return False
    
    async def _initialize_with_console(self, headless: bool = False) -> bool:
        """控制台模式初始化"""
        try:
            from ..crawler.selenium_crawler import SeleniumCrawler
            
            print("🚀 正在初始化浏览器...")
            
            crawler = SeleniumCrawler(headless=headless)
            
            # 设置进度回调
            async def progress_callback(data):
                message = data.get('message', '')
                progress = data.get('progress', 0)
                print(f"[{progress:3d}%] {message}")
            
            crawler.set_initialization_progress_callback(progress_callback)
            
            success = await crawler.initialize()
            
            if success:
                print("✅ 浏览器初始化成功")
            else:
                print("❌ 浏览器初始化失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ [初始化管理器] 控制台模式初始化失败: {str(e)}")
            return False
    
    async def preload_browsers_in_background(self, headless: bool = False):
        """在后台预加载浏览器"""
        try:
            logger.info("🔄 [初始化管理器] 开始后台预加载浏览器")
            
            async def background_progress(message, progress):
                logger.debug(f"[预加载 {progress}%] {message}")
            
            success = await self.preloader.preload_browsers(headless, background_progress)
            
            if success:
                logger.info("✅ [初始化管理器] 后台预加载完成")
            else:
                logger.warning("⚠️ [初始化管理器] 后台预加载失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ [初始化管理器] 后台预加载失败: {str(e)}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            self.preloader.cleanup()
            if self.dialog:
                self.dialog.close()
                self.dialog = None
        except Exception as e:
            logger.error(f"❌ [初始化管理器] 清理资源失败: {str(e)}")


# 全局初始化管理器实例
_initialization_manager = None

def get_initialization_manager(use_gui: bool = True) -> BrowserInitializationManager:
    """获取全局初始化管理器实例"""
    global _initialization_manager
    if _initialization_manager is None:
        _initialization_manager = BrowserInitializationManager(use_gui)
    return _initialization_manager
