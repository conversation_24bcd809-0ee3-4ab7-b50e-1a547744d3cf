"""
重构后的爬虫管理器 - 支持完全分离的DirectCrawler和SeleniumCrawler
"""

import asyncio
from typing import List, Optional, Dict, Any, Callable
from enum import Enum
from loguru import logger

from .crawler_interface import (
    ICrawler, CrawlerFactory, DirectCrawlerConfig, SeleniumCrawlerConfig,
    CrawlerResult, ErrorType
)
from .direct_crawler import DirectCrawler
from .selenium_crawler_new import SeleniumCrawler
from .url_utils import URLUtils, URLValidator
from ..models.product import Product
from ..models.shop import Shop
from ..models.scraping_task import ScrapingTask, TaskType, TaskStatus
from ..utils.memory_storage import MemoryStorage



class CrawlerMode(Enum):
    """爬虫模式枚举"""
    DIRECT = "direct"           # 直连模式
    SELENIUM = "selenium"       # Selenium浏览器模式
    AUTO = "auto"              # 自动选择模式


class CrawlerManagerNew:
    """重构后的爬虫管理器"""
    
    def __init__(self):
        """初始化爬虫管理器"""
        self.memory_storage = MemoryStorage()
        self.active_tasks: Dict[str, ScrapingTask] = {}
        self.crawlers: Dict[str, ICrawler] = {}
        
        # 爬虫实例缓存
        self.direct_crawler: Optional[DirectCrawler] = None
        self.selenium_crawler: Optional[SeleniumCrawler] = None
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'direct_requests': 0,
            'selenium_requests': 0,
            'auto_switches': 0
        }
        
        logger.info("🚀 [爬虫管理器] 重构版本初始化完成")
    
    async def start_task(self, task: ScrapingTask, crawler_mode: str = "direct", 
                        user_interaction_callback: Optional[Callable] = None) -> bool:
        """
        启动爬取任务
        
        Args:
            task: 爬取任务
            crawler_mode: 爬虫模式 ("direct", "selenium", "auto")
            user_interaction_callback: 用户交互回调函数
            
        Returns:
            bool: 任务启动是否成功
        """
        try:
            task_id = task.task_id
            
            if task_id in self.active_tasks:
                logger.warning(f"⚠️ [任务管理] 任务已存在: {task_id}")
                return False
            
            # 解析爬虫模式
            try:
                mode = CrawlerMode(crawler_mode.lower())
            except ValueError:
                logger.error(f"❌ [任务管理] 不支持的爬虫模式: {crawler_mode}")
                return False
            
            # 创建爬虫实例
            crawler = await self._create_crawler(mode, user_interaction_callback)
            if not crawler:
                logger.error(f"❌ [任务管理] 创建爬虫实例失败")
                return False
            
            # 注册任务和爬虫
            self.active_tasks[task_id] = task
            self.crawlers[task_id] = crawler
            
            logger.info(f"🔧 [任务管理] 爬虫模式: {mode.value}")
            
            # 启动任务
            task.start()
            self.stats['total_tasks'] += 1
            
            # 根据任务类型执行不同的处理逻辑
            if task.task_type == TaskType.SINGLE_PRODUCT:
                logger.info(f"🚀 [任务管理] 启动单商品爬取任务")
                asyncio.create_task(self._execute_single_product_task(task, crawler, mode))
            else:
                logger.error(f"❌ [任务管理] 不支持的任务类型: {task.task_type}")
                return False
            
            logger.info(f"✅ [任务管理] 任务启动成功: {task.name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [任务管理] 启动任务失败: {str(e)}")
            return False
    
    async def _create_crawler(self, mode: CrawlerMode, 
                            user_interaction_callback: Optional[Callable] = None) -> Optional[ICrawler]:
        """创建爬虫实例"""
        try:
            if mode == CrawlerMode.DIRECT:
                if not self.direct_crawler:
                    config = DirectCrawlerConfig()
                    self.direct_crawler = DirectCrawler(config)
                
                crawler = self.direct_crawler
                
            elif mode == CrawlerMode.SELENIUM:
                if not self.selenium_crawler:
                    config = SeleniumCrawlerConfig()
                    config.headless = False  # 默认显示浏览器窗口
                    self.selenium_crawler = SeleniumCrawler(config)
                
                crawler = self.selenium_crawler
                
            elif mode == CrawlerMode.AUTO:
                # 自动模式优先使用直连爬虫
                if not self.direct_crawler:
                    config = DirectCrawlerConfig()
                    self.direct_crawler = DirectCrawler(config)
                
                crawler = self.direct_crawler
                
            else:
                logger.error(f"❌ [爬虫创建] 不支持的爬虫模式: {mode}")
                return None
            
            # 设置用户交互回调
            if user_interaction_callback:
                crawler.set_user_interaction_callback(user_interaction_callback)
            
            # 初始化爬虫
            if not await crawler.initialize():
                logger.error(f"❌ [爬虫创建] 爬虫初始化失败")
                return None
            
            return crawler
            
        except Exception as e:
            logger.error(f"❌ [爬虫创建] 创建爬虫实例失败: {str(e)}")
            return None
    
    async def _execute_single_product_task(self, task: ScrapingTask, crawler: ICrawler, mode: CrawlerMode):
        """执行单商品爬取任务"""
        try:
            logger.info(f"📦 [任务执行] 开始执行单商品任务: {task.name}")
            
            # 验证URL
            url = task.urls[0] if task.urls else None
            if not url:
                await self._handle_task_error(task, "任务URL为空")
                return
            
            if not URLValidator.is_valid_tiktok_url(url):
                await self._handle_task_error(task, f"无效的TikTok URL: {url}")
                return
            
            # 根据URL类型选择处理方法
            if URLUtils.is_pdp_url(url):
                result = await self._handle_product_url(task, crawler, url, mode)
            elif URLUtils.is_shop_url(url):
                result = await self._handle_shop_url(task, crawler, url, mode)
            else:
                await self._handle_task_error(task, f"不支持的URL类型: {url}")
                return
            
            # 处理结果
            if result and result.success:
                await self._handle_task_success(task, result)
            else:
                error_msg = result.error.message if result and result.error else "未知错误"
                await self._handle_task_error(task, error_msg)
                
        except Exception as e:
            logger.error(f"❌ [任务执行] 单商品任务执行失败: {str(e)}")
            await self._handle_task_error(task, f"任务执行异常: {str(e)}")
    
    async def _handle_product_url(self, task: ScrapingTask, crawler: ICrawler, 
                                url: str, mode: CrawlerMode) -> Optional[CrawlerResult]:
        """处理产品URL"""
        try:
            logger.info(f"📦 [URL处理] 处理产品URL: {url}")
            
            # 更新统计
            if mode == CrawlerMode.DIRECT:
                self.stats['direct_requests'] += 1
            elif mode == CrawlerMode.SELENIUM:
                self.stats['selenium_requests'] += 1
            
            # 获取产品数据
            result = await crawler.get_product_data(url)
            
            # 如果是自动模式且直连失败，尝试切换到Selenium
            if (mode == CrawlerMode.AUTO and not result.success and 
                result.error and result.error.error_type == ErrorType.CAPTCHA_DETECTED):
                
                logger.info("🔄 [自动模式] 检测到验证码，切换到Selenium模式")
                selenium_result = await self._retry_with_selenium(url, 'product')
                if selenium_result:
                    self.stats['auto_switches'] += 1
                    return selenium_result
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [URL处理] 处理产品URL失败: {str(e)}")
            return None
    
    async def _handle_shop_url(self, task: ScrapingTask, crawler: ICrawler, 
                             url: str, mode: CrawlerMode) -> Optional[CrawlerResult]:
        """处理店铺URL"""
        try:
            logger.info(f"🏪 [URL处理] 处理店铺URL: {url}")
            
            # 更新统计
            if mode == CrawlerMode.DIRECT:
                self.stats['direct_requests'] += 1
            elif mode == CrawlerMode.SELENIUM:
                self.stats['selenium_requests'] += 1
            
            # 获取店铺数据（包含商品列表）
            result = await crawler.get_shop_data(url, load_products=True)
            
            # 如果是自动模式且直连失败，尝试切换到Selenium
            if (mode == CrawlerMode.AUTO and not result.success and 
                result.error and result.error.error_type == ErrorType.CAPTCHA_DETECTED):
                
                logger.info("🔄 [自动模式] 检测到验证码，切换到Selenium模式")
                selenium_result = await self._retry_with_selenium(url, 'shop')
                if selenium_result:
                    self.stats['auto_switches'] += 1
                    return selenium_result
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [URL处理] 处理店铺URL失败: {str(e)}")
            return None
    
    async def _retry_with_selenium(self, url: str, url_type: str) -> Optional[CrawlerResult]:
        """使用Selenium重试请求"""
        try:
            # 创建Selenium爬虫实例
            if not self.selenium_crawler:
                config = SeleniumCrawlerConfig()
                config.headless = False
                self.selenium_crawler = SeleniumCrawler(config)
                
                if not await self.selenium_crawler.initialize():
                    logger.error("❌ [自动切换] Selenium爬虫初始化失败")
                    return None
            
            # 根据URL类型调用相应方法
            if url_type == 'product':
                return await self.selenium_crawler.get_product_data(url)
            elif url_type == 'shop':
                return await self.selenium_crawler.get_shop_data(url, load_products=True)
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ [自动切换] Selenium重试失败: {str(e)}")
            return None

    async def _handle_task_success(self, task: ScrapingTask, result: CrawlerResult):
        """处理任务成功"""
        try:
            logger.info(f"✅ [任务完成] 任务执行成功: {task.name}")

            # 保存数据到存储
            if result.data:
                await self._save_result_data(task, result.data)

            # 更新任务状态
            task.complete()
            self.stats['successful_tasks'] += 1

            # 通知任务完成
            if hasattr(task, 'progress_callback') and task.progress_callback:
                await task.progress_callback({
                    'type': 'task_completed',
                    'task_id': task.task_id,
                    'message': '任务执行成功',
                    'data': result.data
                })

        except Exception as e:
            logger.error(f"❌ [任务完成] 处理任务成功失败: {str(e)}")

    async def _handle_task_error(self, task: ScrapingTask, error_message: str):
        """处理任务错误"""
        try:
            logger.error(f"❌ [任务失败] 任务执行失败: {task.name} - {error_message}")

            # 更新任务状态
            task.fail(error_message)
            self.stats['failed_tasks'] += 1

            # 通知任务失败
            if hasattr(task, 'progress_callback') and task.progress_callback:
                await task.progress_callback({
                    'type': 'task_failed',
                    'task_id': task.task_id,
                    'message': error_message,
                    'error': error_message
                })

        except Exception as e:
            logger.error(f"❌ [任务失败] 处理任务错误失败: {str(e)}")

    async def _save_result_data(self, task: ScrapingTask, data: Any):
        """保存结果数据"""
        try:
            from ..crawler.crawler_interface import ProductData, ShopData

            if isinstance(data, ProductData):
                # 保存产品数据
                product = Product(
                    product_id=data.product_id,
                    title=data.title,
                    price=data.price,
                    currency=data.currency,
                    sold_count=data.sold_count,
                    rating=data.rating,
                    review_count=data.review_count,
                    images=data.images,
                    description=data.description,
                    seller_id=data.seller_id,
                    seller_name=data.seller_name,
                    shop_id=data.shop_id,
                    shop_name=data.shop_name,
                    category=data.category,
                    shipping_fee=data.shipping_fee,
                    availability=data.availability,
                    url=data.url
                )

                await self.memory_storage.save_product_async(product)
                logger.info(f"💾 [数据保存] 产品数据已保存: {data.product_id}")

            elif isinstance(data, ShopData):
                # 保存店铺数据
                shop = Shop(
                    shop_id=data.shop_id,
                    shop_name=data.shop_name,
                    seller_id=data.seller_id,
                    seller_name=data.seller_name,
                    product_count=data.product_count,
                    rating=data.rating,
                    follower_count=data.follower_count,
                    description=data.description,
                    avatar_url=data.avatar_url,
                    cover_url=data.cover_url,
                    url=data.url
                )

                await self.memory_storage.save_shop_async(shop)
                logger.info(f"💾 [数据保存] 店铺数据已保存: {data.shop_id}")

                # 保存店铺中的商品数据
                if data.products:
                    for product_data in data.products:
                        product = Product(
                            product_id=product_data.product_id,
                            title=product_data.title,
                            price=product_data.price,
                            currency=product_data.currency,
                            sold_count=product_data.sold_count,
                            rating=product_data.rating,
                            review_count=product_data.review_count,
                            images=product_data.images,
                            description=product_data.description,
                            seller_id=product_data.seller_id,
                            seller_name=product_data.seller_name,
                            shop_id=product_data.shop_id,
                            shop_name=product_data.shop_name,
                            category=product_data.category,
                            shipping_fee=product_data.shipping_fee,
                            availability=product_data.availability,
                            url=product_data.url
                        )
                        await self.memory_storage.save_product_async(product)

                    logger.info(f"💾 [数据保存] 店铺商品数据已保存: {len(data.products)} 个商品")

        except Exception as e:
            logger.error(f"❌ [数据保存] 保存结果数据失败: {str(e)}")

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            if task_id not in self.active_tasks:
                logger.warning(f"⚠️ [任务管理] 任务不存在: {task_id}")
                return False

            task = self.active_tasks[task_id]
            task.cancel()

            # 清理爬虫资源
            if task_id in self.crawlers:
                crawler = self.crawlers[task_id]
                await crawler.close()
                del self.crawlers[task_id]

            del self.active_tasks[task_id]

            logger.info(f"🚫 [任务管理] 任务已取消: {task_id}")
            return True

        except Exception as e:
            logger.error(f"❌ [任务管理] 取消任务失败: {str(e)}")
            return False

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            if task_id not in self.active_tasks:
                return None

            task = self.active_tasks[task_id]
            crawler = self.crawlers.get(task_id)

            status_info = {
                'task_id': task_id,
                'name': task.name,
                'status': task.status.value,
                'progress': task.progress,
                'start_time': task.start_time,
                'end_time': task.end_time,
                'error_message': task.error_message
            }

            if crawler:
                status_info['crawler_status'] = crawler.get_status().value
                status_info['crawler_stats'] = crawler.get_stats()

            return status_info

        except Exception as e:
            logger.error(f"❌ [任务管理] 获取任务状态失败: {str(e)}")
            return None

    def get_all_tasks_status(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        try:
            return [
                self.get_task_status(task_id)
                for task_id in self.active_tasks.keys()
            ]
        except Exception as e:
            logger.error(f"❌ [任务管理] 获取所有任务状态失败: {str(e)}")
            return []

    def get_manager_stats(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        try:
            stats = self.stats.copy()
            stats['active_tasks_count'] = len(self.active_tasks)
            stats['active_crawlers_count'] = len(self.crawlers)

            # 添加爬虫状态信息
            if self.direct_crawler:
                stats['direct_crawler_stats'] = self.direct_crawler.get_stats()

            if self.selenium_crawler:
                stats['selenium_crawler_stats'] = self.selenium_crawler.get_stats()

            return stats

        except Exception as e:
            logger.error(f"❌ [统计信息] 获取管理器统计失败: {str(e)}")
            return {}

    async def cleanup_completed_tasks(self):
        """清理已完成的任务"""
        try:
            completed_task_ids = []
            for task_id, task in self.active_tasks.items():
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed_task_ids.append(task_id)

            for task_id in completed_task_ids:
                # 清理爬虫资源
                if task_id in self.crawlers:
                    crawler = self.crawlers[task_id]
                    await crawler.close()
                    del self.crawlers[task_id]

                del self.active_tasks[task_id]

            logger.info(f"🧹 [任务清理] 清理了 {len(completed_task_ids)} 个已完成的任务")

        except Exception as e:
            logger.error(f"❌ [任务清理] 清理任务失败: {str(e)}")

    async def shutdown(self):
        """关闭管理器"""
        try:
            logger.info("🔒 [管理器关闭] 开始关闭爬虫管理器...")

            # 取消所有活跃任务
            for task_id in list(self.active_tasks.keys()):
                await self.cancel_task(task_id)

            # 关闭爬虫实例
            if self.direct_crawler:
                await self.direct_crawler.close()
                self.direct_crawler = None

            if self.selenium_crawler:
                await self.selenium_crawler.close()
                self.selenium_crawler = None

            # 关闭数据存储
            await self.memory_storage.close()

            logger.info("✅ [管理器关闭] 爬虫管理器已关闭")

        except Exception as e:
            logger.error(f"❌ [管理器关闭] 关闭管理器失败: {str(e)}")
