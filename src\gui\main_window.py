"""
主窗口界面
"""

import sys
import asyncio
from typing import Optional, List, Dict, Any
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
    QProgressBar, QTabWidget, QSplitter, QGroupBox, QComboBox, QSpinBox,
    QDoubleSpinBox, QCheckBox, QMessageBox, QFileDialog, QStatusBar, QMenuBar, QMenu,
    QHeaderView, QFrame
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QIcon, QFont, QPixmap
from PyQt5.QtWidgets import QAction
from loguru import logger

from ..config.settings import config
from ..crawler.crawler_manager import CrawlerManager
from ..crawler.url_parser import URLParser
from ..models.scraping_task import ScrapingTask, TaskStatus
from ..utils.memory_storage import MemoryStorage
from ..utils.data_exporter import DataExporter

from .task_widget import TaskWidget
from .product_table import ProductTable
from .async_worker import AsyncCrawlerWorker
from .settings_dialog import SettingsDialog
from .tiktok_data_viewer import TikTokDataViewer


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.memory_storage = MemoryStorage()
        self.data_exporter = DataExporter(self.memory_storage)
        self.crawler_manager = CrawlerManager()
        self.crawler_manager.memory_storage = self.memory_storage  # 共享内存存储
        self.url_parser = URLParser()
        self.current_task: Optional[ScrapingTask] = None

        # 初始化异步工作线程
        self.worker_thread = None

        # 初始化UI
        self.init_ui()
        self.init_menu()
        self.init_status_bar()

        # 启动定时器更新任务状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_task_status)
        self.update_timer.start(1000)  # 每秒更新一次

        logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(config.gui.window_title)
        self.setGeometry(100, 100, config.gui.window_width, config.gui.window_height)
        self.setMinimumSize(config.gui.min_width, config.gui.min_height)
        
        # 设置应用图标
        # self.setWindowIcon(QIcon("resources/icons/app_icon.png"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建输入区域
        input_group = self.create_input_group()
        main_layout.addWidget(input_group)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 任务管理选项卡
        self.task_tab = self.create_task_tab()
        self.tab_widget.addTab(self.task_tab, "任务管理")
        
        # 商品数据选项卡
        # self.product_tab = self.create_product_tab()
        # self.tab_widget.addTab(self.product_tab, "商品数据")
        
        # 店铺信息选项卡
        # self.shop_tab = self.create_shop_tab()
        # self.tab_widget.addTab(self.shop_tab, "店铺信息")

        # TikTok数据查看器选项卡
        self.tiktok_data_tab = self.create_tiktok_data_tab()
        self.tab_widget.addTab(self.tiktok_data_tab, "TikTok数据")

        main_layout.addWidget(self.tab_widget)
        
        # 应用样式
        self.apply_styles()
    
    def create_input_group(self) -> QGroupBox:
        """创建输入区域"""
        group = QGroupBox("TikTok Shop 链接输入")
        layout = QVBoxLayout(group)
        
        # URL输入行
        url_layout = QHBoxLayout()
        
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("请粘贴TikTok Shop商品或店铺链接...")
        self.url_input.returnPressed.connect(self.start_crawling)
        
        self.start_button = QPushButton("开始爬取")
        self.start_button.clicked.connect(self.start_crawling)
        self.start_button.setMinimumWidth(100)
        
        self.pause_button = QPushButton("暂停")
        self.pause_button.clicked.connect(self.pause_crawling)
        self.pause_button.setEnabled(False)
        self.pause_button.setMinimumWidth(80)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.stop_crawling)
        self.stop_button.setEnabled(False)
        self.stop_button.setMinimumWidth(80)
        
        url_layout.addWidget(QLabel("链接:"))
        url_layout.addWidget(self.url_input)
        url_layout.addWidget(self.start_button)
        url_layout.addWidget(self.pause_button)
        url_layout.addWidget(self.stop_button)
        
        layout.addLayout(url_layout)
        
        # 选项行
        options_layout = QHBoxLayout()

        # 爬虫模式选择
        options_layout.addWidget(QLabel("爬虫模式:"))
        self.crawler_mode_combo = QComboBox()
        self.crawler_mode_combo.addItems([
            "直连模式 (暂不使用)",
            "浏览器模式 (推荐)",
            "自动切换模式"
        ])
        self.crawler_mode_combo.setCurrentIndex(1)  # 默认浏览器模式
        self.crawler_mode_combo.setToolTip(
            "直连模式: 快速稳定，适合大部分情况\n"
            "浏览器模式: 显示真实浏览器，可手动处理验证码\n"
            "自动切换模式: 遇到验证码时自动切换到浏览器模式"
        )
        options_layout.addWidget(self.crawler_mode_combo)

        # 添加分隔符
        options_layout.addWidget(QLabel("|"))



        # 包含图片（保留此选项）
        self.include_images_check = QCheckBox("包含商品图片")
        self.include_images_check.setChecked(True)
        options_layout.addWidget(self.include_images_check)

        # 添加分隔符
        options_layout.addWidget(QLabel("|"))

        # 店铺商品数量区间
        options_layout.addWidget(QLabel("店铺商品数:"))
        self.shop_count_min_spin = QSpinBox()
        self.shop_count_min_spin.setRange(0, 999999)
        self.shop_count_min_spin.setValue(0)
        self.shop_count_min_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.shop_count_min_spin)

        options_layout.addWidget(QLabel("-"))

        self.shop_count_max_spin = QSpinBox()
        self.shop_count_max_spin.setRange(0, 999999)
        self.shop_count_max_spin.setValue(999999)
        self.shop_count_max_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.shop_count_max_spin)

        # 运费区间
        options_layout.addWidget(QLabel("运费范围:"))
        self.shipping_fee_min_spin = QDoubleSpinBox()
        self.shipping_fee_min_spin.setRange(0.0, 9999.99)
        self.shipping_fee_min_spin.setValue(0.0)
        self.shipping_fee_min_spin.setDecimals(2)
        self.shipping_fee_min_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.shipping_fee_min_spin)

        options_layout.addWidget(QLabel("-"))

        self.shipping_fee_max_spin = QDoubleSpinBox()
        self.shipping_fee_max_spin.setRange(0.0, 9999.99)
        self.shipping_fee_max_spin.setValue(9999.99)
        self.shipping_fee_max_spin.setDecimals(2)
        self.shipping_fee_max_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.shipping_fee_max_spin)

        # 价格区间
        options_layout.addWidget(QLabel("价格范围:"))
        self.price_min_spin = QDoubleSpinBox()
        self.price_min_spin.setRange(0.0, 99999.99)
        self.price_min_spin.setValue(0.0)
        self.price_min_spin.setDecimals(2)
        self.price_min_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.price_min_spin)

        options_layout.addWidget(QLabel("-"))

        self.price_max_spin = QDoubleSpinBox()
        self.price_max_spin.setRange(0.0, 99999.99)
        self.price_max_spin.setValue(99999.99)
        self.price_max_spin.setDecimals(2)
        self.price_max_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.price_max_spin)

        # 商品销量区间
        options_layout.addWidget(QLabel("销量范围:"))
        self.sales_min_spin = QSpinBox()
        self.sales_min_spin.setRange(0, 999999)
        self.sales_min_spin.setValue(1)
        self.sales_min_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.sales_min_spin)

        options_layout.addWidget(QLabel("-"))

        self.sales_max_spin = QSpinBox()
        self.sales_max_spin.setRange(0, 999999)
        self.sales_max_spin.setValue(999999)
        self.sales_max_spin.setEnabled(True)  # 初始启用，任务开始时禁用
        options_layout.addWidget(self.sales_max_spin)

        # 注意：过滤条件将在爬取开始前应用，不需要连接实时过滤信号
        # 过滤控件的值将作为任务配置的一部分传递给爬虫

        options_layout.addStretch()
        layout.addLayout(options_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态文本
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
        return group
    
    def create_task_tab(self) -> QWidget:
        """创建任务管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 任务列表
        self.task_widget = TaskWidget()
        layout.addWidget(self.task_widget)
        
        return widget
    
    def create_product_tab(self) -> QWidget:
        """创建商品数据选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 商品表格
        self.product_table = ProductTable(self.memory_storage)
        layout.addWidget(self.product_table)
        
        return widget
    
    def create_shop_tab(self) -> QWidget:
        """创建店铺信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 店铺信息显示区域
        self.shop_info_text = QTextEdit()
        self.shop_info_text.setReadOnly(True)
        layout.addWidget(self.shop_info_text)
        
        return widget

    def create_tiktok_data_tab(self) -> QWidget:
        """创建TikTok数据查看器选项卡"""
        self.tiktok_data_viewer = TikTokDataViewer()

        # 连接数据导出信号
        self.tiktok_data_viewer.data_exported.connect(self.on_data_exported)

        return self.tiktok_data_viewer

    def init_menu(self):
        """初始化菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        # 导入任务
        import_action = QAction("导入任务", self)
        import_action.triggered.connect(self.import_tasks)
        file_menu.addAction(import_action)
        
        # 导出数据
        export_menu = file_menu.addMenu("导出数据")

        export_json_action = QAction("导出为JSON", self)
        export_json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(export_json_action)

        export_csv_action = QAction("导出为CSV", self)
        export_csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(export_csv_action)

        export_excel_action = QAction("导出为Excel", self)
        export_excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(export_excel_action)

        # 导出任务
        export_tasks_action = QAction("导出任务", self)
        export_tasks_action.triggered.connect(self.export_tasks)
        file_menu.addAction(export_tasks_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具")
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 清理数据
        cleanup_action = QAction("清理数据", self)
        cleanup_action.triggered.connect(self.cleanup_data)
        tools_menu.addAction(cleanup_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def init_status_bar(self):
        """初始化状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 添加状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 添加任务计数标签
        self.task_count_label = QLabel("任务: 0")
        self.status_bar.addPermanentWidget(self.task_count_label)
        
        # 添加商品计数标签
        self.product_count_label = QLabel("商品: 0")
        self.status_bar.addPermanentWidget(self.product_count_label)
    
    def apply_styles(self):
        """应用样式"""
        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
            
            QLineEdit:focus {
                border-color: #4CAF50;
            }
            
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 5px;
                text-align: center;
            }
            
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
            
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            
            QTabBar::tab {
                background-color: #e1e1e1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #4CAF50;
            }
        """)
    
    def start_crawling(self):
        """开始爬取"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "警告", "请输入TikTok Shop链接")
            return
        
        # 验证URL
        validation_result = self.url_parser.validate_and_suggest(url)
        if not validation_result["is_valid"]:
            error_msg = validation_result.get("error", "无效的URL")
            suggestions = validation_result.get("suggestions", [])
            
            msg = f"URL验证失败: {error_msg}\n"
            if suggestions:
                msg += "\n建议:\n" + "\n".join(f"• {s}" for s in suggestions)
            
            QMessageBox.warning(self, "URL验证失败", msg)
            return
        
        # 获取过滤条件（在任务开始前）
        filter_conditions = self.get_filter_conditions()

        # 禁用数据过滤控件（任务进行中）
        self.disable_data_filters()

        # 创建任务配置（包含过滤条件和爬虫模式）
        task_config = {
            "include_images": self.include_images_check.isChecked(),
            "filter_conditions": filter_conditions,  # 添加过滤条件
            "crawler_mode": self.get_crawler_mode(),  # 添加爬虫模式
        }

        # 在后台线程中创建和启动任务
        self.start_crawling_async(url, task_config)
    
    def start_crawling_async(self, url: str, config: Dict[str, Any]):
        """使用工作线程开始爬取"""
        try:
            logger.info(f"🚀 [主窗口] 开始处理链接: {url}")

            # 检查是否已有任务在运行
            if self.worker_thread and self.worker_thread.isRunning():
                logger.warning(f"⚠️ [主窗口] 已有任务在运行，请先停止当前任务")
                QMessageBox.warning(self, "警告", "已有任务在运行，请先停止当前任务")
                return

            # 更新UI状态
            self.status_label.setText("正在创建任务...")
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            self.progress_bar.setVisible(True)

            # 添加状态消息
            self.add_status_message(f"开始处理链接: {url}")

            # 创建并配置工作线程
            self.worker_thread = AsyncCrawlerWorker()
            self.worker_thread.setup_task(url, config)

            # 连接信号
            self.connect_worker_signals()

            # 启动工作线程
            logger.info(f"🚀 [主窗口] 启动工作线程")
            self.worker_thread.start()

        except Exception as e:
            logger.error(f"💥 [主窗口] 启动爬取失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动爬取失败: {str(e)}")
            self.reset_ui_state()

    def connect_worker_signals(self):
        """连接工作线程信号"""
        if not self.worker_thread:
            return

        # 连接任务创建信号
        self.worker_thread.task_created.connect(self.on_task_created)
        self.worker_thread.task_creation_failed.connect(self.on_task_creation_failed)

        # 连接任务启动信号
        self.worker_thread.task_started.connect(self.on_task_started)
        self.worker_thread.task_start_failed.connect(self.on_task_start_failed)

        # 连接任务进度信号
        self.worker_thread.task_progress.connect(self.on_task_progress)

        # 连接任务完成信号
        self.worker_thread.task_completed.connect(self.on_task_completed)
        self.worker_thread.task_failed.connect(self.on_task_failed)

        # 连接状态消息信号
        self.worker_thread.status_message.connect(self.add_status_message)

        # 连接错误信号
        self.worker_thread.error_occurred.connect(self.on_worker_error)

        # 连接线程完成信号
        self.worker_thread.finished.connect(self.on_worker_finished)

        logger.info(f"✅ [主窗口] 工作线程信号连接完成")

    def on_task_created(self, task: ScrapingTask):
        """任务创建成功处理"""
        self.current_task = task
        logger.info(f"✅ [主窗口] 收到任务创建成功信号: {task.task_id}")
        self.add_status_message(f"✅ 任务创建成功: {task.name}")

        # 更新任务显示
        try:
            if hasattr(self, 'task_widget') and hasattr(self.task_widget, 'add_task'):
                self.task_widget.add_task(task)
                logger.debug(f"📋 [主窗口] 任务已添加到任务组件")
            else:
                logger.debug(f"📋 [主窗口] 任务组件不可用，跳过添加")
        except Exception as e:
            logger.warning(f"⚠️ [主窗口] 添加任务到组件失败: {str(e)}")

    def on_task_creation_failed(self, error_msg: str):
        """任务创建失败处理"""
        logger.error(f"❌ [主窗口] 收到任务创建失败信号: {error_msg}")
        self.add_status_message(f"❌ 任务创建失败: {error_msg}")
        QMessageBox.critical(self, "任务创建失败", error_msg)
        self.reset_ui_state()

    def on_task_started(self, task_id: str):
        """任务启动成功处理"""
        logger.info(f"✅ [主窗口] 收到任务启动成功信号: {task_id}")
        self.add_status_message("✅ 任务启动成功，开始爬取...")
        self.status_label.setText("正在爬取...")

        # 更新当前任务状态为运行中
        if self.current_task and self.current_task.task_id == task_id:
            from datetime import datetime
            self.current_task.status = TaskStatus.RUNNING
            self.current_task.started_at = datetime.now()

            # 更新任务列表中的任务状态
            if hasattr(self, 'task_widget') and hasattr(self.task_widget, 'task_list'):
                try:
                    self.task_widget.task_list.update_task(self.current_task)
                    logger.info(f"✅ [主窗口] 任务状态已更新为运行中: {task_id}")
                except Exception as e:
                    logger.warning(f"⚠️ [主窗口] 更新任务状态失败: {str(e)}")

    def on_task_start_failed(self, error_msg: str):
        """任务启动失败处理"""
        logger.error(f"❌ [主窗口] 收到任务启动失败信号: {error_msg}")
        self.add_status_message(f"❌ 任务启动失败: {error_msg}")
        QMessageBox.critical(self, "任务启动失败", error_msg)
        self.reset_ui_state()

    def on_task_progress(self, task_id: str, completed: int, total: int, failed: int):
        """任务进度更新处理"""
        if total > 0:
            progress = int((completed / total) * 100)
            self.progress_bar.setValue(progress)
            self.status_label.setText(f"正在爬取... ({completed}/{total})")

            logger.debug(f"📊 [主窗口] 任务进度更新: {completed}/{total} ({progress}%)")

        # 更新当前任务的进度信息
        if self.current_task and self.current_task.task_id == task_id:
            self.current_task.progress.total = total
            self.current_task.progress.completed = completed
            self.current_task.progress.failed = failed

            # 更新任务列表中的任务进度
            if hasattr(self, 'task_widget') and hasattr(self.task_widget, 'task_list'):
                try:
                    self.task_widget.task_list.update_task(self.current_task)
                    logger.debug(f"📊 [主窗口] 任务进度已更新: {task_id} - {completed}/{total}")
                except Exception as e:
                    logger.debug(f"⚠️ [主窗口] 更新任务进度失败: {str(e)}")

    def on_task_completed(self, task_id: str, results: list):
        """任务完成处理"""
        logger.info(f"🎉 [主窗口] 收到任务完成信号: {task_id}")
        self.add_status_message(f"🎉 任务完成！获取到 {len(results)} 个结果")
        self.status_label.setText("任务完成")
        self.progress_bar.setValue(100)

        # 更新当前任务状态为已完成
        if self.current_task and self.current_task.task_id == task_id:
            from datetime import datetime
            self.current_task.status = TaskStatus.COMPLETED
            self.current_task.completed_at = datetime.now()
            self.current_task.progress.completed = len(results)

            # 更新任务列表中的任务状态
            if hasattr(self, 'task_widget') and hasattr(self.task_widget, 'task_list'):
                try:
                    self.task_widget.task_list.update_task(self.current_task)
                    logger.info(f"✅ [主窗口] 任务状态已更新为已完成: {task_id}")
                except Exception as e:
                    logger.warning(f"⚠️ [主窗口] 更新任务状态失败: {str(e)}")

        # 刷新TikTok数据查看器
        try:
            if hasattr(self, 'tiktok_data_viewer') and self.tiktok_data_viewer:
                self.tiktok_data_viewer.refresh_data()
                logger.info(f"✅ [主窗口] TikTok数据查看器已刷新")
        except Exception as e:
            logger.warning(f"⚠️ [主窗口] 刷新TikTok数据查看器失败: {str(e)}")

        # 显示完成消息
        QMessageBox.information(self, "任务完成", f"爬取完成！获取到 {len(results)} 个商品")

        # 重置UI状态
        self.reset_ui_state()

        # 重新启用数据过滤控件（任务完成后）
        self.enable_data_filters()

        # 刷新产品表格
        if hasattr(self, 'product_table'):
            self.product_table.refresh_data()

        # 刷新TikTok数据查看器
        if hasattr(self, 'tiktok_data_viewer'):
            self.tiktok_data_viewer.refresh_data()

    def on_task_failed(self, task_id: str, error_msg: str):
        """任务失败处理"""
        logger.error(f"❌ [主窗口] 收到任务失败信号: {task_id} - {error_msg}")
        self.add_status_message(f"❌ 任务失败: {error_msg}")
        self.status_label.setText("任务失败")

        # 更新当前任务状态为失败
        if self.current_task and self.current_task.task_id == task_id:
            from datetime import datetime
            self.current_task.status = TaskStatus.FAILED
            self.current_task.completed_at = datetime.now()
            self.current_task.last_error = error_msg

            # 更新任务列表中的任务状态
            if hasattr(self, 'task_widget') and hasattr(self.task_widget, 'task_list'):
                try:
                    self.task_widget.task_list.update_task(self.current_task)
                    logger.info(f"✅ [主窗口] 任务状态已更新为失败: {task_id}")
                except Exception as e:
                    logger.warning(f"⚠️ [主窗口] 更新任务状态失败: {str(e)}")

        # 显示错误消息
        QMessageBox.critical(self, "任务失败", f"爬取任务失败:\n{error_msg}")

        # 重置UI状态
        self.reset_ui_state()

    def on_worker_error(self, error_msg: str):
        """工作线程错误处理"""
        logger.error(f"💥 [主窗口] 收到工作线程错误信号: {error_msg}")
        self.add_status_message(f"💥 工作线程错误: {error_msg}")

        # 显示错误消息
        QMessageBox.critical(self, "工作线程错误", f"工作线程发生错误:\n{error_msg}")

        # 重置UI状态
        self.reset_ui_state()

    def on_worker_finished(self):
        """工作线程完成处理"""
        logger.info(f"✅ [主窗口] 工作线程已完成")

        # 清理工作线程
        if self.worker_thread:
            self.worker_thread.deleteLater()
            self.worker_thread = None
    
    def pause_crawling(self):
        """暂停爬取"""
        if self.current_task:
            # 暂停当前任务
            self.add_status_message("任务已暂停")
            self.pause_button.setText("继续")
            self.pause_button.clicked.disconnect()
            self.pause_button.clicked.connect(self.resume_crawling)
    
    def resume_crawling(self):
        """恢复爬取"""
        if self.current_task:
            # 恢复当前任务
            self.add_status_message("任务已恢复")
            self.pause_button.setText("暂停")
            self.pause_button.clicked.disconnect()
            self.pause_button.clicked.connect(self.pause_crawling)
    
    def stop_crawling(self):
        """停止爬取"""
        logger.info(f"🛑 [主窗口] 用户请求停止爬取")

        # 停止工作线程
        if self.worker_thread and self.worker_thread.isRunning():
            logger.info(f"🛑 [主窗口] 停止工作线程")
            self.worker_thread.stop_task()

            # 等待线程结束（最多等待3秒）
            if not self.worker_thread.wait(3000):
                logger.warning(f"⚠️ [主窗口] 工作线程未能及时停止，强制终止")
                self.worker_thread.terminate()
                self.worker_thread.wait()

        # 停止当前任务
        if self.current_task:
            self.add_status_message("任务已停止")

        # 重置UI状态
        self.reset_ui_state()
    
    def export_data(self):
        """导出数据（已移除导出按钮，此方法保留用于其他地方调用）"""
        try:
            # 默认导出为Excel格式
            file_format = "xlsx"
            file_filter = "Excel文件 (*.xlsx)"

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出数据", f"tiktok_shop_data.{file_format}",
                file_filter
            )

            if file_path:
                self.add_status_message(f"正在导出数据到: {file_path}")
                # 这里添加实际的导出逻辑
                QMessageBox.information(self, "成功", "数据导出完成")

        except Exception as e:
            logger.error(f"导出数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出数据失败: {str(e)}")
    
    def update_task_status(self):
        """更新任务状态"""
        # 更新任务计数
        # self.task_count_label.setText(f"任务: {len(self.crawler_manager.active_tasks)}")
        
        # 更新商品计数
        # 这里可以从数据库获取商品总数
        pass
    
    def add_status_message(self, message: str):
        """添加状态消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_text.setTextCursor(cursor)
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")
        self.current_task = None
    
    def import_tasks(self):
        """导入任务"""
        QMessageBox.information(self, "提示", "导入任务功能开发中...")
    
    def export_to_json(self):
        """导出数据为JSON格式"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出JSON数据", "tiktok_products.json",
                "JSON文件 (*.json)"
            )

            if file_path:
                self.add_status_message("正在导出JSON数据...")
                output_path = self.data_exporter.export_products_to_json(
                    filename=Path(file_path).name
                )
                self.add_status_message(f"JSON数据导出完成: {output_path}")
                QMessageBox.information(self, "成功", f"数据已导出到:\n{output_path}")

        except Exception as e:
            logger.error(f"导出JSON数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出JSON数据失败:\n{str(e)}")

    def export_to_csv(self):
        """导出数据为CSV格式"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出CSV数据", "tiktok_products.csv",
                "CSV文件 (*.csv)"
            )

            if file_path:
                self.add_status_message("正在导出CSV数据...")
                output_path = self.data_exporter.export_products_to_csv(
                    filename=Path(file_path).name
                )
                self.add_status_message(f"CSV数据导出完成: {output_path}")
                QMessageBox.information(self, "成功", f"数据已导出到:\n{output_path}")

        except Exception as e:
            logger.error(f"导出CSV数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出CSV数据失败:\n{str(e)}")

    def export_to_excel(self):
        """导出数据为Excel格式"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel数据", "tiktok_products.xlsx",
                "Excel文件 (*.xlsx)"
            )

            if file_path:
                self.add_status_message("正在导出Excel数据...")
                output_path = self.data_exporter.export_products_to_excel(
                    filename=Path(file_path).name
                )
                self.add_status_message(f"Excel数据导出完成: {output_path}")
                QMessageBox.information(self, "成功", f"数据已导出到:\n{output_path}")

        except ImportError:
            QMessageBox.warning(self, "警告", "Excel导出需要安装pandas和openpyxl库")
        except Exception as e:
            logger.error(f"导出Excel数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出Excel数据失败:\n{str(e)}")

    def export_tasks(self):
        """导出任务"""
        QMessageBox.information(self, "提示", "导出任务功能开发中...")
    
    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        dialog.exec()
    
    def cleanup_data(self):
        """清理数据"""
        reply = QMessageBox.question(
            self, "确认", "确定要清理所有数据吗？此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 清理数据逻辑
            QMessageBox.information(self, "完成", "数据清理完成")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
            f"{config.gui.window_title}\n\n"
            "版本: 1.0.0\n"
            "一个专业的TikTok Shop商品数据采集和分析工具\n\n"
            "© 2025 Danly"
        )
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 检查是否有未保存的TikTok数据
        if self.check_unsaved_data():
            event.ignore()
            return

        reply = QMessageBox.question(
            self, "确认退出", "确定要退出应用程序吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 清理资源
            self.update_timer.stop()

            # 停止工作线程
            if self.worker_thread and self.worker_thread.isRunning():
                logger.info(f"🛑 [主窗口] 关闭时停止工作线程")
                self.worker_thread.stop_task()
                if not self.worker_thread.wait(2000):
                    self.worker_thread.terminate()
                    self.worker_thread.wait()

            event.accept()
        else:
            event.ignore()

    def check_unsaved_data(self) -> bool:
        """检查是否有未保存的数据，返回True表示需要阻止关闭"""
        try:
            from src.models.tiktok_product import tiktok_storage

            if tiktok_storage.has_unsaved_changes():
                count = tiktok_storage.get_product_count()

                reply = QMessageBox.question(
                    self, "未保存的数据",
                    f"您有 {count} 条未导出的TikTok商品数据。\n\n"
                    "如果现在退出，这些数据将会丢失。\n\n"
                    "是否要先导出数据？",
                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
                )

                if reply == QMessageBox.Yes:
                    # 切换到TikTok数据标签页
                    if hasattr(self, 'tab_widget') and hasattr(self, 'tiktok_data_viewer'):
                        # 找到TikTok数据标签页的索引
                        for i in range(self.tab_widget.count()):
                            if self.tab_widget.widget(i) == self.tiktok_data_viewer:
                                self.tab_widget.setCurrentIndex(i)
                                break

                    # 提示用户导出数据
                    QMessageBox.information(
                        self, "导出数据",
                        "请在TikTok数据标签页中点击\"导出Excel\"或\"导出CSV\"按钮来保存数据。\n\n"
                        "导出完成后再次关闭程序。"
                    )
                    return True  # 阻止关闭

                elif reply == QMessageBox.Cancel:
                    return True  # 阻止关闭

                # 用户选择No，继续关闭，不保存数据

            return False  # 允许关闭

        except Exception as e:
            logger.error(f"💥 [主窗口] 检查未保存数据失败: {str(e)}")
            return False  # 出错时允许关闭

    def on_data_exported(self):
        """数据导出完成处理"""
        logger.info("📊 [主窗口] TikTok数据导出完成")
        # 可以在这里添加其他处理逻辑



    def enable_data_filters(self):
        """启用数据过滤控件（在任务完成后调用）"""
        try:
            # 启用所有过滤控件
            self.shop_count_min_spin.setEnabled(True)
            self.shop_count_max_spin.setEnabled(True)
            self.shipping_fee_min_spin.setEnabled(True)
            self.shipping_fee_max_spin.setEnabled(True)
            self.price_min_spin.setEnabled(True)
            self.price_max_spin.setEnabled(True)
            self.sales_min_spin.setEnabled(True)
            self.sales_max_spin.setEnabled(True)

            logger.info("✅ [数据过滤] 过滤控件已重新启用")
            self.add_status_message("✅ 可以设置新的过滤条件")

        except Exception as e:
            logger.error(f"❌ [数据过滤] 启用过滤控件失败: {str(e)}")

    def disable_data_filters(self):
        """禁用数据过滤控件（在任务进行中调用）"""
        try:
            # 禁用所有过滤控件（任务进行中不允许修改过滤条件）
            self.shop_count_min_spin.setEnabled(False)
            self.shop_count_max_spin.setEnabled(False)
            self.shipping_fee_min_spin.setEnabled(False)
            self.shipping_fee_max_spin.setEnabled(False)
            self.price_min_spin.setEnabled(False)
            self.price_max_spin.setEnabled(False)
            self.sales_min_spin.setEnabled(False)
            self.sales_max_spin.setEnabled(False)

            logger.info("🔒 [数据过滤] 过滤控件已禁用（任务进行中）")

        except Exception as e:
            logger.error(f"❌ [数据过滤] 禁用过滤控件失败: {str(e)}")



    def get_filter_conditions(self) -> Dict[str, Any]:
        """获取当前的过滤条件"""
        try:
            return {
                'shop_count_min': self.shop_count_min_spin.value(),
                'shop_count_max': self.shop_count_max_spin.value(),
                'shipping_fee_min': self.shipping_fee_min_spin.value(),
                'shipping_fee_max': self.shipping_fee_max_spin.value(),
                'price_min': self.price_min_spin.value(),
                'price_max': self.price_max_spin.value(),
                'sales_min': self.sales_min_spin.value(),
                'sales_max': self.sales_max_spin.value(),
            }
        except Exception as e:
            logger.error(f"❌ [过滤条件] 获取过滤条件失败: {str(e)}")
            return {
                'shop_count_min': 0,
                'shop_count_max': 999999,
                'shipping_fee_min': 0.0,
                'shipping_fee_max': 9999.99,
                'price_min': 0.0,
                'price_max': 99999.99,
                'sales_min': 0,
                'sales_max': 999999,
            }

    def get_crawler_mode(self) -> str:
        """获取爬虫模式设置"""
        try:
            mode_index = self.crawler_mode_combo.currentIndex()
            mode_map = {
                0: "direct",      # 直连模式
                1: "browser",     # 浏览器模式
                2: "auto"         # 自动切换模式
            }
            return mode_map.get(mode_index, "direct")
        except (AttributeError, KeyError):
            return "direct"  # 默认直连模式
