"""
销量状态检测模块
用于检测商品销量状态，支持智能停止加载更多功能
"""

import re
import json
from typing import List, Dict, Optional, Tuple
from loguru import logger
from bs4 import BeautifulSoup


class SalesDetector:
    """销量检测器"""
    
    def __init__(self, patterns: List[str] = None):
        """
        初始化销量检测器
        
        Args:
            patterns: 零销量检测的正则表达式模式列表
        """
        self.patterns = patterns or [
            r'sold_count[:\s]*0\b',  # "sold_count: 0" 或 "sold_count 0"
            r'销量[:\s]*0\b',  # "销量: 0" 或 "销量 0"
            r'已售[:\s]*0\b',  # "已售: 0" 或 "已售 0"
            r'0\s*sold_count',       # "0 sold_count"
            r'0\s*销量',       # "0 销量"
            r'0\s*已售',       # "0 已售"
            r'no\s*sales',     # "no sales"
            r'暂无销量',        # "暂无销量"
            r'未售出',          # "未售出"
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE) for pattern in self.patterns
        ]
        
        logger.debug(f"🔍 [销量检测器] 初始化完成，加载了 {len(self.patterns)} 个检测模式")
    
    def detect_zero_sales_in_text(self, text: str) -> bool:
        """
        在文本中检测零销量模式
        
        Args:
            text: 要检测的文本内容
            
        Returns:
            bool: 如果检测到零销量则返回True
        """
        if not text:
            return False
        
        # 清理文本，移除多余的空白字符
        cleaned_text = ' '.join(text.split())
        
        for pattern in self.compiled_patterns:
            if pattern.search(cleaned_text):
                logger.debug(f"🎯 [销量检测器] 检测到零销量模式: {pattern.pattern}")
                return True
        
        return False
    
    def detect_zero_sales_in_html(self, html_content: str) -> Tuple[bool, List[str]]:
        """
        在HTML内容中检测零销量商品

        Args:
            html_content: HTML页面内容

        Returns:
            Tuple[bool, List[str]]: (是否检测到零销量, 检测到的零销量文本列表)
        """
        try:
            # 优先检查TikTok Shop的__MODERN_ROUTER_DATA__脚本标签
            zero_sales_found = self._check_modern_router_data_for_zero_sales(html_content)
            if zero_sales_found:
                return True, ["sold_count: 0 (来自__MODERN_ROUTER_DATA__)"]

            soup = BeautifulSoup(html_content, 'html.parser')
            zero_sales_texts = []

            # 查找可能包含销量信息的元素
            sales_selectors = [
                '[class*="sales"]',
                '[class*="sold"]',
                '[class*="销量"]',
                '[class*="已售"]',
                '[data-testid*="sales"]',
                '[data-testid*="sold"]',
                'span:contains("sold")',
                'span:contains("销量")',
                'span:contains("已售")',
                'div:contains("sold")',
                'div:contains("销量")',
                'div:contains("已售")',
            ]

            for selector in sales_selectors:
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        text = element.get_text(strip=True)
                        if text and self.detect_zero_sales_in_text(text):
                            zero_sales_texts.append(text)
                            logger.debug(f"🔍 [销量检测器] 在元素中发现零销量: {text}")
                except Exception as e:
                    logger.debug(f"选择器 {selector} 执行失败: {str(e)}")
                    continue

            # 如果没有通过选择器找到，则在整个页面文本中搜索
            if not zero_sales_texts:
                page_text = soup.get_text()
                if self.detect_zero_sales_in_text(page_text):
                    # 尝试提取具体的零销量文本片段
                    for pattern in self.compiled_patterns:
                        matches = pattern.findall(page_text)
                        if matches:
                            zero_sales_texts.extend(matches)
                            break

            has_zero_sales = len(zero_sales_texts) > 0

            if has_zero_sales:
                logger.info(f"⚠️ [销量检测器] 检测到 {len(zero_sales_texts)} 个零销量商品")
                for text in zero_sales_texts[:3]:  # 只记录前3个
                    logger.debug(f"   - {text}")

            return has_zero_sales, zero_sales_texts

        except Exception as e:
            logger.error(f"❌ [销量检测器] HTML销量检测失败: {str(e)}")
            return False

    def _check_modern_router_data_for_zero_sales(self, html_content: str) -> bool:
        """
        检查TikTok Shop的__MODERN_ROUTER_DATA__脚本标签中的sold_count字段

        Args:
            html_content: HTML页面内容

        Returns:
            bool: 如果检测到sold_count为0则返回True
        """
        try:
            # 查找__MODERN_ROUTER_DATA__脚本标签
            soup = BeautifulSoup(html_content, 'html.parser')
            script_tag = soup.find('script', {'type': 'application/json', 'id': '__MODERN_ROUTER_DATA__'})

            if not script_tag:
                logger.debug("🔍 [销量检测器] 未找到__MODERN_ROUTER_DATA__脚本标签")
                return False

            script_content = script_tag.string
            if not script_content:
                logger.debug("🔍 [销量检测器] __MODERN_ROUTER_DATA__脚本标签内容为空")
                return False

            # 解析JSON数据
            try:
                data = json.loads(script_content)
                logger.debug("🔍 [销量检测器] 成功解析__MODERN_ROUTER_DATA__的JSON数据")

                # 递归搜索sold_count字段
                zero_sales_found = self._search_sold_count_in_data(data)

                if zero_sales_found:
                    logger.info("⚠️ [销量检测器] 在__MODERN_ROUTER_DATA__中检测到sold_count为0的商品")
                    return True
                else:
                    logger.debug("✅ [销量检测器] __MODERN_ROUTER_DATA__中未发现零销量商品")
                    return False

            except json.JSONDecodeError as e:
                logger.debug(f"🔍 [销量检测器] __MODERN_ROUTER_DATA__JSON解析失败: {str(e)}")
                return False

        except Exception as e:
            logger.debug(f"🔍 [销量检测器] 检查__MODERN_ROUTER_DATA__时出现异常: {str(e)}")
            return False

    def _search_sold_count_in_data(self, data, path="") -> bool:
        """
        递归搜索数据中的sold_count字段

        Args:
            data: 要搜索的数据对象
            path: 当前搜索路径（用于调试）

        Returns:
            bool: 如果找到sold_count为0则返回True
        """
        try:
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key

                    # 检查sold_count字段
                    if key == "sold_count":
                        if isinstance(value, (int, float)) and value == 0:
                            logger.debug(f"🎯 [销量检测器] 发现零销量: {current_path} = {value}")
                            return True
                        elif isinstance(value, str):
                            try:
                                num_value = float(value)
                                if num_value == 0:
                                    logger.debug(f"🎯 [销量检测器] 发现零销量(字符串): {current_path} = {value}")
                                    return True
                            except (ValueError, TypeError):
                                pass

                    # 递归搜索嵌套对象
                    if isinstance(value, (dict, list)):
                        if self._search_sold_count_in_data(value, current_path):
                            return True

            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    if self._search_sold_count_in_data(item, current_path):
                        return True

            return False

        except Exception as e:
            logger.debug(f"🔍 [销量检测器] 搜索sold_count时出现异常: {str(e)}")
            return False, []

    def detect_zero_sales_in_json(self, json_content: str) -> Tuple[bool, List[Dict]]:
        """
        在JSON数据中检测零销量商品
        
        Args:
            json_content: JSON字符串内容
            
        Returns:
            Tuple[bool, List[Dict]]: (是否检测到零销量, 零销量商品数据列表)
        """
        try:
            data = json.loads(json_content)
            zero_sales_products = []
            
            def search_sales_in_data(obj, path=""):
                """递归搜索销量数据"""
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        
                        # 检查销量相关字段
                        if any(sales_key in key.lower() for sales_key in ['sales', 'sold', '销量', '已售']):
                            if isinstance(value, (int, float)) and value == 0:
                                zero_sales_products.append({
                                    'path': current_path,
                                    'value': value,
                                    'context': obj
                                })
                                logger.debug(f"🎯 [销量检测器] JSON中发现零销量字段: {current_path} = {value}")
                            elif isinstance(value, str) and self.detect_zero_sales_in_text(value):
                                zero_sales_products.append({
                                    'path': current_path,
                                    'value': value,
                                    'context': obj
                                })
                                logger.debug(f"🎯 [销量检测器] JSON中发现零销量文本: {current_path} = {value}")
                        
                        # 递归搜索
                        if isinstance(value, (dict, list)):
                            search_sales_in_data(value, current_path)
                
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        current_path = f"{path}[{i}]" if path else f"[{i}]"
                        search_sales_in_data(item, current_path)
            
            search_sales_in_data(data)
            
            has_zero_sales = len(zero_sales_products) > 0
            
            if has_zero_sales:
                logger.info(f"⚠️ [销量检测器] JSON中检测到 {len(zero_sales_products)} 个零销量商品")
            
            return has_zero_sales, zero_sales_products
            
        except json.JSONDecodeError as e:
            logger.debug(f"JSON解析失败: {str(e)}")
            return False, []
        except Exception as e:
            logger.error(f"❌ [销量检测器] JSON销量检测失败: {str(e)}")
            return False, []
    
    def analyze_content_increment(self, old_content: str, new_content: str) -> Dict:
        """
        分析内容增量中的销量状态
        
        Args:
            old_content: 旧的页面内容
            new_content: 新的页面内容
            
        Returns:
            Dict: 分析结果，包含是否有零销量、增量内容等信息
        """
        try:
            # 计算内容增量
            old_length = len(old_content) if old_content else 0
            new_length = len(new_content) if new_content else 0
            increment_length = new_length - old_length
            
            # 提取增量内容（简单方法：取新内容的后部分）
            if increment_length > 0:
                increment_content = new_content[-increment_length:]
            else:
                increment_content = ""
            
            # 检测增量内容中的零销量
            has_zero_sales_html, zero_sales_texts = self.detect_zero_sales_in_html(increment_content)
            
            # 尝试从增量内容中提取JSON并检测
            has_zero_sales_json = False
            zero_sales_json_data = []
            
            # 查找JSON数据
            json_patterns = [
                r'<script[^>]*type=["\']application/json["\'][^>]*>(.*?)</script>',
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__MODERN_ROUTER_DATA__\s*=\s*({.*?});',
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, increment_content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    has_json_zero_sales, json_zero_sales = self.detect_zero_sales_in_json(match)
                    if has_json_zero_sales:
                        has_zero_sales_json = True
                        zero_sales_json_data.extend(json_zero_sales)
            
            result = {
                'content_increment': increment_length,
                'has_content_increase': increment_length > 0,
                'has_zero_sales': has_zero_sales_html or has_zero_sales_json,
                'zero_sales_html': {
                    'detected': has_zero_sales_html,
                    'texts': zero_sales_texts
                },
                'zero_sales_json': {
                    'detected': has_zero_sales_json,
                    'data': zero_sales_json_data
                },
                'increment_content_preview': increment_content[:500] if increment_content else ""
            }
            
            if result['has_zero_sales']:
                logger.warning(f"⚠️ [销量检测器] 增量内容中检测到零销量商品")
                logger.debug(f"   - HTML检测: {has_zero_sales_html}")
                logger.debug(f"   - JSON检测: {has_zero_sales_json}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [销量检测器] 内容增量分析失败: {str(e)}")
            return {
                'content_increment': 0,
                'has_content_increase': False,
                'has_zero_sales': False,
                'error': str(e)
            }


class ContentIncrementDetector:
    """内容增量检测器"""
    
    def __init__(self, min_increase: int = 1000, increase_ratio: float = 0.05):
        """
        初始化内容增量检测器
        
        Args:
            min_increase: 最小内容增量（字符数）
            increase_ratio: 内容增量比例
        """
        self.min_increase = min_increase
        self.increase_ratio = increase_ratio
        
        logger.debug(f"📏 [增量检测器] 初始化完成，最小增量: {min_increase}字符，增量比例: {increase_ratio*100}%")
    
    def detect_content_increase(self, baseline_length: int, new_length: int) -> Tuple[bool, Dict]:
        """
        检测内容是否有显著增量
        
        Args:
            baseline_length: 基准内容长度
            new_length: 新内容长度
            
        Returns:
            Tuple[bool, Dict]: (是否有显著增量, 详细信息)
        """
        try:
            increment = new_length - baseline_length
            ratio = increment / baseline_length if baseline_length > 0 else 0
            
            # 判断是否有显著增量
            has_significant_increase = (
                increment >= self.min_increase or 
                ratio >= self.increase_ratio
            )
            
            result = {
                'baseline_length': baseline_length,
                'new_length': new_length,
                'increment': increment,
                'increment_ratio': ratio,
                'has_significant_increase': has_significant_increase,
                'min_increase_threshold': self.min_increase,
                'ratio_threshold': self.increase_ratio
            }
            
            if has_significant_increase:
                logger.debug(f"✅ [增量检测器] 检测到显著内容增量: +{increment}字符 ({ratio*100:.1f}%)")
            else:
                logger.debug(f"❌ [增量检测器] 未检测到显著增量: +{increment}字符 ({ratio*100:.1f}%)")
            
            return has_significant_increase, result
            
        except Exception as e:
            logger.error(f"❌ [增量检测器] 内容增量检测失败: {str(e)}")
            return False, {'error': str(e)}
