"""
异步工作线程 - 处理PyQt6中的异步操作
"""

import asyncio
from typing import Dict, Any, Optional
from PyQt5.QtCore import QThread, pyqtSignal
from loguru import logger

from ..crawler.crawler_manager import CrawlerManager
from ..models.scraping_task import ScrapingTask


class AsyncCrawlerWorker(QThread):
    """异步爬虫工作线程"""
    
    # 信号定义
    task_created = pyqtSignal(object)  # 任务创建成功信号
    task_creation_failed = pyqtSignal(str)  # 任务创建失败信号
    task_started = pyqtSignal(str)  # 任务启动成功信号
    task_start_failed = pyqtSignal(str)  # 任务启动失败信号
    task_progress = pyqtSignal(str, int, int, int)  # 任务进度信号 (task_id, completed, total, failed)
    task_completed = pyqtSignal(str, list)  # 任务完成信号 (task_id, results)
    task_failed = pyqtSignal(str, str)  # 任务失败信号 (task_id, error)
    status_message = pyqtSignal(str)  # 状态消息信号
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.crawler_manager = None
        self.url = None
        self.config = None
        self.current_task = None
        self._should_stop = False
    
    def setup_task(self, url: str, config: Dict[str, Any]):
        """设置要执行的任务"""
        self.url = url
        self.config = config
        logger.info(f"🔧 [工作线程] 设置任务: {url}")
    
    def stop_task(self):
        """停止任务"""
        self._should_stop = True
        logger.info(f"🛑 [工作线程] 收到停止信号")

        # 如果有爬虫管理器，尝试停止当前任务
        if self.crawler_manager and self.current_task:
            try:
                logger.info(f"🛑 [工作线程] 尝试停止爬虫任务: {self.current_task.task_id}")
                # 这里可以添加停止爬虫任务的逻辑
            except Exception as e:
                logger.warning(f"⚠️ [工作线程] 停止爬虫任务失败: {str(e)}")
    
    def run(self):
        """线程主函数 - 在这里运行异步事件循环"""
        try:
            logger.info(f"🚀 [工作线程] 启动异步事件循环")
            
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 运行异步任务
                loop.run_until_complete(self._run_async_task())
            finally:
                # 清理事件循环
                loop.close()
                logger.info(f"✅ [工作线程] 事件循环已关闭")
                
        except Exception as e:
            logger.error(f"💥 [工作线程] 线程执行异常: {str(e)}")
            self.error_occurred.emit(f"工作线程异常: {str(e)}")
    
    async def _run_async_task(self):
        """运行异步任务"""
        try:
            logger.info(f"📋 [工作线程] 开始执行异步任务")
            
            # 发送状态消息
            self.status_message.emit("正在初始化爬虫管理器...")
            
            # 创建爬虫管理器
            self.crawler_manager = CrawlerManager()
            logger.info(f"✅ [工作线程] 爬虫管理器创建成功")
            
            # 检查是否需要停止
            if self._should_stop:
                logger.info(f"🛑 [工作线程] 任务被取消")
                return
            
            # 步骤1: 创建任务
            self.status_message.emit("正在创建爬取任务...")
            logger.info(f"📋 [工作线程] 创建任务: {self.url}")
            
            task = await self.crawler_manager.create_task_from_url(self.url, **self.config)
            
            if not task:
                error_msg = "任务创建失败"
                logger.error(f"❌ [工作线程] {error_msg}")
                self.task_creation_failed.emit(error_msg)
                return
            
            self.current_task = task
            logger.info(f"✅ [工作线程] 任务创建成功: {task.task_id}")
            self.task_created.emit(task)
            
            # 检查是否需要停止
            if self._should_stop:
                logger.info(f"🛑 [工作线程] 任务被取消")
                return
            
            # 步骤2: 启动任务
            self.status_message.emit("正在启动爬取任务...")
            logger.info(f"🚀 [工作线程] 启动任务: {task.task_id}")
            
            success = await self.crawler_manager.start_task(task.task_id)
            
            if success:
                logger.info(f"✅ [工作线程] 任务启动成功")
                self.task_started.emit(task.task_id)
                self.status_message.emit("任务启动成功，开启爬取工作...")
                
                # 监控任务进度
                try:
                    await self._monitor_task_progress(task)
                except asyncio.CancelledError:
                    logger.info(f"🛑 [工作线程] 任务监控被取消")
                except Exception as monitor_error:
                    logger.error(f"💥 [工作线程] 任务监控异常: {str(monitor_error)}")

            else:
                error_msg = "任务启动失败"
                logger.error(f"❌ [工作线程] {error_msg}")
                self.task_start_failed.emit(error_msg)

        except asyncio.CancelledError:
            logger.info(f"🛑 [工作线程] 异步任务被取消")
        except Exception as e:
            logger.error(f"💥 [工作线程] 异步任务执行异常: {str(e)}")
            import traceback
            logger.error(f"   - 堆栈跟踪: {traceback.format_exc()}")
            self.error_occurred.emit(f"任务执行异常: {str(e)}")
        finally:
            # 清理资源
            logger.info(f"🧹 [工作线程] 清理异步任务资源")
            if self.crawler_manager:
                try:
                    # 这里可以添加清理爬虫管理器的逻辑
                    pass
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ [工作线程] 清理资源失败: {str(cleanup_error)}")
    
    async def _monitor_task_progress(self, task: ScrapingTask):
        """监控任务进度"""
        try:
            logger.info(f"👀 [工作线程] 开始监控任务进度: {task.task_id}")

            # 导入TaskStatus
            from ..models.scraping_task import TaskStatus

            # 轮询任务状态
            while not self._should_stop:
                try:
                    await asyncio.sleep(1)  # 每秒检查一次

                    # 检查是否需要停止
                    if self._should_stop:
                        logger.info(f"🛑 [工作线程] 监控被停止")
                        break

                    # 获取任务状态
                    current_task = self.crawler_manager.get_task_status(task.task_id)
                    if not current_task:
                        logger.warning(f"⚠️ [工作线程] 无法获取任务状态: {task.task_id}")
                        break

                    # 发送进度信号
                    try:
                        self.task_progress.emit(
                            current_task.task_id,
                            current_task.progress.completed,
                            current_task.progress.total,
                            current_task.progress.failed
                        )
                    except Exception as progress_error:
                        logger.warning(f"⚠️ [工作线程] 发送进度信号失败: {str(progress_error)}")

                    # 检查任务是否完成 - 使用安全的状态检查
                    if current_task.status == TaskStatus.COMPLETED:
                        logger.info(f"🎉 [工作线程] 任务完成: {task.task_id}")
                        self.task_completed.emit(current_task.task_id, current_task.results)
                        self.status_message.emit("任务完成！")
                        break
                    elif current_task.status == TaskStatus.FAILED:
                        logger.error(f"❌ [工作线程] 任务失败: {task.task_id}")
                        error_msg = getattr(current_task, 'last_error', None) or "未知错误"
                        self.task_failed.emit(current_task.task_id, error_msg)
                        self.status_message.emit("任务失败")
                        break
                    elif current_task.status == TaskStatus.CANCELLED:
                        logger.info(f"🛑 [工作线程] 任务被取消: {task.task_id}")
                        self.status_message.emit("任务已取消")
                        break

                except asyncio.CancelledError:
                    logger.info(f"🛑 [工作线程] 监控任务被取消")
                    break
                except Exception as loop_error:
                    logger.warning(f"⚠️ [工作线程] 监控循环异常: {str(loop_error)}")
                    # 继续监控，不要因为单次错误就退出
                    continue

        except Exception as e:
            logger.error(f"💥 [工作线程] 监控任务进度异常: {str(e)}")
            self.error_occurred.emit(f"监控任务进度异常: {str(e)}")
        finally:
            logger.info(f"✅ [工作线程] 任务监控结束: {task.task_id}")
    
    def get_current_task(self) -> Optional[ScrapingTask]:
        """获取当前任务"""
        return self.current_task
