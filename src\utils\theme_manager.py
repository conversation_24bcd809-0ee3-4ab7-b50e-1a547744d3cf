"""
主题管理器
"""

from pathlib import Path
from typing import Optional
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from loguru import logger

from ..config.settings import config


class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = pyqtSignal(str)  # 主题改变信号
    
    def __init__(self):
        super().__init__()
        self.current_theme = config.gui.theme
        self.themes_dir = Path(__file__).parent.parent.parent / "resources" / "styles"
        
        # 可用主题
        self.available_themes = {
            "light": "浅色主题",
            "dark": "深色主题"
        }
    
    def get_available_themes(self) -> dict:
        """获取可用主题列表"""
        return self.available_themes.copy()
    
    def get_current_theme(self) -> str:
        """获取当前主题"""
        return self.current_theme
    
    def set_theme(self, theme_name: str) -> bool:
        """设置主题"""
        try:
            if theme_name not in self.available_themes:
                logger.warning(f"未知主题: {theme_name}")
                return False
            
            # 加载主题样式
            style_sheet = self.load_theme_stylesheet(theme_name)
            if style_sheet is None:
                return False
            
            # 应用主题
            app = QApplication.instance()
            if app:
                app.setStyleSheet(style_sheet)
            
            # 更新当前主题
            old_theme = self.current_theme
            self.current_theme = theme_name
            
            # 更新配置
            config.gui.theme = theme_name
            
            # 发送主题改变信号
            self.theme_changed.emit(theme_name)
            
            logger.info(f"主题已切换: {old_theme} -> {theme_name}")
            return True
            
        except Exception as e:
            logger.error(f"设置主题失败: {theme_name} - {str(e)}")
            return False
    
    def load_theme_stylesheet(self, theme_name: str) -> Optional[str]:
        """加载主题样式表"""
        try:
            style_file = self.themes_dir / f"{theme_name}_theme.qss"
            
            if not style_file.exists():
                logger.warning(f"主题文件不存在: {style_file}")
                return None
            
            with open(style_file, 'r', encoding='utf-8') as f:
                stylesheet = f.read()
            
            # 处理资源路径
            stylesheet = self.process_resource_paths(stylesheet)
            
            logger.debug(f"加载主题样式: {style_file}")
            return stylesheet
            
        except Exception as e:
            logger.error(f"加载主题样式失败: {theme_name} - {str(e)}")
            return None
    
    def process_resource_paths(self, stylesheet: str) -> str:
        """处理样式表中的资源路径"""
        # 将相对路径转换为绝对路径
        resources_dir = Path(__file__).parent.parent.parent / "resources"
        
        # 替换资源路径
        stylesheet = stylesheet.replace(
            "url(resources/",
            f"url({resources_dir.as_posix()}/"
        )
        
        return stylesheet
    
    def toggle_theme(self) -> bool:
        """切换主题"""
        if self.current_theme == "light":
            return self.set_theme("dark")
        else:
            return self.set_theme("light")
    
    def apply_default_theme(self) -> bool:
        """应用默认主题"""
        return self.set_theme(config.gui.theme)
    
    def create_custom_theme(self, theme_name: str, base_theme: str = "light") -> bool:
        """创建自定义主题"""
        try:
            if theme_name in self.available_themes:
                logger.warning(f"主题已存在: {theme_name}")
                return False
            
            # 复制基础主题
            base_file = self.themes_dir / f"{base_theme}_theme.qss"
            custom_file = self.themes_dir / f"{theme_name}_theme.qss"
            
            if not base_file.exists():
                logger.error(f"基础主题不存在: {base_theme}")
                return False
            
            # 复制文件
            import shutil
            shutil.copy2(base_file, custom_file)
            
            # 添加到可用主题
            self.available_themes[theme_name] = f"自定义主题 - {theme_name}"
            
            logger.info(f"创建自定义主题: {theme_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建自定义主题失败: {theme_name} - {str(e)}")
            return False
    
    def delete_custom_theme(self, theme_name: str) -> bool:
        """删除自定义主题"""
        try:
            if theme_name in ["light", "dark"]:
                logger.warning(f"不能删除内置主题: {theme_name}")
                return False
            
            if theme_name not in self.available_themes:
                logger.warning(f"主题不存在: {theme_name}")
                return False
            
            # 删除主题文件
            theme_file = self.themes_dir / f"{theme_name}_theme.qss"
            if theme_file.exists():
                theme_file.unlink()
            
            # 从可用主题中移除
            del self.available_themes[theme_name]
            
            # 如果当前主题被删除，切换到默认主题
            if self.current_theme == theme_name:
                self.set_theme("light")
            
            logger.info(f"删除自定义主题: {theme_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除自定义主题失败: {theme_name} - {str(e)}")
            return False
    
    def export_theme(self, theme_name: str, export_path: Path) -> bool:
        """导出主题"""
        try:
            if theme_name not in self.available_themes:
                logger.warning(f"主题不存在: {theme_name}")
                return False
            
            theme_file = self.themes_dir / f"{theme_name}_theme.qss"
            if not theme_file.exists():
                logger.error(f"主题文件不存在: {theme_file}")
                return False
            
            # 复制主题文件
            import shutil
            shutil.copy2(theme_file, export_path)
            
            logger.info(f"导出主题: {theme_name} -> {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出主题失败: {theme_name} - {str(e)}")
            return False
    
    def import_theme(self, theme_file: Path, theme_name: Optional[str] = None) -> bool:
        """导入主题"""
        try:
            if not theme_file.exists():
                logger.error(f"主题文件不存在: {theme_file}")
                return False
            
            # 确定主题名称
            if not theme_name:
                theme_name = theme_file.stem.replace("_theme", "")
            
            # 检查主题名称冲突
            if theme_name in self.available_themes:
                theme_name = f"{theme_name}_imported"
            
            # 复制主题文件
            target_file = self.themes_dir / f"{theme_name}_theme.qss"
            import shutil
            shutil.copy2(theme_file, target_file)
            
            # 添加到可用主题
            self.available_themes[theme_name] = f"导入主题 - {theme_name}"
            
            logger.info(f"导入主题: {theme_file} -> {theme_name}")
            return True
            
        except Exception as e:
            logger.error(f"导入主题失败: {theme_file} - {str(e)}")
            return False


# 全局主题管理器实例
theme_manager = ThemeManager()


def get_theme_manager() -> ThemeManager:
    """获取主题管理器实例"""
    return theme_manager


def apply_theme(theme_name: str) -> bool:
    """应用主题的便捷函数"""
    return theme_manager.set_theme(theme_name)


def toggle_theme() -> bool:
    """切换主题的便捷函数"""
    return theme_manager.toggle_theme()


def get_current_theme() -> str:
    """获取当前主题的便捷函数"""
    return theme_manager.get_current_theme()
