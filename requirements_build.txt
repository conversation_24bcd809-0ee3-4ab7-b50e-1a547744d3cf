# TikTok Shop Tool - 打包专用依赖文件
# 只包含运行时必需的依赖，排除开发和测试工具

# GUI框架
PyQt5==5.15.10

# 数据处理
pandas==2.1.4
openpyxl==3.1.2
xlsxwriter==3.1.9

# 网络请求和爬虫
requests==2.31.0
aiohttp==3.9.1
beautifulsoup4==4.12.2
lxml==4.9.4
selenium==4.16.0

# 用户代理和反爬虫
fake-useragent==1.4.0
undetected-chromedriver==3.5.4

# 日志系统
loguru==0.7.2

# 配置管理
pyyaml==6.0.1
python-dotenv==1.0.0

# 图像处理
Pillow==10.1.0

# 数据验证
pydantic==2.5.2

# 进度条
tqdm==4.66.1

# 时间处理
python-dateutil==2.8.2



# 系统信息
psutil==5.9.6

# 加密和安全
cryptography==42.0.0

# 打包工具
pyinstaller==6.2.0

# 额外的运行时依赖
# 这些包可能被PyInstaller遗漏，需要显式包含
setuptools>=65.0.0
wheel>=0.38.0
packaging>=21.0

# Windows特定依赖
pywin32>=306; sys_platform == "win32"
pywin32-ctypes>=0.2.0; sys_platform == "win32"

# 网络相关
urllib3>=1.26.0
certifi>=2022.0.0
charset-normalizer>=3.0.0
idna>=3.0.0

# 异步支持
asyncio-throttle>=1.0.0
aiofiles>=23.0.0
