"""
爬取任务数据模型
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Callable
from datetime import datetime
from enum import Enum
import json
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TaskType(Enum):
    """任务类型枚举"""
    SINGLE_PRODUCT = "single_product"
    SHOP_PRODUCTS = "shop_products"
    CATEGORY_PRODUCTS = "category_products"
    SEARCH_PRODUCTS = "search_products"


@dataclass
class TaskProgress:
    """任务进度信息"""
    total: int = 0
    completed: int = 0
    failed: int = 0
    skipped: int = 0
    
    @property
    def percentage(self) -> float:
        """完成百分比"""
        if self.total == 0:
            return 0.0
        return (self.completed / self.total) * 100
    
    @property
    def remaining(self) -> int:
        """剩余数量"""
        return self.total - self.completed - self.failed - self.skipped
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total": self.total,
            "completed": self.completed,
            "failed": self.failed,
            "skipped": self.skipped,
            "percentage": self.percentage,
            "remaining": self.remaining
        }


@dataclass
class TaskError:
    """任务错误信息"""
    error_type: str
    error_message: str
    error_details: Optional[str] = None
    occurred_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "error_type": self.error_type,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "occurred_at": self.occurred_at.isoformat()
        }


@dataclass
class ScrapingTask:
    """爬取任务数据模型"""
    
    # 基本信息
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: Optional[str] = None
    task_type: TaskType = TaskType.SINGLE_PRODUCT
    
    # 任务参数
    target_url: str = ""
    shop_id: Optional[str] = None
    shop_name: Optional[str] = None
    search_keywords: Optional[str] = None
    category: Optional[str] = None
    
    # 爬取配置
    max_products: Optional[int] = None
    min_sales: Optional[int] = None
    max_price: Optional[float] = None
    min_price: Optional[float] = None
    include_variants: bool = True
    include_images: bool = True
    include_reviews: bool = False

    # 过滤条件 - 新增字段
    filter_conditions: Optional[Dict[str, Any]] = None

    # 爬虫模式 - 新增字段
    crawler_mode: str = "direct"  # direct, browser, auto
    
    # 状态和进度
    status: TaskStatus = TaskStatus.PENDING
    progress: TaskProgress = field(default_factory=TaskProgress)
    
    # 结果信息
    results: List[str] = field(default_factory=list)  # 产品ID列表
    output_file: Optional[str] = None
    export_format: str = "xlsx"
    
    # 错误信息
    errors: List[TaskError] = field(default_factory=list)
    last_error: Optional[str] = None
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    
    # 性能统计
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    
    # 回调函数 (不序列化)
    progress_callback: Optional[Callable[[TaskProgress], None]] = field(default=None, repr=False)
    completion_callback: Optional[Callable[['ScrapingTask'], None]] = field(default=None, repr=False)
    error_callback: Optional[Callable[[TaskError], None]] = field(default=None, repr=False)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.name:
            self.name = f"{self.task_type.value}_{self.task_id[:8]}"
    
    def start(self):
        """开始任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
    
    def complete(self):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        if self.completion_callback:
            self.completion_callback(self)
    
    def fail(self, error_message: str, error_type: str = "UnknownError", error_details: Optional[str] = None):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.last_error = error_message
        error = TaskError(
            error_type=error_type,
            error_message=error_message,
            error_details=error_details
        )
        self.errors.append(error)
        if self.error_callback:
            self.error_callback(error)
    
    def cancel(self):
        """取消任务"""
        self.status = TaskStatus.CANCELLED
    
    def pause(self):
        """暂停任务"""
        self.status = TaskStatus.PAUSED
    
    def resume(self):
        """恢复任务"""
        if self.status == TaskStatus.PAUSED:
            self.status = TaskStatus.RUNNING
    
    def update_progress(self, completed: Optional[int] = None, failed: Optional[int] = None, 
                       skipped: Optional[int] = None, total: Optional[int] = None):
        """更新进度"""
        if total is not None:
            self.progress.total = total
        if completed is not None:
            self.progress.completed = completed
        if failed is not None:
            self.progress.failed = failed
        if skipped is not None:
            self.progress.skipped = skipped
        
        if self.progress_callback:
            self.progress_callback(self.progress)
    
    def add_result(self, product_id: str):
        """添加结果"""
        if product_id not in self.results:
            self.results.append(product_id)
    
    def get_duration(self) -> Optional[float]:
        """获取任务持续时间（秒）"""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.now()
        return (end_time - self.started_at).total_seconds()
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    def estimate_completion_time(self) -> Optional[datetime]:
        """估算完成时间"""
        if not self.started_at or self.progress.completed == 0:
            return None
        
        elapsed = (datetime.now() - self.started_at).total_seconds()
        rate = self.progress.completed / elapsed  # 每秒完成数
        
        if rate > 0 and self.progress.remaining > 0:
            remaining_seconds = self.progress.remaining / rate
            self.estimated_completion = datetime.now() + datetime.timedelta(seconds=remaining_seconds)
            return self.estimated_completion
        
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "name": self.name,
            "description": self.description,
            "task_type": self.task_type.value,
            "target_url": self.target_url,
            "shop_id": self.shop_id,
            "shop_name": self.shop_name,
            "search_keywords": self.search_keywords,
            "category": self.category,
            "max_products": self.max_products,
            "min_sales": self.min_sales,
            "max_price": self.max_price,
            "min_price": self.min_price,
            "include_variants": self.include_variants,
            "include_images": self.include_images,
            "include_reviews": self.include_reviews,
            "filter_conditions": self.filter_conditions,
            "crawler_mode": self.crawler_mode,
            "status": self.status.value,
            "progress": self.progress.to_dict(),
            "results": self.results,
            "output_file": self.output_file,
            "export_format": self.export_format,
            "errors": [error.to_dict() for error in self.errors],
            "last_error": self.last_error,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "estimated_completion": self.estimated_completion.isoformat() if self.estimated_completion else None,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "average_response_time": self.average_response_time,
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScrapingTask':
        """从字典创建ScrapingTask实例"""
        # 处理进度信息
        progress = TaskProgress()
        if "progress" in data and data["progress"]:
            progress_data = data["progress"].copy()
            # 移除计算属性，这些不应该传递给构造函数
            progress_data.pop('percentage', None)
            progress_data.pop('remaining', None)
            progress = TaskProgress(**progress_data)
        
        # 处理错误信息
        errors = []
        if "errors" in data and data["errors"]:
            for error_data in data["errors"]:
                if "occurred_at" in error_data and isinstance(error_data["occurred_at"], str):
                    error_data["occurred_at"] = datetime.fromisoformat(error_data["occurred_at"])
                errors.append(TaskError(**error_data))
        
        # 处理时间字段
        time_fields = ["created_at", "started_at", "completed_at", "estimated_completion"]
        for field_name in time_fields:
            if field_name in data and data[field_name] and isinstance(data[field_name], str):
                data[field_name] = datetime.fromisoformat(data[field_name])
        
        # 处理枚举字段
        if "task_type" in data:
            data["task_type"] = TaskType(data["task_type"])
        if "status" in data:
            data["status"] = TaskStatus(data["status"])
        
        # 设置处理后的数据
        data["progress"] = progress
        data["errors"] = errors
        
        return cls(**data)

    def is_pending(self) -> bool:
        """检查任务是否为待处理状态"""
        return self.status == TaskStatus.PENDING

    def is_running(self) -> bool:
        """检查任务是否正在运行"""
        return self.status == TaskStatus.RUNNING

    def is_completed(self) -> bool:
        """检查任务是否已完成"""
        return self.status == TaskStatus.COMPLETED

    def is_failed(self) -> bool:
        """检查任务是否失败"""
        return self.status == TaskStatus.FAILED

    def is_cancelled(self) -> bool:
        """检查任务是否被取消"""
        return self.status == TaskStatus.CANCELLED

    def is_paused(self) -> bool:
        """检查任务是否暂停"""
        return self.status == TaskStatus.PAUSED
