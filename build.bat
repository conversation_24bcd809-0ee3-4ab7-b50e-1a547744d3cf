@echo off
chcp 65001 >nul
echo ========================================
echo TikTok Shop Tool - 可执行文件构建脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.8或更高版本
    pause
    exit /b 1
)

:: 显示Python版本
echo 🐍 Python版本:
python --version

:: 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip不可用
    pause
    exit /b 1
)

echo.
echo 📦 开始构建过程...
echo.

:: 升级pip
echo 🔄 升级pip...
python -m pip install --upgrade pip

:: 安装/升级PyInstaller
echo 🔄 安装/升级PyInstaller...
pip install --upgrade pyinstaller

:: 安装项目依赖
echo 🔄 安装项目依赖...
pip install -r requirements.txt

:: 运行构建脚本
echo 🚀 开始构建可执行文件...
python build_executable.py

:: 检查构建结果
if errorlevel 1 (
    echo.
    echo ❌ 构建失败！
    echo 请检查上面的错误信息
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 构建成功！
    echo 可执行文件位于 dist 目录
    echo 发布包位于 release 目录
)

echo.
echo 按任意键退出...
pause >nul
