"""
URL工具模块 - 提供共享的URL解析和处理功能
这是唯一允许在DirectCrawler和SeleniumCrawler之间共享的模块
"""

import re
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from loguru import logger


class URLUtils:
    """URL工具类 - 提供URL解析和处理的共享功能"""
    
    @staticmethod
    def extract_product_id(url: str) -> Optional[str]:
        """从URL中提取product_id"""
        try:
            # PDP URL格式: https://www.tiktok.com/shop/pdp/product-name/product-id
            pdp_pattern = r'/shop/pdp/[^/]+/(\d+)'
            pdp_match = re.search(pdp_pattern, url)
            if pdp_match:
                return pdp_match.group(1)
            
            # Desktop Shop URL格式: https://shop.tiktok.com/view/product/product-id
            desktop_pattern = r'/view/product/(\d+)'
            desktop_match = re.search(desktop_pattern, url)
            if desktop_match:
                return desktop_match.group(1)
            
            # 从查询参数中提取
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            if 'product_id' in query_params:
                return query_params['product_id'][0]
            
            return None
            
        except Exception as e:
            logger.debug(f"提取product_id失败: {url} - {str(e)}")
            return None
    
    @staticmethod
    def extract_shop_id(url: str) -> Optional[str]:
        """从URL中提取shop_id"""
        try:
            # Shop URL格式: https://www.tiktok.com/shop/store/shop-name/shop-id
            shop_pattern = r'/shop/store/[^/]+/(\d+)'
            shop_match = re.search(shop_pattern, url)
            if shop_match:
                return shop_match.group(1)
            
            # 从查询参数中提取
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            if 'shop_id' in query_params:
                return query_params['shop_id'][0]
            
            return None
            
        except Exception as e:
            logger.debug(f"提取shop_id失败: {url} - {str(e)}")
            return None
    
    @staticmethod
    def extract_seller_id(url: str) -> Optional[str]:
        """从URL中提取seller_id"""
        try:
            # 从查询参数中提取
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            if 'seller_id' in query_params:
                return query_params['seller_id'][0]
            
            return None
            
        except Exception as e:
            logger.debug(f"提取seller_id失败: {url} - {str(e)}")
            return None
    
    @staticmethod
    def is_pdp_url(url: str) -> bool:
        """判断是否为PDP页面URL"""
        try:
            url_lower = url.lower()
            return '/shop/pdp/' in url_lower or '/view/product/' in url_lower
        except:
            return False
    
    @staticmethod
    def is_shop_url(url: str) -> bool:
        """判断是否为店铺页面URL"""
        try:
            url_lower = url.lower()
            return '/shop/store/' in url_lower
        except:
            return False
    
    @staticmethod
    def normalize_url(url: str) -> str:
        """标准化URL格式"""
        try:
            # 移除多余的空格和换行符
            url = url.strip()
            
            # 确保URL有协议
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # 移除URL末尾的斜杠
            url = url.rstrip('/')
            
            return url
            
        except Exception as e:
            logger.debug(f"URL标准化失败: {url} - {str(e)}")
            return url
    
    @staticmethod
    def clean_url_params(url: str, keep_params: list = None) -> str:
        """清理URL参数，只保留指定的参数"""
        try:
            if keep_params is None:
                keep_params = ['product_id', 'shop_id', 'seller_id']
            
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            
            # 只保留指定的参数
            cleaned_params = {k: v for k, v in query_params.items() if k in keep_params}
            
            # 重构URL
            from urllib.parse import urlencode, urlunparse
            new_query = urlencode(cleaned_params, doseq=True)
            cleaned_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path,
                parsed.params,
                new_query,
                ''  # 移除fragment
            ))
            
            return cleaned_url
            
        except Exception as e:
            logger.debug(f"URL参数清理失败: {url} - {str(e)}")
            return url
    
    @staticmethod
    def get_domain(url: str) -> Optional[str]:
        """获取URL的域名"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except Exception as e:
            logger.debug(f"获取域名失败: {url} - {str(e)}")
            return None
    
    @staticmethod
    def is_tiktok_domain(url: str) -> bool:
        """判断是否为TikTok相关域名"""
        try:
            domain = URLUtils.get_domain(url)
            if not domain:
                return False
            
            tiktok_domains = [
                'tiktok.com',
                'shop.tiktok.com',
                'www.tiktok.com',
                'm.tiktok.com'
            ]
            
            return any(tiktok_domain in domain for tiktok_domain in tiktok_domains)
            
        except Exception as e:
            logger.debug(f"域名检查失败: {url} - {str(e)}")
            return False
    
    @staticmethod
    def build_api_url(base_url: str, endpoint: str, params: Dict[str, Any] = None) -> str:
        """构建API请求URL"""
        try:
            # 确保base_url以斜杠结尾
            if not base_url.endswith('/'):
                base_url += '/'
            
            # 移除endpoint开头的斜杠
            if endpoint.startswith('/'):
                endpoint = endpoint[1:]
            
            # 组合URL
            api_url = base_url + endpoint
            
            # 添加查询参数
            if params:
                from urllib.parse import urlencode
                query_string = urlencode(params)
                api_url += '?' + query_string
            
            return api_url
            
        except Exception as e:
            logger.debug(f"构建API URL失败: {base_url}, {endpoint} - {str(e)}")
            return base_url
    
    @staticmethod
    def extract_share_code(url: str) -> Optional[str]:
        """从移动分享链接中提取分享码"""
        try:
            # 移动分享链接格式: https://www.tiktok.com/t/share-code/
            share_pattern = r'/t/([A-Za-z0-9]+)/?'
            share_match = re.search(share_pattern, url)
            if share_match:
                return share_match.group(1)
            
            return None
            
        except Exception as e:
            logger.debug(f"提取分享码失败: {url} - {str(e)}")
            return None


class URLValidator:
    """URL验证器"""
    
    @staticmethod
    def is_valid_tiktok_url(url: str) -> bool:
        """验证是否为有效的TikTok URL"""
        try:
            # 基本格式检查
            if not url or not isinstance(url, str):
                return False
            
            # 域名检查
            if not URLUtils.is_tiktok_domain(url):
                return False
            
            # URL格式检查
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"URL验证失败: {url} - {str(e)}")
            return False
    
    @staticmethod
    def is_valid_product_url(url: str) -> bool:
        """验证是否为有效的产品URL"""
        try:
            if not URLValidator.is_valid_tiktok_url(url):
                return False
            
            # 检查是否包含产品相关路径
            return URLUtils.is_pdp_url(url) or URLUtils.extract_product_id(url) is not None
            
        except Exception as e:
            logger.debug(f"产品URL验证失败: {url} - {str(e)}")
            return False
    
    @staticmethod
    def is_valid_shop_url(url: str) -> bool:
        """验证是否为有效的店铺URL"""
        try:
            if not URLValidator.is_valid_tiktok_url(url):
                return False
            
            # 检查是否包含店铺相关路径
            return URLUtils.is_shop_url(url) or URLUtils.extract_shop_id(url) is not None
            
        except Exception as e:
            logger.debug(f"店铺URL验证失败: {url} - {str(e)}")
            return False
