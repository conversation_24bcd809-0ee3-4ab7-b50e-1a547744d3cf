# TikTok Shop爬虫配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 日志配置
# ================================
# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=ERROR

# ================================
# 基础爬虫配置
# ================================
# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# 基础请求延迟（秒）
REQUEST_DELAY=2.0

# 最大重试次数
MAX_RETRIES=3

# ================================
# 并发优化配置
# ================================
# 最大并发请求数（建议值：5-12，根据网络环境调整）
MAX_CONCURRENT=8

# 信号量限制（通常与MAX_CONCURRENT相同）
SEMAPHORE_LIMIT=8

# 最小请求间隔（秒，避免请求过于频繁）
MIN_REQUEST_INTERVAL=0.5

# 最大请求间隔（秒，保持合理的爬取速度）
MAX_REQUEST_INTERVAL=2.0

# ================================
# 反爬策略配置
# ================================
# 启用随机延迟（true/false）
ENABLE_RANDOM_DELAY=true

# 启用User-Agent轮换（true/false）
ENABLE_USER_AGENT_ROTATION=true

# 启用请求头随机化（true/false）
ENABLE_HEADER_RANDOMIZATION=true

# ================================
# 错误处理配置
# ================================
# 失败率阈值（0.0-1.0，超过此值触发熔断器）
FAILURE_THRESHOLD=0.3

# 熔断器超时时间（秒）
CIRCUIT_BREAKER_TIMEOUT=60

# 重试基础延迟（秒）
RETRY_DELAY_BASE=1.0

# 最大重试延迟（秒）
RETRY_DELAY_MAX=10.0

# ================================
# 代理配置（可选）
# ================================
# 启用代理池（true/false）
ENABLE_PROXY=false

# 代理选择策略（round_robin/best/random）
PROXY_SELECTION_STRATEGY=round_robin

# ================================
# GUI配置
# ================================
# 窗口主题（light/dark）
GUI_THEME=light

# 窗口语言（zh_CN/en_US）
GUI_LANGUAGE=zh_CN

# ================================
# 导出配置
# ================================
# 默认导出格式（xlsx/csv）
EXPORT_FORMAT=xlsx

# 是否包含图片（true/false）
EXPORT_INCLUDE_IMAGES=true

# ================================
# 性能调优建议
# ================================
# 网络环境良好时的推荐配置：
# MAX_CONCURRENT=10
# MIN_REQUEST_INTERVAL=0.3
# MAX_REQUEST_INTERVAL=1.5

# 网络环境一般时的推荐配置：
# MAX_CONCURRENT=6
# MIN_REQUEST_INTERVAL=0.8
# MAX_REQUEST_INTERVAL=2.5

# 反爬严格时的推荐配置：
# MAX_CONCURRENT=4
# MIN_REQUEST_INTERVAL=2.0
# MAX_REQUEST_INTERVAL=5.0
# FAILURE_THRESHOLD=0.2

# 稳定性优先的推荐配置：
# MAX_CONCURRENT=5
# MIN_REQUEST_INTERVAL=1.0
# MAX_REQUEST_INTERVAL=3.0
# FAILURE_THRESHOLD=0.2
# CIRCUIT_BREAKER_TIMEOUT=120
