"""
并发请求管理器
实现智能的并发控制、反爬策略和错误处理
"""

import asyncio
import random
import time
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from loguru import logger
import aiohttp
from aiohttp import ClientTimeout, ClientSession
from ..config.settings import config


@dataclass
class RequestResult:
    """请求结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    response_time: float = 0.0
    retry_count: int = 0


class AntiCrawlManager:
    """反爬策略管理器"""
    
    def __init__(self):
        self.user_agents = config.crawler.user_agents.copy()
        self.request_count = 0
        self.last_request_time = 0
        
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)
    
    def get_randomized_headers(self, base_headers: Dict[str, str]) -> Dict[str, str]:
        """获取随机化的请求头"""
        headers = base_headers.copy()
        
        if config.crawler.enable_user_agent_rotation:
            headers['User-Agent'] = self.get_random_user_agent()
        
        if config.crawler.enable_header_randomization:
            # 随机化一些请求头
            accept_languages = [
                "en-US,en;q=0.9",
                "en-US,en;q=0.8,zh-CN;q=0.7,zh;q=0.6",
                "en-US,en;q=0.5",
                "zh-CN,zh;q=0.9,en;q=0.8"
            ]
            headers['Accept-Language'] = random.choice(accept_languages)
            
            # 随机添加一些可选头
            if random.random() > 0.5:
                headers['Cache-Control'] = random.choice(['no-cache', 'max-age=0'])
            
            if random.random() > 0.7:
                headers['Pragma'] = 'no-cache'
        
        return headers
    
    async def apply_request_delay(self):
        """应用请求延迟"""
        current_time = time.time()
        
        if config.crawler.enable_random_delay:
            # 随机延迟
            delay = random.uniform(
                config.crawler.min_request_interval,
                config.crawler.max_request_interval
            )
        else:
            delay = config.crawler.request_delay
        
        # 确保最小间隔
        if self.last_request_time > 0:
            elapsed = current_time - self.last_request_time
            if elapsed < delay:
                await asyncio.sleep(delay - elapsed)
        
        self.last_request_time = time.time()
        self.request_count += 1


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: float = 0.3, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def record_success(self):
        """记录成功"""
        self.success_count += 1
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            self.failure_count = 0
    
    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        total_requests = self.success_count + self.failure_count
        if total_requests > 10:  # 至少10个请求后才判断
            failure_rate = self.failure_count / total_requests
            if failure_rate > self.failure_threshold:
                self.state = "OPEN"
                logger.warning(f"🔥 [熔断器] 熔断器开启，失败率: {failure_rate:.2%}")
    
    def can_request(self) -> bool:
        """是否可以请求"""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
                logger.info("🔄 [熔断器] 熔断器半开状态")
                return True
            return False
        else:  # HALF_OPEN
            return True


class ConcurrentRequestManager:
    """并发请求管理器"""
    
    def __init__(self):
        self.semaphore = asyncio.Semaphore(config.crawler.semaphore_limit)
        self.anti_crawl = AntiCrawlManager()
        self.circuit_breaker = CircuitBreaker(
            config.crawler.failure_threshold,
            config.crawler.circuit_breaker_timeout
        )
        self.session: Optional[ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        timeout = ClientTimeout(total=config.crawler.timeout)
        connector = aiohttp.TCPConnector(
            limit=config.crawler.max_concurrent,
            limit_per_host=config.crawler.max_concurrent,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        self.session = ClientSession(
            timeout=timeout,
            connector=connector,
            headers=config.crawler.headers
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def make_request(
        self,
        url: str,
        method: str = 'GET',
        **kwargs
    ) -> RequestResult:
        """发起单个请求"""
        if not self.circuit_breaker.can_request():
            return RequestResult(
                success=False,
                error="Circuit breaker is open",
                status_code=503
            )
        
        async with self.semaphore:
            # 应用反爬延迟
            await self.anti_crawl.apply_request_delay()
            
            start_time = time.time()
            retry_count = 0
            
            for attempt in range(config.crawler.max_retries + 1):
                try:
                    # 获取随机化的请求头
                    headers = self.anti_crawl.get_randomized_headers(
                        kwargs.get('headers', {})
                    )
                    kwargs['headers'] = headers
                    
                    logger.debug(f"🌐 [请求] {method} {url} (尝试 {attempt + 1})")
                    
                    async with self.session.request(method, url, **kwargs) as response:
                        response_time = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.text()
                            self.circuit_breaker.record_success()
                            
                            return RequestResult(
                                success=True,
                                data=data,
                                status_code=response.status,
                                response_time=response_time,
                                retry_count=retry_count
                            )
                        else:
                            logger.warning(f"⚠️ [请求] HTTP {response.status}: {url}")
                            
                            if response.status in [429, 503, 502, 504]:
                                # 可重试的错误
                                retry_count += 1
                                if attempt < config.crawler.max_retries:
                                    delay = min(
                                        config.crawler.retry_delay_base * (2 ** attempt),
                                        config.crawler.retry_delay_max
                                    )
                                    logger.info(f"🔄 [重试] 等待 {delay:.1f}s 后重试")
                                    await asyncio.sleep(delay)
                                    continue
                            
                            self.circuit_breaker.record_failure()
                            return RequestResult(
                                success=False,
                                error=f"HTTP {response.status}",
                                status_code=response.status,
                                response_time=response_time,
                                retry_count=retry_count
                            )
                
                except asyncio.TimeoutError:
                    retry_count += 1
                    logger.warning(f"⏰ [超时] 请求超时: {url}")
                    if attempt < config.crawler.max_retries:
                        await asyncio.sleep(config.crawler.retry_delay_base)
                        continue
                    
                    self.circuit_breaker.record_failure()
                    return RequestResult(
                        success=False,
                        error="Request timeout",
                        response_time=time.time() - start_time,
                        retry_count=retry_count
                    )
                
                except Exception as e:
                    retry_count += 1
                    logger.error(f"❌ [错误] 请求异常: {url} - {str(e)}")
                    if attempt < config.crawler.max_retries:
                        await asyncio.sleep(config.crawler.retry_delay_base)
                        continue
                    
                    self.circuit_breaker.record_failure()
                    return RequestResult(
                        success=False,
                        error=str(e),
                        response_time=time.time() - start_time,
                        retry_count=retry_count
                    )
            
            # 所有重试都失败了
            self.circuit_breaker.record_failure()
            return RequestResult(
                success=False,
                error="Max retries exceeded",
                response_time=time.time() - start_time,
                retry_count=retry_count
            )
    
    async def batch_request(
        self,
        urls: List[str],
        request_func: Callable[[str], Any],
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[Tuple[str, RequestResult]]:
        """批量并发请求"""
        logger.info(f"🚀 [并发] 开始批量请求，共 {len(urls)} 个URL")
        
        tasks = []
        for url in urls:
            task = asyncio.create_task(self._process_single_request(url, request_func))
            tasks.append(task)
        
        results = []
        completed = 0
        
        for coro in asyncio.as_completed(tasks):
            result = await coro
            results.append(result)
            completed += 1
            
            if progress_callback:
                progress_callback(completed, len(urls))
            
            logger.info(f"📊 [进度] {completed}/{len(urls)} 完成")
        
        # 统计结果
        success_count = sum(1 for _, result in results if result.success)
        failure_count = len(results) - success_count
        
        logger.info(f"✅ [完成] 批量请求完成: 成功 {success_count}, 失败 {failure_count}")
        
        return results
    
    async def _process_single_request(
        self,
        url: str,
        request_func: Callable[[str], Any]
    ) -> Tuple[str, RequestResult]:
        """处理单个请求"""
        try:
            result = await request_func(url)
            return url, result
        except Exception as e:
            logger.error(f"❌ [处理] 请求处理异常: {url} - {str(e)}")
            return url, RequestResult(
                success=False,
                error=str(e)
            )
