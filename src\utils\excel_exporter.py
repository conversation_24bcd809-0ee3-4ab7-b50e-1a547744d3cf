"""
Excel导出工具
"""

import os
import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.drawing.image import Image as OpenpyxlImage
import requests
from loguru import logger

from ..models.product import Product
from ..models.shop import Shop
from ..config.settings import config


class ExcelExporter:
    """Excel导出器"""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.output_dir = output_dir or config.export.output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 样式定义
        self.header_font = Font(bold=True, color="FFFFFF")
        self.header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        self.header_alignment = Alignment(horizontal="center", vertical="center")
        
        self.data_alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
        self.number_alignment = Alignment(horizontal="right", vertical="center")
        
        self.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
    
    def export_products_to_excel(self, products: List[Product], filename: Optional[str] = None) -> str:
        """导出商品数据到Excel"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tiktok_shop_products_{timestamp}.xlsx"
            
            file_path = self.output_dir / filename
            
            # 创建工作簿
            workbook = openpyxl.Workbook()
            
            # 删除默认工作表
            workbook.remove(workbook.active)
            
            # 按店铺分组
            shop_groups = {}
            for product in products:
                shop_name = product.shop_name or "未知店铺"
                if shop_name not in shop_groups:
                    shop_groups[shop_name] = []
                shop_groups[shop_name].append(product)
            
            # 为每个店铺创建工作表
            for shop_name, shop_products in shop_groups.items():
                # 限制工作表名称长度
                sheet_name = shop_name[:30] if len(shop_name) > 30 else shop_name
                sheet_name = self._sanitize_sheet_name(sheet_name)
                
                worksheet = workbook.create_sheet(title=sheet_name)
                self._write_products_to_sheet(worksheet, shop_products)
            
            # 创建汇总工作表
            summary_sheet = workbook.create_sheet(title="汇总", index=0)
            self._write_summary_to_sheet(summary_sheet, products, shop_groups)
            
            # 保存文件
            workbook.save(file_path)
            
            logger.info(f"商品数据已导出到: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"导出Excel失败: {str(e)}")
            raise
    
    def _write_products_to_sheet(self, worksheet, products: List[Product]):
        """将商品数据写入工作表"""
        # 定义列标题
        headers = [
            "商品ID", "商品名称", "商品描述", "分类", "子分类", "品牌",
            "价格", "原价", "货币", "折扣百分比", "库存", "销量", "月销量",
            "主图链接", "评分", "评论数", "店铺ID", "店铺名称", "商品链接",
            "状态", "标签", "创建时间", "更新时间", "爬取时间"
        ]
        
        # 写入标题行
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.header_alignment
            cell.border = self.border
        
        # 写入数据行
        for row, product in enumerate(products, 2):
            data = [
                product.product_id,
                product.title,
                product.description,
                product.category,
                product.subcategory,
                product.brand,
                float(product.price),
                float(product.original_price) if product.original_price else None,
                product.currency.value,
                product.discount_percentage,
                product.stock,
                product.sold_count,
                product.monthly_sales,
                product.main_image_url,
                product.rating.average_rating,
                product.rating.total_reviews,
                product.shop_id,
                product.shop_name,
                product.product_url,
                product.status.value,
                ", ".join(product.tags),
                product.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                product.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                product.scraped_at.strftime("%Y-%m-%d %H:%M:%S")
            ]
            
            for col, value in enumerate(data, 1):
                cell = worksheet.cell(row=row, column=col, value=value)
                cell.border = self.border
                
                # 设置对齐方式
                if col in [7, 8, 10, 11, 12, 13, 15, 16]:  # 数字列
                    cell.alignment = self.number_alignment
                else:
                    cell.alignment = self.data_alignment
        
        # 调整列宽
        self._adjust_column_widths(worksheet)
        
        # 冻结首行
        worksheet.freeze_panes = "A2"
    
    def _write_summary_to_sheet(self, worksheet, products: List[Product], shop_groups: Dict[str, List[Product]]):
        """写入汇总信息到工作表"""
        # 标题
        worksheet.cell(row=1, column=1, value="TikTok Shop 商品数据汇总报告").font = Font(size=16, bold=True)
        worksheet.merge_cells("A1:D1")
        
        # 基本统计
        row = 3
        stats = [
            ("总商品数", len(products)),
            ("总店铺数", len(shop_groups)),
            ("平均价格", sum(float(p.price) for p in products) / len(products) if products else 0),
            ("总销量", sum(p.sold_count for p in products)),
            ("导出时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        ]
        
        for label, value in stats:
            worksheet.cell(row=row, column=1, value=label).font = Font(bold=True)
            worksheet.cell(row=row, column=2, value=value)
            row += 1
        
        # 店铺统计
        row += 2
        worksheet.cell(row=row, column=1, value="店铺统计").font = Font(size=14, bold=True)
        row += 1
        
        # 店铺统计表头
        shop_headers = ["店铺名称", "商品数量", "平均价格", "总销量"]
        for col, header in enumerate(shop_headers, 1):
            cell = worksheet.cell(row=row, column=col, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.header_alignment
            cell.border = self.border
        row += 1
        
        # 店铺统计数据
        for shop_name, shop_products in shop_groups.items():
            avg_price = sum(float(p.price) for p in shop_products) / len(shop_products)
            total_sales = sum(p.sold_count for p in shop_products)
            
            data = [shop_name, len(shop_products), f"{avg_price:.2f}", total_sales]
            for col, value in enumerate(data, 1):
                cell = worksheet.cell(row=row, column=col, value=value)
                cell.border = self.border
                if col in [2, 4]:  # 数字列
                    cell.alignment = self.number_alignment
                else:
                    cell.alignment = self.data_alignment
            row += 1
        
        # 调整列宽
        self._adjust_column_widths(worksheet)
    
    def export_products_to_csv(self, products: List[Product], filename: Optional[str] = None) -> str:
        """导出商品数据到CSV"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tiktok_shop_products_{timestamp}.csv"
            
            file_path = self.output_dir / filename
            
            # 转换为DataFrame
            data = []
            for product in products:
                data.append({
                    "商品ID": product.product_id,
                    "商品名称": product.title,
                    "商品描述": product.description,
                    "分类": product.category,
                    "子分类": product.subcategory,
                    "品牌": product.brand,
                    "价格": float(product.price),
                    "原价": float(product.original_price) if product.original_price else None,
                    "货币": product.currency.value,
                    "折扣百分比": product.discount_percentage,
                    "库存": product.stock,
                    "销量": product.sold_count,
                    "月销量": product.monthly_sales,
                    "主图链接": product.main_image_url,
                    "评分": product.rating.average_rating,
                    "评论数": product.rating.total_reviews,
                    "店铺ID": product.shop_id,
                    "店铺名称": product.shop_name,
                    "商品链接": product.product_url,
                    "状态": product.status.value,
                    "标签": ", ".join(product.tags),
                    "创建时间": product.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "更新时间": product.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "爬取时间": product.scraped_at.strftime("%Y-%m-%d %H:%M:%S")
                })
            
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            
            logger.info(f"商品数据已导出到: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"导出CSV失败: {str(e)}")
            raise
    
    def export_products_to_json(self, products: List[Product], filename: Optional[str] = None) -> str:
        """导出商品数据到JSON"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tiktok_shop_products_{timestamp}.json"
            
            file_path = self.output_dir / filename
            
            # 转换为字典列表
            data = {
                "export_info": {
                    "export_time": datetime.now().isoformat(),
                    "total_products": len(products),
                    "exporter_version": "1.0.0"
                },
                "products": [product.to_dict() for product in products]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"商品数据已导出到: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"导出JSON失败: {str(e)}")
            raise
    
    def _sanitize_sheet_name(self, name: str) -> str:
        """清理工作表名称"""
        # Excel工作表名称不能包含的字符
        invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
        for char in invalid_chars:
            name = name.replace(char, '_')
        return name
    
    def _adjust_column_widths(self, worksheet):
        """调整列宽"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            # 设置最小和最大宽度
            adjusted_width = min(max(max_length + 2, 10), 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def download_and_embed_images(self, products: List[Product], worksheet, image_column: int):
        """下载并嵌入商品图片"""
        try:
            image_dir = self.output_dir / "images"
            image_dir.mkdir(exist_ok=True)
            
            for row, product in enumerate(products, 2):
                if product.main_image_url:
                    try:
                        # 下载图片
                        response = requests.get(
                            product.main_image_url, 
                            timeout=config.export.image_download_timeout
                        )
                        response.raise_for_status()
                        
                        # 保存图片
                        image_filename = f"{product.product_id}.jpg"
                        image_path = image_dir / image_filename
                        
                        with open(image_path, 'wb') as f:
                            f.write(response.content)
                        
                        # 插入图片到Excel
                        img = OpenpyxlImage(str(image_path))
                        img.width = 100
                        img.height = 100
                        
                        cell = worksheet.cell(row=row, column=image_column)
                        worksheet.add_image(img, cell.coordinate)
                        worksheet.row_dimensions[row].height = 75
                        
                    except Exception as e:
                        logger.warning(f"下载图片失败 {product.main_image_url}: {str(e)}")
                        continue
            
        except Exception as e:
            logger.error(f"嵌入图片失败: {str(e)}")


def export_products(products: List[Product], format_type: str = "xlsx", 
                   filename: Optional[str] = None, output_dir: Optional[Path] = None) -> str:
    """导出商品数据的便捷函数"""
    exporter = ExcelExporter(output_dir)
    
    if format_type.lower() == "xlsx":
        return exporter.export_products_to_excel(products, filename)
    elif format_type.lower() == "csv":
        return exporter.export_products_to_csv(products, filename)
    elif format_type.lower() == "json":
        return exporter.export_products_to_json(products, filename)
    else:
        raise ValueError(f"不支持的导出格式: {format_type}")
