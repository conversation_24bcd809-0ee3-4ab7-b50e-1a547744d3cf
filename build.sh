#!/bin/bash

# TikTok Shop Tool - 可执行文件构建脚本 (Linux/macOS)

set -e  # 遇到错误立即退出

echo "========================================"
echo "TikTok Shop Tool - 可执行文件构建脚本"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Python未安装${NC}"
        echo "请安装Python 3.8或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 显示Python版本
echo -e "${BLUE}🐍 Python版本:${NC}"
$PYTHON_CMD --version

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo -e "${RED}❌ Python版本过低，需要Python 3.8或更高版本${NC}"
    echo "当前版本: $PYTHON_VERSION"
    exit 1
fi

# 检查pip是否可用
if ! $PYTHON_CMD -m pip --version &> /dev/null; then
    echo -e "${RED}❌ pip不可用${NC}"
    exit 1
fi

echo
echo -e "${BLUE}📦 开始构建过程...${NC}"
echo

# 升级pip
echo -e "${YELLOW}🔄 升级pip...${NC}"
$PYTHON_CMD -m pip install --upgrade pip

# 安装/升级PyInstaller
echo -e "${YELLOW}🔄 安装/升级PyInstaller...${NC}"
$PYTHON_CMD -m pip install --upgrade pyinstaller

# 安装项目依赖
echo -e "${YELLOW}🔄 安装项目依赖...${NC}"
$PYTHON_CMD -m pip install -r requirements.txt

# 运行构建脚本
echo -e "${YELLOW}🚀 开始构建可执行文件...${NC}"
$PYTHON_CMD build_executable.py

# 检查构建结果
if [ $? -eq 0 ]; then
    echo
    echo -e "${GREEN}✅ 构建成功！${NC}"
    echo "可执行文件位于 dist 目录"
    echo "发布包位于 release 目录"
    
    # 显示文件信息
    if [ -f "dist/TikTokShopTool" ]; then
        echo
        echo -e "${BLUE}📁 构建结果:${NC}"
        ls -lh dist/TikTokShopTool
        
        # 设置执行权限
        chmod +x dist/TikTokShopTool
        echo -e "${GREEN}✅ 已设置执行权限${NC}"
    fi
    
else
    echo
    echo -e "${RED}❌ 构建失败！${NC}"
    echo "请检查上面的错误信息"
    exit 1
fi

echo
echo -e "${GREEN}🎉 构建完成！${NC}"
