"""
应用配置设置
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass, field

# 项目根目录（PyInstaller兼容）
def get_project_root():
    """获取项目根目录，兼容PyInstaller打包环境"""
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包环境
        return Path(sys._MEIPASS)
    else:
        # 开发环境
        return Path(__file__).parent.parent.parent

PROJECT_ROOT = get_project_root()

# 日志配置
@dataclass
class LogConfig:
    """日志配置"""
    level: str = "ERROR"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    rotation: str = "10 MB"
    retention: str = "30 days"
    log_dir: Path = field(default_factory=lambda: get_log_dir())

def get_log_dir():
    """获取日志目录，兼容PyInstaller打包环境"""
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包环境，使用临时目录
        import tempfile
        return Path(tempfile.gettempdir()) / "TikTokShopTool" / "logs"
    else:
        # 开发环境
        return PROJECT_ROOT / "logs"

# 爬虫配置
@dataclass
class CrawlerConfig:
    """爬虫配置"""
    # 基础配置（从环境变量读取，提供默认值）
    request_delay: float = field(default_factory=lambda: float(os.getenv("REQUEST_DELAY", "2.0")))
    timeout: int = field(default_factory=lambda: int(os.getenv("REQUEST_TIMEOUT", "30")))
    max_retries: int = field(default_factory=lambda: int(os.getenv("MAX_RETRIES", "3")))

    # 并发优化配置（从环境变量读取）
    max_concurrent: int = field(default_factory=lambda: int(os.getenv("MAX_CONCURRENT", "8")))
    semaphore_limit: int = field(default_factory=lambda: int(os.getenv("SEMAPHORE_LIMIT", "8")))
    min_request_interval: float = field(default_factory=lambda: float(os.getenv("MIN_REQUEST_INTERVAL", "0.5")))
    max_request_interval: float = field(default_factory=lambda: float(os.getenv("MAX_REQUEST_INTERVAL", "2.0")))

    # 反爬策略配置（从环境变量读取）
    enable_random_delay: bool = field(default_factory=lambda: os.getenv("ENABLE_RANDOM_DELAY", "true").lower() == "true")
    enable_user_agent_rotation: bool = field(default_factory=lambda: os.getenv("ENABLE_USER_AGENT_ROTATION", "true").lower() == "true")
    enable_header_randomization: bool = field(default_factory=lambda: os.getenv("ENABLE_HEADER_RANDOMIZATION", "true").lower() == "true")

    # 错误处理配置（从环境变量读取）
    failure_threshold: float = field(default_factory=lambda: float(os.getenv("FAILURE_THRESHOLD", "0.3")))
    circuit_breaker_timeout: int = field(default_factory=lambda: int(os.getenv("CIRCUIT_BREAKER_TIMEOUT", "60")))
    retry_delay_base: float = field(default_factory=lambda: float(os.getenv("RETRY_DELAY_BASE", "1.0")))
    retry_delay_max: float = field(default_factory=lambda: float(os.getenv("RETRY_DELAY_MAX", "10.0")))

    # 兼容性配置（保持向后兼容）
    random_delay_range: tuple = field(default_factory=lambda: (
        float(os.getenv("MIN_REQUEST_INTERVAL", "0.5")),
        float(os.getenv("MAX_REQUEST_INTERVAL", "2.0"))
    ))
    # User-Agent列表
    user_agents: List[str] = field(default_factory=lambda: [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ])
    # 请求头
    headers: Dict[str, str] = field(default_factory=lambda: {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Cache-Control": "max-age=0"
    })

# 加载更多配置
@dataclass
class LoadMoreConfig:
    """加载更多按钮处理配置"""
    # 内容增量检测配置
    initial_wait_time: int = 3  # 点击按钮后初始等待时间（秒）
    additional_wait_time: int = 2  # 额外等待时间（秒）
    max_retry_attempts: int = 3  # 最大重试检测次数
    min_content_increase: int = 1000  # 最小内容增量（字符数）
    content_increase_ratio: float = 0.05  # 内容增量比例（5%）

    # 加载控制配置
    max_load_clicks: int = 10  # 最大点击次数
    click_interval: int = 1  # 点击间隔时间（秒）

    # 销量检测配置
    enable_sales_detection: bool = True  # 是否启用销量检测
    stop_on_zero_sales: bool = True  # 遇到零销量商品时是否停止
    sales_detection_patterns: List[str] = field(default_factory=lambda: [
        r'sold_count[:\s]*0\b',  # "sold_count: 0" 或 "sold_count 0"
    ])

    # 调试配置
    enable_debug_logging: bool = False  # 是否启用详细调试日志
    save_content_snapshots: bool = False  # 是否保存内容快照用于调试

# GUI配置
@dataclass
class GUIConfig:
    """GUI配置"""
    window_title: str = "TikTok Shop 商品数据爬取工具"
    window_width: int = 1500
    window_height: int = 900
    min_width: int = 800
    min_height: int = 600
    theme: str = field(default_factory=lambda: os.getenv("GUI_THEME", "light"))
    language: str = field(default_factory=lambda: os.getenv("GUI_LANGUAGE", "zh_CN"))



def get_data_dir():
    """获取数据目录，兼容PyInstaller打包环境"""
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包环境，使用用户数据目录
        import tempfile
        return Path(tempfile.gettempdir()) / "TikTokShopTool" / "data"
    else:
        # 开发环境
        return PROJECT_ROOT / "data"

# 导出配置
@dataclass
class ExportConfig:
    """导出配置"""
    default_format: str = field(default_factory=lambda: os.getenv("EXPORT_FORMAT", "xlsx"))
    output_dir: Path = field(default_factory=lambda: get_export_dir())
    max_rows_per_sheet: int = 100000
    include_images: bool = field(default_factory=lambda: os.getenv("EXPORT_INCLUDE_IMAGES", "true").lower() == "true")
    image_download_timeout: int = 10

def get_export_dir():
    """获取导出目录，兼容PyInstaller打包环境"""
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包环境，使用用户文档目录
        import tempfile
        return Path(tempfile.gettempdir()) / "TikTokShopTool" / "exports"
    else:
        # 开发环境
        return PROJECT_ROOT / "exports"

# 应用配置
@dataclass
class AppConfig:
    """应用主配置"""
    log: LogConfig = field(default_factory=LogConfig)
    crawler: CrawlerConfig = field(default_factory=CrawlerConfig)
    load_more: LoadMoreConfig = field(default_factory=LoadMoreConfig)
    gui: GUIConfig = field(default_factory=GUIConfig)

    export: ExportConfig = field(default_factory=ExportConfig)
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保目录存在
        self.log.log_dir.mkdir(parents=True, exist_ok=True)
        self.export.output_dir.mkdir(parents=True, exist_ok=True)

# 全局配置实例
_config = None

def get_config():
    """获取配置实例，确保线程安全的单例模式"""
    global _config
    if _config is None:
        try:
            _config = AppConfig()
        except Exception as e:
            # 在无控制台环境中，print可能也会失败，所以使用try-except
            try:
                print(f"❌ 配置初始化失败: {e}")
            except:
                pass  # 忽略print失败
            # 创建最小化的备用配置
            import tempfile
            from dataclasses import dataclass, field

            @dataclass
            class FallbackLogConfig:
                level: str = "ERROR"
                format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
                rotation: str = "10 MB"
                retention: str = "30 days"
                log_dir: Path = field(default_factory=lambda: Path(tempfile.gettempdir()) / "TikTokShopTool_fallback" / "logs")

            @dataclass
            class FallbackConfig:
                log: FallbackLogConfig = field(default_factory=FallbackLogConfig)

            _config = FallbackConfig()
            try:
                print(f"✅ 使用备用配置: {_config}")
            except:
                pass  # 忽略print失败

    return _config

# 为了向后兼容，提供config变量
config = get_config()

# 环境变量配置
def load_env_config():
    """从环境变量加载配置"""
    from dotenv import load_dotenv

    env_file = PROJECT_ROOT / ".env"
    if env_file.exists():
        load_dotenv(env_file)

    # 从环境变量更新日志配置
    if os.getenv("LOG_LEVEL"):
        config.log.level = os.getenv("LOG_LEVEL")

    # 从环境变量更新爬虫配置
    if os.getenv("REQUEST_DELAY"):
        config.crawler.request_delay = float(os.getenv("REQUEST_DELAY"))

    if os.getenv("REQUEST_TIMEOUT"):
        config.crawler.timeout = int(os.getenv("REQUEST_TIMEOUT"))

    if os.getenv("MAX_RETRIES"):
        config.crawler.max_retries = int(os.getenv("MAX_RETRIES"))

    # 并发优化配置
    if os.getenv("MAX_CONCURRENT"):
        config.crawler.max_concurrent = int(os.getenv("MAX_CONCURRENT"))

    if os.getenv("SEMAPHORE_LIMIT"):
        config.crawler.semaphore_limit = int(os.getenv("SEMAPHORE_LIMIT"))

    if os.getenv("MIN_REQUEST_INTERVAL"):
        config.crawler.min_request_interval = float(os.getenv("MIN_REQUEST_INTERVAL"))

    if os.getenv("MAX_REQUEST_INTERVAL"):
        config.crawler.max_request_interval = float(os.getenv("MAX_REQUEST_INTERVAL"))
        # 同时更新random_delay_range以保持兼容性
        config.crawler.random_delay_range = (
            config.crawler.min_request_interval,
            config.crawler.max_request_interval
        )

    # 反爬策略配置
    if os.getenv("ENABLE_RANDOM_DELAY"):
        config.crawler.enable_random_delay = os.getenv("ENABLE_RANDOM_DELAY").lower() == "true"

    if os.getenv("ENABLE_USER_AGENT_ROTATION"):
        config.crawler.enable_user_agent_rotation = os.getenv("ENABLE_USER_AGENT_ROTATION").lower() == "true"

    if os.getenv("ENABLE_HEADER_RANDOMIZATION"):
        config.crawler.enable_header_randomization = os.getenv("ENABLE_HEADER_RANDOMIZATION").lower() == "true"

    # 错误处理配置
    if os.getenv("FAILURE_THRESHOLD"):
        config.crawler.failure_threshold = float(os.getenv("FAILURE_THRESHOLD"))

    if os.getenv("CIRCUIT_BREAKER_TIMEOUT"):
        config.crawler.circuit_breaker_timeout = int(os.getenv("CIRCUIT_BREAKER_TIMEOUT"))

    if os.getenv("RETRY_DELAY_BASE"):
        config.crawler.retry_delay_base = float(os.getenv("RETRY_DELAY_BASE"))

    if os.getenv("RETRY_DELAY_MAX"):
        config.crawler.retry_delay_max = float(os.getenv("RETRY_DELAY_MAX"))

    # GUI配置
    if os.getenv("GUI_THEME"):
        config.gui.theme = os.getenv("GUI_THEME")

    if os.getenv("GUI_LANGUAGE"):
        config.gui.language = os.getenv("GUI_LANGUAGE")

    # 导出配置
    if os.getenv("EXPORT_FORMAT"):
        config.export.default_format = os.getenv("EXPORT_FORMAT")

    if os.getenv("EXPORT_INCLUDE_IMAGES"):
        config.export.include_images = os.getenv("EXPORT_INCLUDE_IMAGES").lower() == "true"


def print_config_summary():
    """打印配置摘要"""
    print("🔧 TikTok Shop爬虫配置摘要")
    print("=" * 50)

    print("📊 并发配置:")
    print(f"   最大并发数: {config.crawler.max_concurrent}")
    print(f"   信号量限制: {config.crawler.semaphore_limit}")
    print(f"   请求间隔: {config.crawler.min_request_interval}s - {config.crawler.max_request_interval}s")

    print("\n🛡️ 反爬策略:")
    print(f"   随机延迟: {'启用' if config.crawler.enable_random_delay else '禁用'}")
    print(f"   UA轮换: {'启用' if config.crawler.enable_user_agent_rotation else '禁用'}")
    print(f"   请求头随机化: {'启用' if config.crawler.enable_header_randomization else '禁用'}")

    print("\n🔥 错误处理:")
    print(f"   失败阈值: {config.crawler.failure_threshold}")
    print(f"   熔断器超时: {config.crawler.circuit_breaker_timeout}s")
    print(f"   重试延迟: {config.crawler.retry_delay_base}s - {config.crawler.retry_delay_max}s")

    print("\n🎨 界面配置:")
    print(f"   主题: {config.gui.theme}")
    print(f"   语言: {config.gui.language}")

    print("\n📁 导出配置:")
    print(f"   格式: {config.export.default_format}")
    print(f"   包含图片: {'是' if config.export.include_images else '否'}")


def validate_config():
    """验证配置的有效性"""
    errors = []
    warnings = []

    # 验证并发配置
    if config.crawler.max_concurrent <= 0:
        errors.append("最大并发数必须大于0")
    elif config.crawler.max_concurrent > 20:
        warnings.append("最大并发数过高可能导致被封IP")

    if config.crawler.semaphore_limit != config.crawler.max_concurrent:
        warnings.append("建议信号量限制与最大并发数相同")

    if config.crawler.min_request_interval >= config.crawler.max_request_interval:
        errors.append("最小请求间隔必须小于最大请求间隔")

    if config.crawler.min_request_interval < 0.1:
        warnings.append("请求间隔过短可能触发反爬机制")

    # 验证错误处理配置
    if not (0.0 <= config.crawler.failure_threshold <= 1.0):
        errors.append("失败阈值必须在0.0-1.0之间")

    if config.crawler.circuit_breaker_timeout <= 0:
        errors.append("熔断器超时时间必须大于0")

    # 验证GUI配置
    if config.gui.theme not in ["light", "dark"]:
        warnings.append(f"未知的主题: {config.gui.theme}")

    if config.gui.language not in ["zh_CN", "en_US"]:
        warnings.append(f"未知的语言: {config.gui.language}")

    # 验证导出配置
    if config.export.default_format not in ["xlsx", "csv"]:
        warnings.append(f"未知的导出格式: {config.export.default_format}")

    return errors, warnings


def get_config_dict():
    """获取配置字典（用于GUI显示）"""
    return {
        "crawler": {
            "max_concurrent": config.crawler.max_concurrent,
            "semaphore_limit": config.crawler.semaphore_limit,
            "min_request_interval": config.crawler.min_request_interval,
            "max_request_interval": config.crawler.max_request_interval,
            "enable_random_delay": config.crawler.enable_random_delay,
            "enable_user_agent_rotation": config.crawler.enable_user_agent_rotation,
            "enable_header_randomization": config.crawler.enable_header_randomization,
            "failure_threshold": config.crawler.failure_threshold,
            "circuit_breaker_timeout": config.crawler.circuit_breaker_timeout,
            "retry_delay_base": config.crawler.retry_delay_base,
            "retry_delay_max": config.crawler.retry_delay_max,
        },
        "gui": {
            "theme": config.gui.theme,
            "language": config.gui.language,
        },
        "export": {
            "default_format": config.export.default_format,
            "include_images": config.export.include_images,
        }
    }

# TikTok Shop相关常量
class TikTokShopConstants:
    """TikTok Shop相关常量"""
    
    # 域名列表
    DOMAINS = [
        "shop.tiktok.com",
        "www.tiktok.com",
        "tiktok.com"
    ]
    
    # API端点
    API_ENDPOINTS = {
        "product_detail": "/api/v1/product/detail",
        "shop_products": "/api/v1/shop/products",
        "product_reviews": "/api/v1/product/reviews"
    }
    
    # 商品状态
    PRODUCT_STATUS = {
        "ACTIVE": "active",
        "INACTIVE": "inactive",
        "OUT_OF_STOCK": "out_of_stock"
    }
    
    # 支持的货币
    CURRENCIES = ["USD", "EUR", "GBP", "CAD", "AUD", "SGD", "MYR", "THB", "VND", "IDR", "PHP"]
    
    # 商品分类
    CATEGORIES = [
        "Fashion", "Beauty", "Electronics", "Home & Garden", "Sports & Outdoors",
        "Toys & Games", "Books", "Health & Personal Care", "Automotive", "Others"
    ]
