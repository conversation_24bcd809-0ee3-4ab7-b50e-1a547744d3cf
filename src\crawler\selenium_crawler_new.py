"""
Selenium浏览器爬虫 - 使用浏览器自动化获取数据
完全独立的实现，不依赖直连HTTP请求
"""

import asyncio
import time
import threading
from typing import Dict, Any, Optional, List, Callable
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

# Selenium相关导入（可选依赖）
try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.common.exceptions import TimeoutException, WebDriverException
    HAS_SELENIUM = True
except ImportError:
    logger.warning("⚠️ [Selenium] 未安装Selenium相关依赖，浏览器模式将不可用")
    HAS_SELENIUM = False
    uc = None

from .crawler_interface import (
    ICrawler, CrawlerResult, CrawlerStatus, CrawlerError, ErrorType,
    ProductData, ShopData, SeleniumCrawlerConfig,
    create_success_result, create_error_result
)
from .url_utils import URLUtils, URLValidator


class BrowserPool:
    """浏览器实例池管理器 - Selenium专用"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.pool = []
            self.max_pool_size = 3
            self.initialization_timeout = 60
            self.executor = ThreadPoolExecutor(max_workers=2)
            self.initialized = True
            logger.info("🏊 [浏览器池] 初始化完成")
    
    async def get_browser(self, headless: bool = False) -> Optional['uc.Chrome']:
        """获取浏览器实例"""
        try:
            # 检查池中是否有可用实例
            while self.pool:
                driver = self.pool.pop(0)
                if self._validate_browser_session(driver):
                    logger.info("♻️ [浏览器池] 复用现有浏览器实例")
                    return driver
                else:
                    logger.warning("⚠️ [浏览器池] 池中的浏览器会话已失效")
                    self._force_quit_browser(driver)
            
            # 创建新的浏览器实例
            logger.info("🆕 [浏览器池] 创建新的浏览器实例...")
            driver = await self._create_browser_instance(headless)
            
            if driver:
                logger.info("✅ [浏览器池] 浏览器实例创建成功")
                return driver
            else:
                logger.error("❌ [浏览器池] 浏览器实例创建失败")
                return None
                
        except Exception as e:
            logger.error(f"❌ [浏览器池] 获取浏览器实例失败: {str(e)}")
            return None
    
    async def _create_browser_instance(self, headless: bool = False) -> Optional['uc.Chrome']:
        """创建浏览器实例"""
        try:
            if not HAS_SELENIUM:
                raise ImportError("Selenium相关依赖未安装")
            
            # 配置Chrome选项
            options = uc.ChromeOptions()
            
            # 基础选项
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')
            options.add_argument('--disable-javascript')
            
            # 无头模式
            if headless:
                options.add_argument('--headless')
            
            # 窗口大小
            options.add_argument('--window-size=1920,1080')
            
            # 用户代理
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 创建浏览器实例
            driver = uc.Chrome(
                options=options,
                headless=headless,
                use_subprocess=True,
                debug=False
            )
            
            # 设置超时
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            return driver
            
        except Exception as e:
            logger.error(f"❌ [浏览器池] 创建浏览器实例失败: {str(e)}")
            return None
    
    def _validate_browser_session(self, driver: 'uc.Chrome') -> bool:
        """验证浏览器会话是否有效"""
        try:
            driver.current_url
            driver.execute_script("return document.readyState;")
            return True
        except Exception:
            return False
    
    def _force_quit_browser(self, driver: 'uc.Chrome'):
        """强制关闭浏览器实例"""
        try:
            if driver:
                driver.quit()
        except Exception as e:
            logger.debug(f"强制关闭浏览器异常: {str(e)}")
    
    def return_browser(self, driver: 'uc.Chrome'):
        """归还浏览器实例到池中"""
        try:
            if driver and len(self.pool) < self.max_pool_size:
                if self._validate_browser_session(driver):
                    # 清理浏览器状态
                    self._clean_browser_state(driver)
                    self.pool.append(driver)
                    logger.debug("♻️ [浏览器池] 浏览器实例已归还到池中")
                    return
            
            # 如果无法归还，直接关闭
            self._force_quit_browser(driver)
            
        except Exception as e:
            logger.debug(f"归还浏览器实例失败: {str(e)}")
            self._force_quit_browser(driver)
    
    def _clean_browser_state(self, driver: 'uc.Chrome'):
        """清理浏览器状态"""
        try:
            driver.delete_all_cookies()
            driver.execute_script("try { window.localStorage.clear(); } catch(e) {}")
            driver.execute_script("try { window.sessionStorage.clear(); } catch(e) {}")
            driver.get("about:blank")
        except Exception as e:
            logger.debug(f"清理浏览器状态失败: {str(e)}")
    
    def close_all(self):
        """关闭所有浏览器实例"""
        try:
            logger.info("🔒 [浏览器池] 开始关闭所有浏览器实例...")
            
            for driver in self.pool:
                self._force_quit_browser(driver)
            
            self.pool.clear()
            
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=True, timeout=5)
            
            logger.info("🔒 [浏览器池] 所有浏览器实例已关闭")
            
        except Exception as e:
            logger.error(f"❌ [浏览器池] 关闭浏览器池失败: {str(e)}")


class CaptchaDetectionResult:
    """验证码检测结果"""
    
    def __init__(self, has_captcha: bool = False, captcha_type: str = "", 
                 element_selector: str = "", confidence: float = 0.0):
        self.has_captcha = has_captcha
        self.captcha_type = captcha_type
        self.element_selector = element_selector
        self.confidence = confidence
        self.detected_at = time.time()


class SeleniumCrawler(ICrawler):
    """Selenium浏览器爬虫 - 独立实现"""
    
    def __init__(self, config: Optional[SeleniumCrawlerConfig] = None):
        """
        初始化Selenium爬虫
        
        Args:
            config: 爬虫配置
        """
        if not HAS_SELENIUM:
            raise ImportError("Selenium相关依赖未安装，请运行: pip install selenium undetected-chromedriver")
        
        self.config = config or SeleniumCrawlerConfig()
        self.status = CrawlerStatus.IDLE
        self.driver: Optional['uc.Chrome'] = None
        self.browser_pool = BrowserPool()
        self.user_interaction_callback: Optional[Callable] = None
        self.is_initialized = False
        
        # 统计信息
        self.stats = {
            'pages_loaded': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'captcha_detections': 0,
            'timeouts': 0,
            'start_time': None,
            'last_request_time': None
        }
        
        logger.info("🌐 [Selenium爬虫] 初始化完成")
    
    async def initialize(self) -> bool:
        """初始化爬虫"""
        try:
            if self.is_initialized:
                logger.warning("⚠️ [Selenium爬虫] 爬虫已初始化")
                return True
            
            self.status = CrawlerStatus.INITIALIZING
            logger.info("🚀 [Selenium爬虫] 开始初始化...")
            
            # 从浏览器池获取实例
            self.driver = await self.browser_pool.get_browser(self.config.headless)
            
            if not self.driver:
                logger.error("❌ [Selenium爬虫] 无法获取浏览器实例")
                self.status = CrawlerStatus.ERROR
                return False
            
            # 更新统计信息
            self.stats['start_time'] = time.time()
            self.is_initialized = True
            self.status = CrawlerStatus.RUNNING
            
            logger.info("✅ [Selenium爬虫] 初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ [Selenium爬虫] 初始化失败: {str(e)}")
            self.status = CrawlerStatus.ERROR
            return False
    
    async def close(self) -> None:
        """关闭爬虫，清理资源"""
        try:
            logger.info("🔒 [Selenium爬虫] 开始关闭...")
            
            if self.driver:
                self.browser_pool.return_browser(self.driver)
                self.driver = None
            
            self.is_initialized = False
            self.status = CrawlerStatus.CLOSED
            
            logger.info("✅ [Selenium爬虫] 关闭完成")
            
        except Exception as e:
            logger.error(f"❌ [Selenium爬虫] 关闭失败: {str(e)}")
    
    def get_status(self) -> CrawlerStatus:
        """获取爬虫当前状态"""
        return self.status
    
    def get_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        stats = self.stats.copy()
        if stats['start_time']:
            stats['uptime'] = time.time() - stats['start_time']
        return stats
    
    def set_user_interaction_callback(self, callback: Optional[Callable]) -> None:
        """设置用户交互回调函数"""
        self.user_interaction_callback = callback

    async def get_product_data(self, url: str) -> CrawlerResult:
        """获取产品数据"""
        try:
            if not URLValidator.is_valid_product_url(url):
                return create_error_result(
                    ErrorType.INVALID_URL,
                    f"无效的产品URL: {url}",
                    url
                )

            logger.info(f"📦 [Selenium爬虫] 获取产品数据: {url}")

            # 获取页面内容
            content_result = await self.get_page_content(url, wait_for_load=True)
            if not content_result.success:
                return content_result

            # 提取产品ID
            product_id = URLUtils.extract_product_id(url)
            if not product_id:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法从URL中提取产品ID",
                    url
                )

            # 解析产品数据
            product_data = await self._parse_product_data_from_page(content_result.data, product_id, url)
            if not product_data:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法解析产品数据",
                    url
                )

            self.stats['successful_extractions'] += 1
            return create_success_result(
                product_data,
                {'source': 'selenium_browser', 'url': url}
            )

        except Exception as e:
            self.stats['failed_extractions'] += 1
            logger.error(f"❌ [Selenium爬虫] 获取产品数据失败: {url} - {str(e)}")
            return create_error_result(
                ErrorType.UNKNOWN_ERROR,
                f"获取产品数据失败: {str(e)}",
                url
            )

    async def get_shop_data(self, url: str, load_products: bool = False) -> CrawlerResult:
        """获取店铺数据"""
        try:
            if not URLValidator.is_valid_shop_url(url):
                return create_error_result(
                    ErrorType.INVALID_URL,
                    f"无效的店铺URL: {url}",
                    url
                )

            logger.info(f"🏪 [Selenium爬虫] 获取店铺数据: {url}")

            # 获取页面内容
            content_result = await self.get_page_content(
                url,
                wait_for_load=True,
                load_more=load_products
            )
            if not content_result.success:
                return content_result

            # 提取店铺ID
            shop_id = URLUtils.extract_shop_id(url)
            if not shop_id:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法从URL中提取店铺ID",
                    url
                )

            # 解析店铺数据
            shop_data = await self._parse_shop_data_from_page(content_result.data, shop_id, url, load_products)
            if not shop_data:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法解析店铺数据",
                    url
                )

            self.stats['successful_extractions'] += 1
            return create_success_result(
                shop_data,
                {'source': 'selenium_browser', 'url': url}
            )

        except Exception as e:
            self.stats['failed_extractions'] += 1
            logger.error(f"❌ [Selenium爬虫] 获取店铺数据失败: {url} - {str(e)}")
            return create_error_result(
                ErrorType.UNKNOWN_ERROR,
                f"获取店铺数据失败: {str(e)}",
                url
            )

    async def get_page_content(self, url: str, **kwargs) -> CrawlerResult:
        """获取页面内容"""
        try:
            if not await self.initialize():
                return create_error_result(
                    ErrorType.NETWORK_ERROR,
                    "爬虫初始化失败",
                    url
                )

            # 更新统计信息
            self.stats['pages_loaded'] += 1
            self.stats['last_request_time'] = time.time()

            wait_for_load = kwargs.get('wait_for_load', self.config.wait_for_load)
            load_more = kwargs.get('load_more', self.config.load_more_enabled)

            logger.info(f"🌐 [Selenium爬虫] 访问页面: {url}")

            # 访问页面
            self.driver.get(url)

            if wait_for_load:
                # 等待页面加载
                await asyncio.sleep(3.5)

                # 智能页面处理
                content = await self._handle_page_loading_and_processing(url, load_more)
                if content is None:
                    return create_error_result(
                        ErrorType.TIMEOUT_ERROR,
                        "页面加载超时或失败",
                        url
                    )
            else:
                # 直接获取页面内容
                content = self.driver.page_source

            return create_success_result(
                content,
                {
                    'content_length': len(content),
                    'load_more_used': load_more,
                    'wait_for_load': wait_for_load
                }
            )

        except Exception as e:
            self.stats['failed_extractions'] += 1
            logger.error(f"❌ [Selenium爬虫] 获取页面内容失败: {url} - {str(e)}")
            return create_error_result(
                ErrorType.NETWORK_ERROR,
                f"获取页面内容失败: {str(e)}",
                url
            )

    # ==================== 私有方法 ====================

    async def _handle_page_loading_and_processing(self, url: str, load_more: bool = False) -> Optional[str]:
        """处理页面加载和智能处理"""
        try:
            max_wait_time = 60  # 最大等待时间
            check_interval = 2  # 检查间隔
            start_time = time.time()
            check_count = 0

            while time.time() - start_time < max_wait_time:
                check_count += 1

                try:
                    # 获取页面内容
                    page_content = self.driver.page_source
                    if not page_content:
                        await asyncio.sleep(check_interval)
                        continue

                    # 检测页面状态
                    page_state = self._detect_page_state(page_content)

                    if page_state == "normal":
                        # 正常页面
                        logger.info(f"✅ [Selenium爬虫] 页面加载完成，检测次数: {check_count}")

                        # 如果需要加载更多内容
                        if load_more and self._should_load_more(url, page_content):
                            logger.info("🔄 [Selenium爬虫] 开始执行加载更多操作...")
                            enhanced_content = await self._execute_load_more(page_content)
                            return enhanced_content if enhanced_content else page_content

                        return page_content

                    elif page_state == "captcha":
                        # 验证码页面
                        self.stats['captcha_detections'] += 1

                        if check_count == 1:
                            logger.warning("🚫 [Selenium爬虫] 检测到验证码页面")

                            # 如果有用户交互回调，通知用户处理验证码
                            if self.user_interaction_callback:
                                await self._handle_captcha_with_user_interaction()

                        # 继续等待验证码解决
                        await asyncio.sleep(check_interval)
                        continue

                    elif page_state == "loading":
                        # 页面仍在加载
                        if check_count % 5 == 0:
                            logger.debug(f"⏳ [Selenium爬虫] 页面仍在加载，已等待 {check_count * check_interval} 秒")
                        await asyncio.sleep(check_interval)
                        continue

                    else:
                        # 未知状态，等待后重试
                        await asyncio.sleep(check_interval)
                        continue

                except Exception as e:
                    logger.debug(f"页面处理异常: {str(e)}")
                    await asyncio.sleep(check_interval)
                    continue

            # 超时处理
            self.stats['timeouts'] += 1
            logger.warning(f"⏰ [Selenium爬虫] 页面处理超时，返回当前内容")

            try:
                return self.driver.page_source
            except Exception:
                return None

        except Exception as e:
            logger.error(f"❌ [Selenium爬虫] 页面处理失败: {str(e)}")
            return None

    def _detect_page_state(self, page_content: str) -> str:
        """检测页面状态"""
        try:
            content_lower = page_content.lower()

            # 检测验证码
            captcha_indicators = [
                'captcha', 'verification', 'robot check',
                'please verify', 'security check'
            ]
            if any(indicator in content_lower for indicator in captcha_indicators):
                return "captcha"

            # 检测加载状态
            loading_indicators = [
                'loading', 'please wait', 'loading...'
            ]
            if any(indicator in content_lower for indicator in loading_indicators):
                return "loading"

            # 检测正常页面（包含关键元素）
            normal_indicators = [
                'tiktok', 'shop', 'product', 'store'
            ]
            if any(indicator in content_lower for indicator in normal_indicators):
                return "normal"

            return "unknown"

        except Exception as e:
            logger.debug(f"页面状态检测异常: {str(e)}")
            return "unknown"

    def _should_load_more(self, url: str, page_content: str) -> bool:
        """判断是否需要执行加载更多操作"""
        try:
            # 只有店铺页面才需要加载更多
            if not URLUtils.is_shop_url(url):
                return False

            # 检查页面中是否有"加载更多"相关元素
            load_more_indicators = [
                'load more', 'view more', 'show more',
                'see more', 'more products'
            ]

            content_lower = page_content.lower()
            return any(indicator in content_lower for indicator in load_more_indicators)

        except Exception as e:
            logger.debug(f"加载更多判断异常: {str(e)}")
            return False

    async def _execute_load_more(self, baseline_content: str) -> Optional[str]:
        """执行加载更多操作"""
        try:
            max_attempts = self.config.max_load_more_attempts
            wait_time = 3

            for attempt in range(max_attempts):
                logger.info(f"🔄 [Selenium爬虫] 第 {attempt + 1} 次尝试加载更多内容...")

                # 查找加载更多按钮
                load_more_button = self._find_load_more_button()
                if not load_more_button:
                    logger.debug("未找到加载更多按钮，停止尝试")
                    break

                # 点击按钮
                if await self._click_load_more_button(load_more_button):
                    # 等待内容加载
                    await asyncio.sleep(wait_time)

                    # 检查是否有新内容
                    current_content = self.driver.page_source
                    if len(current_content) > len(baseline_content) * 1.1:  # 内容增加10%以上
                        logger.info("✅ [Selenium爬虫] 检测到新内容加载")
                        baseline_content = current_content
                        continue
                    else:
                        logger.debug("未检测到新内容，可能已加载完毕")
                        break
                else:
                    logger.debug("点击加载更多按钮失败")
                    break

            return self.driver.page_source

        except Exception as e:
            logger.error(f"❌ [Selenium爬虫] 执行加载更多失败: {str(e)}")
            return baseline_content

    def _find_load_more_button(self):
        """查找加载更多按钮"""
        try:
            # 常见的加载更多按钮选择器
            selectors = [
                'button[class*="load-more"]',
                'button[class*="view-more"]',
                'button[class*="show-more"]',
                'div[class*="load-more"]',
                'a[class*="load-more"]',
                'button:contains("Load More")',
                'button:contains("View More")',
                'button:contains("Show More")'
            ]

            for selector in selectors:
                try:
                    if 'contains' in selector:
                        # 使用XPath查找包含文本的元素
                        text = selector.split('"')[1]
                        xpath = f"//button[contains(text(), '{text}')]"
                        elements = self.driver.find_elements(By.XPATH, xpath)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    # 查找可见且可点击的按钮
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            return element

                except Exception as e:
                    logger.debug(f"查找按钮选择器失败 {selector}: {str(e)}")
                    continue

            return None

        except Exception as e:
            logger.debug(f"查找加载更多按钮异常: {str(e)}")
            return None

    async def _click_load_more_button(self, button) -> bool:
        """点击加载更多按钮"""
        try:
            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
            await asyncio.sleep(0.5)

            # 检查按钮是否仍然可见和可点击
            if not (button.is_displayed() and button.is_enabled()):
                return False

            # 尝试点击按钮
            try:
                button.click()
                logger.info("✅ [Selenium爬虫] 成功点击加载更多按钮")
                return True
            except Exception as e:
                # 如果普通点击失败，尝试JavaScript点击
                logger.debug(f"普通点击失败，尝试JavaScript点击: {str(e)}")
                self.driver.execute_script("arguments[0].click();", button)
                logger.info("✅ [Selenium爬虫] 通过JavaScript成功点击加载更多按钮")
                return True

        except Exception as e:
            logger.debug(f"点击加载更多按钮失败: {str(e)}")
            return False

    async def _handle_captcha_with_user_interaction(self):
        """处理验证码用户交互"""
        try:
            if self.user_interaction_callback:
                # 通知用户需要处理验证码
                await self.user_interaction_callback({
                    'type': 'captcha_detected',
                    'message': '检测到验证码，请手动完成验证',
                    'url': self.driver.current_url,
                    'action_required': True
                })

                # 等待用户处理验证码
                max_wait = self.config.captcha_wait_timeout
                check_interval = 5
                waited = 0

                while waited < max_wait:
                    await asyncio.sleep(check_interval)
                    waited += check_interval

                    # 检查验证码是否已解决
                    current_content = self.driver.page_source
                    if self._detect_page_state(current_content) != "captcha":
                        logger.info("✅ [Selenium爬虫] 验证码已解决")
                        break

                if waited >= max_wait:
                    logger.warning("⏰ [Selenium爬虫] 验证码处理超时")

        except Exception as e:
            logger.error(f"❌ [Selenium爬虫] 验证码处理失败: {str(e)}")

    async def _parse_product_data_from_page(self, content: str, product_id: str, url: str) -> Optional[ProductData]:
        """从页面内容解析产品数据"""
        try:
            logger.debug(f"📦 [Selenium解析] 开始解析产品数据: {product_id}")

            # 使用Selenium特有的DOM解析方法
            product_info = await self._extract_product_from_dom(product_id)
            if product_info:
                return self._build_product_data(product_info, url)

            # 如果DOM解析失败，回退到HTML解析
            product_info = self._extract_product_from_html_content(content, product_id)
            if product_info:
                return self._build_product_data(product_info, url)

            logger.warning(f"⚠️ [Selenium解析] 无法解析产品数据: {product_id}")
            return None

        except Exception as e:
            logger.error(f"❌ [Selenium解析] 产品数据解析失败: {product_id} - {str(e)}")
            return None

    async def _parse_shop_data_from_page(self, content: str, shop_id: str, url: str, load_products: bool) -> Optional[ShopData]:
        """从页面内容解析店铺数据"""
        try:
            logger.debug(f"🏪 [Selenium解析] 开始解析店铺数据: {shop_id}")

            # 使用Selenium特有的DOM解析方法
            shop_info = await self._extract_shop_from_dom(shop_id)
            if shop_info:
                shop_data = self._build_shop_data(shop_info, url)

                # 如果需要加载商品列表
                if load_products and shop_data:
                    products = await self._extract_products_from_dom()
                    shop_data.products = [self._build_product_data(p, url) for p in products if p]

                return shop_data

            # 如果DOM解析失败，回退到HTML解析
            shop_info = self._extract_shop_from_html_content(content, shop_id)
            if shop_info:
                shop_data = self._build_shop_data(shop_info, url)

                if load_products and shop_data:
                    products = self._extract_products_from_html_content(content)
                    shop_data.products = [self._build_product_data(p, url) for p in products if p]

                return shop_data

            logger.warning(f"⚠️ [Selenium解析] 无法解析店铺数据: {shop_id}")
            return None

        except Exception as e:
            logger.error(f"❌ [Selenium解析] 店铺数据解析失败: {shop_id} - {str(e)}")
            return None

    async def _extract_product_from_dom(self, product_id: str) -> Optional[Dict[str, Any]]:
        """使用Selenium DOM API提取产品信息"""
        try:
            product_info = {'product_id': product_id}

            # 提取标题
            title_selectors = [
                'h1[class*="product-title"]',
                'h1[class*="title"]',
                '[data-testid="product-title"]',
                '.product-name',
                '.item-title'
            ]

            for selector in title_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        product_info['title'] = element.text.strip()
                        break
                except:
                    continue

            # 提取价格
            price_selectors = [
                '[class*="price"]',
                '[data-testid="price"]',
                '.product-price',
                '.current-price'
            ]

            for selector in price_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        price_text = element.text.strip()
                        # 提取数字
                        import re
                        price_match = re.search(r'[\d,\.]+', price_text.replace(',', ''))
                        if price_match:
                            product_info['price'] = float(price_match.group())
                            break
                except:
                    continue

            # 提取销量
            sold_selectors = [
                '[class*="sold"]',
                '[data-testid="sold-count"]',
                '.sales-count'
            ]

            for selector in sold_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        sold_text = element.text.strip()
                        import re
                        sold_match = re.search(r'(\d+)', sold_text)
                        if sold_match:
                            product_info['sold_count'] = int(sold_match.group(1))
                            break
                except:
                    continue

            # 提取评分
            rating_selectors = [
                '[class*="rating"]',
                '[data-testid="rating"]',
                '.star-rating'
            ]

            for selector in rating_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        rating_text = element.text.strip()
                        import re
                        rating_match = re.search(r'([\d\.]+)', rating_text)
                        if rating_match:
                            product_info['rating'] = float(rating_match.group(1))
                            break
                except:
                    continue

            return product_info if product_info.get('title') else None

        except Exception as e:
            logger.debug(f"DOM产品提取失败: {str(e)}")
            return None

    async def _extract_shop_from_dom(self, shop_id: str) -> Optional[Dict[str, Any]]:
        """使用Selenium DOM API提取店铺信息"""
        try:
            shop_info = {'shop_id': shop_id}

            # 提取店铺名称
            name_selectors = [
                'h1[class*="shop-name"]',
                'h1[class*="store-name"]',
                '[data-testid="shop-name"]',
                '.shop-title',
                '.store-title'
            ]

            for selector in name_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        shop_info['shop_name'] = element.text.strip()
                        break
                except:
                    continue

            # 提取商品数量
            count_selectors = [
                '[class*="product-count"]',
                '[data-testid="product-count"]',
                '.item-count'
            ]

            for selector in count_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        count_text = element.text.strip()
                        import re
                        count_match = re.search(r'(\d+)', count_text)
                        if count_match:
                            shop_info['product_count'] = int(count_match.group(1))
                            break
                except:
                    continue

            return shop_info if shop_info.get('shop_name') else None

        except Exception as e:
            logger.debug(f"DOM店铺提取失败: {str(e)}")
            return None

    async def _extract_products_from_dom(self) -> List[Dict[str, Any]]:
        """使用Selenium DOM API提取商品列表"""
        try:
            products = []

            # 查找商品容器
            product_selectors = [
                '[class*="product-item"]',
                '[class*="item-card"]',
                '.w-full.cursor-pointer'
            ]

            for selector in product_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        product_info = await self._extract_product_from_element(element)
                        if product_info:
                            products.append(product_info)

                    if products:  # 如果找到了商品，就不再尝试其他选择器
                        break

                except Exception as e:
                    logger.debug(f"DOM商品列表提取失败 {selector}: {str(e)}")
                    continue

            return products

        except Exception as e:
            logger.debug(f"DOM商品列表提取失败: {str(e)}")
            return []

    async def _extract_product_from_element(self, element) -> Optional[Dict[str, Any]]:
        """从单个DOM元素中提取产品信息"""
        try:
            product_info = {}

            # 提取产品ID（从链接或属性中）
            try:
                link = element.find_element(By.TAG_NAME, 'a')
                href = link.get_attribute('href')
                if href:
                    product_id = URLUtils.extract_product_id(href)
                    if product_id:
                        product_info['product_id'] = product_id
            except:
                pass

            # 提取标题
            try:
                title_element = element.find_element(By.CSS_SELECTOR, 'h3, .title, [class*="title"]')
                if title_element and title_element.text.strip():
                    product_info['title'] = title_element.text.strip()
            except:
                pass

            # 提取价格
            try:
                price_element = element.find_element(By.CSS_SELECTOR, '[class*="price"]')
                if price_element and price_element.text.strip():
                    price_text = price_element.text.strip()
                    import re
                    price_match = re.search(r'[\d,\.]+', price_text.replace(',', ''))
                    if price_match:
                        product_info['price'] = float(price_match.group())
            except:
                pass

            # 提取销量
            try:
                sold_element = element.find_element(By.CSS_SELECTOR, '[class*="sold"]')
                if sold_element and sold_element.text.strip():
                    sold_text = sold_element.text.strip()
                    import re
                    sold_match = re.search(r'(\d+)', sold_text)
                    if sold_match:
                        product_info['sold_count'] = int(sold_match.group(1))
            except:
                pass

            return product_info if product_info.get('product_id') else None

        except Exception as e:
            logger.debug(f"元素产品提取失败: {str(e)}")
            return None

    def _extract_product_from_html_content(self, content: str, product_id: str) -> Optional[Dict[str, Any]]:
        """从HTML内容中提取产品信息（回退方法）"""
        try:
            import re
            product_info = {'product_id': product_id}

            # 提取标题
            title_patterns = [
                r'<h1[^>]*>([^<]+)</h1>',
                r'<title>([^<]+)</title>',
                r'"title":\s*"([^"]+)"'
            ]

            for pattern in title_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    product_info['title'] = match.group(1).strip()
                    break

            # 提取价格
            price_patterns = [
                r'"price":\s*"?([\d,\.]+)"?',
                r'<span[^>]*class="[^"]*price[^"]*"[^>]*>[\$¥€£]?([\d,\.]+)</span>'
            ]

            for pattern in price_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    try:
                        price_str = match.group(1).replace(',', '')
                        product_info['price'] = float(price_str)
                        break
                    except ValueError:
                        continue

            return product_info if product_info.get('title') else None

        except Exception as e:
            logger.debug(f"HTML产品提取失败: {str(e)}")
            return None

    def _extract_shop_from_html_content(self, content: str, shop_id: str) -> Optional[Dict[str, Any]]:
        """从HTML内容中提取店铺信息（回退方法）"""
        try:
            import re
            shop_info = {'shop_id': shop_id}

            # 提取店铺名称
            name_patterns = [
                r'<h1[^>]*>([^<]+)</h1>',
                r'"shop_name":\s*"([^"]+)"'
            ]

            for pattern in name_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    shop_info['shop_name'] = match.group(1).strip()
                    break

            return shop_info if shop_info.get('shop_name') else None

        except Exception as e:
            logger.debug(f"HTML店铺提取失败: {str(e)}")
            return None

    def _extract_products_from_html_content(self, content: str) -> List[Dict[str, Any]]:
        """从HTML内容中提取商品列表（回退方法）"""
        try:
            import re
            products = []

            # 简单的HTML解析
            product_pattern = r'<div[^>]*class="[^"]*product[^"]*"[^>]*>.*?</div>'
            matches = re.findall(product_pattern, content, re.DOTALL | re.IGNORECASE)

            for match in matches:
                product_info = {}

                # 提取产品ID
                id_match = re.search(r'data-product-id="(\d+)"', match)
                if id_match:
                    product_info['product_id'] = id_match.group(1)

                # 提取标题
                title_match = re.search(r'<h3[^>]*>([^<]+)</h3>', match)
                if title_match:
                    product_info['title'] = title_match.group(1).strip()

                if product_info.get('product_id'):
                    products.append(product_info)

            return products

        except Exception as e:
            logger.debug(f"HTML商品列表提取失败: {str(e)}")
            return []

    def _build_product_data(self, product_info: Dict[str, Any], url: str) -> ProductData:
        """构建ProductData对象"""
        try:
            return ProductData(
                product_id=str(product_info.get('product_id', '')),
                title=product_info.get('title', ''),
                price=float(product_info.get('price', 0)),
                currency=product_info.get('currency', 'USD'),
                sold_count=int(product_info.get('sold_count', 0)),
                rating=float(product_info.get('rating', 0)),
                review_count=int(product_info.get('review_count', 0)),
                images=product_info.get('images', []),
                description=product_info.get('description', ''),
                seller_id=product_info.get('seller_id'),
                seller_name=product_info.get('seller_name'),
                shop_id=product_info.get('shop_id'),
                shop_name=product_info.get('shop_name'),
                category=product_info.get('category'),
                shipping_fee=float(product_info.get('shipping_fee', 0)),
                availability=product_info.get('availability', True),
                url=url
            )
        except Exception as e:
            logger.debug(f"构建ProductData失败: {str(e)}")
            return ProductData(
                product_id=str(product_info.get('product_id', '')),
                title=product_info.get('title', ''),
                price=0,
                url=url
            )

    def _build_shop_data(self, shop_info: Dict[str, Any], url: str) -> ShopData:
        """构建ShopData对象"""
        try:
            return ShopData(
                shop_id=str(shop_info.get('shop_id', '')),
                shop_name=shop_info.get('shop_name', ''),
                seller_id=shop_info.get('seller_id'),
                seller_name=shop_info.get('seller_name'),
                product_count=int(shop_info.get('product_count', 0)),
                rating=float(shop_info.get('rating', 0)),
                follower_count=int(shop_info.get('follower_count', 0)),
                description=shop_info.get('description', ''),
                avatar_url=shop_info.get('avatar_url'),
                cover_url=shop_info.get('cover_url'),
                url=url
            )
        except Exception as e:
            logger.debug(f"构建ShopData失败: {str(e)}")
            return ShopData(
                shop_id=str(shop_info.get('shop_id', '')),
                shop_name=shop_info.get('shop_name', ''),
                url=url
            )
