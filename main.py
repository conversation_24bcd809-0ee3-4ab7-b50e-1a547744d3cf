"""
TikTok Shop 商品数据爬取工具 - 主程序入口
"""

import sys
import asyncio
import signal
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 延迟导入，避免在QApplication创建前导入GUI模块


class TikTokShopApp(QApplication):
    """TikTok Shop应用程序类"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # 设置应用程序信息
        self.setApplicationName("TikTok Shop Tool")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("TikTokShopTool Team")
        self.setOrganizationDomain("tiktokshoptool.com")
        
        # 设置应用程序图标
        # self.setWindowIcon(QIcon("resources/icons/app_icon.png"))
        
        # 设置字体
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
        
        # 设置样式
        self.setStyle("Fusion")
        
        # 主窗口
        self.main_window = None
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # 定时器用于处理信号
        self.timer = QTimer()
        self.timer.timeout.connect(lambda: None)
        self.timer.start(100)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，正在关闭应用...")
        self.quit()
    
    def show_splash_screen(self):
        """显示启动画面"""
        # 创建启动画面
        # splash_pixmap = QPixmap("resources/icons/TikTok-Logo.png")
        # if splash_pixmap.isNull():
        #     # 如果没有启动画面图片，创建一个简单的
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.white)

        splash = QSplashScreen(splash_pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)

        # 显示启动信息
        splash.showMessage(
            "正在启动 TikTok Shop 商品数据爬取工具...",
            Qt.AlignBottom | Qt.AlignCenter,
            Qt.black
        )
        
        splash.show()
        self.processEvents()
        
        return splash
    
    def initialize(self):
        """初始化应用程序"""
        try:
            # 显示启动画面
            splash = self.show_splash_screen()

            # 加载环境配置
            splash.showMessage("正在加载配置...", Qt.AlignBottom | Qt.AlignCenter)
            self.processEvents()

            # 延迟导入配置模块
            from src.config.settings import config, load_env_config
            load_env_config()

            # 设置日志系统
            splash.showMessage("正在初始化日志系统...", Qt.AlignBottom | Qt.AlignCenter)
            self.processEvents()

            from src.utils.logger_setup import setup_logger
            setup_logger()

            logger.info("TikTok Shop Tool 启动中...")
            logger.info(f"配置对象: {config}")

            # 创建主窗口
            splash.showMessage("正在创建主界面...", Qt.AlignBottom | Qt.AlignCenter)
            self.processEvents()

            # 延迟导入主窗口模块
            from src.gui.main_window import MainWindow
            self.main_window = MainWindow()

            # 关闭启动画面
            splash.finish(self.main_window)

            # 显示主窗口
            self.main_window.show()

            logger.info("应用程序初始化完成")
            return True

        except Exception as e:
            error_msg = f"应用程序初始化失败: {str(e)}"
            print(f"❌ {error_msg}")

            QMessageBox.critical(
                None, "初始化失败",
                f"应用程序初始化失败:\n{str(e)}\n\n请检查配置文件和依赖项。"
            )
            return False
    
    def run(self):
        """运行应用程序"""
        if self.initialize():
            logger.info("应用程序开始运行")
            return self.exec()
        else:
            return 1





def check_dependencies():
    """检查依赖项"""
    try:
        import PyQt5  # 修正：使用PyQt5而不是PyQt6
        import requests
        import aiohttp
        import pandas
        import openpyxl
        import bs4  # beautifulsoup4的正确导入名称
        import fake_useragent
        import loguru
        import pydantic

        logger.info("依赖项检查通过")
        return True

    except ImportError as e:
        logger.error(f"缺少依赖项: {str(e)}")
        QMessageBox.critical(
            None, "依赖项错误",
            f"缺少必要的依赖项: {str(e)}\n\n请运行以下命令安装依赖:\npip install -r requirements.txt"
        )
        return False


def check_system_requirements():
    """检查系统要求"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            logger.error(f"Python版本过低: {sys.version}")
            QMessageBox.critical(
                None, "系统要求",
                f"需要Python 3.8或更高版本\n当前版本: {sys.version}"
            )
            return False
        
        # 检查操作系统
        import platform
        os_name = platform.system()
        logger.info(f"操作系统: {os_name} {platform.release()}")
        
        # 检查可用内存
        import psutil
        memory = psutil.virtual_memory()
        if memory.available < 512 * 1024 * 1024:  # 512MB
            logger.warning(f"可用内存较低: {memory.available / 1024 / 1024:.1f}MB")
        
        logger.info("系统要求检查通过")
        return True
        
    except Exception as e:
        logger.error(f"系统要求检查失败: {str(e)}")
        return True  # 非关键错误，继续运行


def main():
    """主函数"""
    try:
        # 设置环境变量以支持高DPI
        import os
        os.environ.setdefault("QT_ENABLE_HIGHDPI_SCALING", "1")
        os.environ.setdefault("QT_AUTO_SCREEN_SCALE_FACTOR", "1")

        # 创建应用程序
        app = TikTokShopApp(sys.argv)
        
        # 检查系统要求
        if not check_system_requirements():
            return 1
        
        # 检查依赖项
        if not check_dependencies():
            return 1
        
        # 运行应用程序
        return app.run()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        return 0
    except Exception as e:
        logger.error(f"程序运行异常: {str(e)}")
        try:
            QMessageBox.critical(
                None, "程序错误",
                f"程序运行时发生错误:\n{str(e)}\n\n请查看日志文件获取详细信息。"
            )
        except:
            print(f"程序运行异常: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
