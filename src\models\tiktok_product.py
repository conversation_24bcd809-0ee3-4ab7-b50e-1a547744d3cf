"""
TikTok商品数据模型 - 专门用于临时存储
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
import json


@dataclass
class TikTokProduct:
    """TikTok商品数据模型 - 临时存储用"""

    # 基本信息
    product_id: str = ""
    # 移除title字段 - 根据业务需求不再提取产品标题

    # 图片信息
    image_urls: str = ""  # 逗号分隔的图片URL列表

    # 价格信息
    currency: str = "USD"
    sale_price: float = 0.0
    original_price: float = 0.0
    discount_amount: float = 0.0

    # 评价信息
    rating: float = 0.0
    review_count: int = 0

    # 销售信息
    sold_count: int = 0

    # 时间信息
    listing_time: str = ""  # 上架时间，格式：2025-06-04 22:19:10

    # 运费信息 - 扩展为详细的运费数据结构
    shipping_fee_price_str: str = ""  # 运费价格字符串，如 "$0.00"
    shipping_fee_price_val: str = ""  # 运费价格数值，如 "0"
    shipping_fee_currency: str = ""   # 运费币种，如 "USD"

    # 店铺信息
    shop_name: str = ""  # 店铺名称
    shop_product_count: int = 0  # 商店产品数量 (on_sell_product_count)

    # 商品链接
    canonical_url: str = ""  # 商品的标准链接

    # 元数据
    scraped_at: datetime = field(default_factory=datetime.now)
    source_url: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于导出"""
        return {
            "商品ID": self.product_id,
            # 移除商品标题字段
            "图片链接": self.image_urls,
            "币种": self.currency,
            "售价": self.sale_price,
            "原价": self.original_price,
            "折扣金额": self.discount_amount,
            "评分": self.rating,
            "评价数量": self.review_count,
            "销量": self.sold_count,
            "上架时间": self.listing_time,
            # 扩展运费信息为详细字段
            "运费价格": self.shipping_fee_price_str,
            "运费数值": self.shipping_fee_price_val,
            "运费币种": self.shipping_fee_currency,
            "店铺名称": self.shop_name,
            "店铺产品数量": self.shop_product_count,
            "商品链接": self.canonical_url,
            "爬取时间": self.scraped_at.strftime("%Y-%m-%d %H:%M:%S"),
            "来源URL": self.source_url
        }
    
    def to_export_dict(self) -> Dict[str, Any]:
        """转换为导出格式的字典 - 中文表头"""
        return {
            "商品ID": self.product_id,
            # 移除商品标题字段
            "图片链接": self.image_urls,
            "币种": self.currency,
            "售价": self.sale_price,
            "原价": self.original_price,
            "折扣金额": self.discount_amount,
            "评分": self.rating,
            "评价数量": self.review_count,
            "销量": self.sold_count,
            "上架时间": self.listing_time,
            # 扩展运费信息为详细字段
            "运费价格": self.shipping_fee_price_str,
            "运费数值": self.shipping_fee_price_val,
            "运费币种": self.shipping_fee_currency,
            "店铺名称": self.shop_name,
            "店铺产品数量": self.shop_product_count,
            "商品链接": self.canonical_url
        }
    
    @classmethod
    def from_tiktok_data(cls, product_data: Dict[str, Any], source_url: str = "") -> 'TikTokProduct':
        """从TikTok原始数据创建对象"""
        try:
            # 基本信息
            product_id = str(product_data.get('product_id', ''))
            # 移除标题提取 - 根据业务需求不再提取产品标题
            
            # 图片信息
            image_urls = []
            image_data = product_data.get('image', {})
            if isinstance(image_data, dict):
                # 尝试多种可能的图片数据结构
                url_list = image_data.get('url_list', [])
                image_urls_list = image_data.get('image_urls', [])

                # 处理url_list格式
                if url_list:
                    import codecs
                    for url in url_list:
                        try:
                            decoded_url = codecs.decode(url, 'unicode_escape')
                            image_urls.append(decoded_url)
                        except Exception:
                            image_urls.append(url)

                # 处理image_urls格式
                elif image_urls_list:
                    for img_obj in image_urls_list:
                        if isinstance(img_obj, dict):
                            url = img_obj.get('url', '')
                            if url:
                                image_urls.append(url)
                        elif isinstance(img_obj, str):
                            image_urls.append(img_obj)
            
            image_urls_str = ','.join(image_urls)
            
            # 价格信息
            price_info = product_data.get('product_price_info', {})
            currency = price_info.get('currency_name', 'USD')
            
            sale_price = 0.0
            original_price = 0.0
            discount_amount = 0.0
            
            if isinstance(price_info, dict):
                # 售价
                sale_price_str = price_info.get('sale_price_decimal', '0')
                try:
                    sale_price = float(sale_price_str)
                except (ValueError, TypeError):
                    sale_price = 0.0
                
                # 原价
                original_price_str = price_info.get('origin_price_decimal', '0')
                try:
                    original_price = float(original_price_str)
                except (ValueError, TypeError):
                    original_price = 0.0
                
                # 折扣金额
                promotion_details = price_info.get('promotion_deduction_details', {})
                if isinstance(promotion_details, dict):
                    discount_str = promotion_details.get('seller_subtotal_deduction', '0')
                    try:
                        discount_amount = float(discount_str)
                    except (ValueError, TypeError):
                        discount_amount = 0.0
            
            # 评价信息 - 支持多种数据结构
            rating = 0.0
            review_count = 0

            # 尝试rate_info结构
            rate_info = product_data.get('rate_info', {})
            if isinstance(rate_info, dict):
                try:
                    rating = float(rate_info.get('score', 0))
                except (ValueError, TypeError):
                    rating = 0.0

                try:
                    review_count = int(rate_info.get('review_count', '0'))
                except (ValueError, TypeError):
                    review_count = 0

            # 尝试product_rating_info结构
            rating_info = product_data.get('product_rating_info', {})
            if isinstance(rating_info, dict) and rating == 0.0:
                try:
                    rating = float(rating_info.get('average_rating', 0))
                except (ValueError, TypeError):
                    rating = 0.0

                try:
                    review_count = int(rating_info.get('review_count', '0'))
                except (ValueError, TypeError):
                    review_count = 0
            
            # 销售信息 - 支持多种数据结构
            sold_count = 0

            # 尝试sold_info结构
            sold_info = product_data.get('sold_info', {})
            if isinstance(sold_info, dict):
                try:
                    sold_count = int(sold_info.get('sold_count', 0))
                except (ValueError, TypeError):
                    sold_count = 0

            # 尝试直接的sold_count字段
            if sold_count == 0:
                try:
                    sold_count = int(product_data.get('sold_count', 0))
                except (ValueError, TypeError):
                    sold_count = 0
            
            # 上架时间
            listing_time = ""
            seo_url_data = product_data.get('seo_url', {})
            if isinstance(seo_url_data, dict):
                updated_at = seo_url_data.get('updated_at', '')
                if updated_at:
                    try:
                        # 转换时间戳（毫秒）为标准时间格式
                        timestamp = int(updated_at) / 1000  # 转换为秒
                        dt = datetime.fromtimestamp(timestamp)
                        listing_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except (ValueError, TypeError):
                        listing_time = ""
            
            # 运费信息 - 提取详细的运费数据结构
            # 注意：这里先设置默认值，实际的运费信息需要从产品详情页面获取
            shipping_fee_price_str = ""
            shipping_fee_price_val = ""
            shipping_fee_currency = ""

            # 尝试从当前数据中提取运费信息（如果有的话）
            shipping_fee_data = product_data.get('shipping_fee', {})
            if isinstance(shipping_fee_data, dict):
                shipping_fee_price_str = shipping_fee_data.get('price_str', '')
                shipping_fee_price_val = shipping_fee_data.get('price_val', '')
                shipping_fee_currency = shipping_fee_data.get('currency', '')
            else:
                # 备用方法：从营销信息中查找运费标签
                marketing_info = product_data.get('product_marketing_info', {})
                if isinstance(marketing_info, dict):
                    placement_labels = marketing_info.get('placement_labels', {})
                    if isinstance(placement_labels, dict):
                        for key, labels in placement_labels.items():
                            if isinstance(labels, list):
                                for label in labels:
                                    if isinstance(label, dict):
                                        text = label.get('text', '')
                                        if 'shipping' in text.lower():
                                            shipping_fee_price_str = text
                                            break
                                if shipping_fee_price_str:
                                    break

            # 店铺名称和产品数量 - 使用增强的提取逻辑
            shop_name = ""
            shop_product_count = 0
            seller_info = product_data.get('seller_info', {})
            if isinstance(seller_info, dict):
                shop_name = seller_info.get('shop_name', '')

            # 使用增强的店铺产品数量提取逻辑
            shop_product_count = cls._extract_shop_product_count_enhanced(product_data)

            # 商品链接
            canonical_url = ""
            seo_url_data = product_data.get('seo_url', {})
            if isinstance(seo_url_data, dict):
                canonical_url = seo_url_data.get('canonical_url', '')

            return cls(
                product_id=product_id,
                # 移除title字段
                image_urls=image_urls_str,
                currency=currency,
                sale_price=sale_price,
                original_price=original_price,
                discount_amount=discount_amount,
                rating=rating,
                review_count=review_count,
                sold_count=sold_count,
                listing_time=listing_time,
                # 使用新的运费字段结构
                shipping_fee_price_str=shipping_fee_price_str,
                shipping_fee_price_val=shipping_fee_price_val,
                shipping_fee_currency=shipping_fee_currency,
                shop_name=shop_name,
                shop_product_count=shop_product_count,
                canonical_url=canonical_url,
                source_url=source_url
            )
            
        except Exception as e:
            # 如果解析失败，返回一个基本的对象
            return cls(
                product_id=str(product_data.get('product_id', '')),
                # 移除title字段
                source_url=source_url
            )

    @classmethod
    def _extract_shop_product_count_enhanced(cls, product_data: Dict[str, Any]) -> int:
        """
        增强的店铺产品数量提取逻辑
        支持多种可能的数据结构和字段名
        """
        # 可能的字段名列表
        possible_field_names = [
            'on_sell_product_count',
            'product_count',
            'shop_product_count',
            'total_products',
            'item_count',
            'products_count',
            'sell_product_count',
            'active_product_count',
            'available_product_count',
            'listing_count'
        ]

        # 可能的路径列表
        possible_paths = [
            'seller_info',
            'shop_info',
            'shop_data',
            'store_info',
            'merchant_info',
            'shop_detail',
            'store_detail'
        ]

        # 1. 首先在seller_info中查找
        seller_info = product_data.get('seller_info', {})
        if isinstance(seller_info, dict):
            for field_name in possible_field_names:
                if field_name in seller_info:
                    try:
                        shop_product_count = int(seller_info[field_name])
                        if shop_product_count > 0:
                            return shop_product_count
                    except (ValueError, TypeError):
                        continue

            # 在seller_info的嵌套对象中查找（支持更深层嵌套）
            for nested_key in ['shop_info', 'store_info', 'shop_data', 'shop_detail']:
                nested_obj = seller_info.get(nested_key, {})
                if isinstance(nested_obj, dict):
                    # 直接在嵌套对象中查找
                    for field_name in possible_field_names:
                        if field_name in nested_obj:
                            try:
                                shop_product_count = int(nested_obj[field_name])
                                if shop_product_count > 0:
                                    return shop_product_count
                            except (ValueError, TypeError):
                                continue

                    # 继续深入一层查找
                    for sub_key, sub_value in nested_obj.items():
                        if isinstance(sub_value, dict):
                            for field_name in possible_field_names:
                                if field_name in sub_value:
                                    try:
                                        shop_product_count = int(sub_value[field_name])
                                        if shop_product_count > 0:
                                            return shop_product_count
                                    except (ValueError, TypeError):
                                        continue

        # 2. 在其他可能的路径中查找
        for path in possible_paths:
            path_data = product_data.get(path, {})
            if isinstance(path_data, dict):
                for field_name in possible_field_names:
                    if field_name in path_data:
                        try:
                            shop_product_count = int(path_data[field_name])
                            if shop_product_count > 0:
                                return shop_product_count
                        except (ValueError, TypeError):
                            continue

        # 3. 在根级别查找
        for field_name in possible_field_names:
            if field_name in product_data:
                try:
                    shop_product_count = int(product_data[field_name])
                    if shop_product_count > 0:
                        return shop_product_count
                except (ValueError, TypeError):
                    continue

        # 4. 递归搜索所有数值字段（增加深度以支持更深层嵌套）
        def find_numeric_fields_recursive(obj, path="", max_depth=5):
            if max_depth <= 0:
                return []

            results = []
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key

                    # 排除明显不是产品数量的字段
                    excluded_fields = ['product_id', 'seller_id', 'shop_id', 'user_id', 'id', 'timestamp', 'price', 'rating']
                    if key.lower() in excluded_fields:
                        continue

                    # 检查是否是数值字段且包含相关关键词
                    if isinstance(value, (int, str)) and any(keyword in key.lower() for keyword in ['product', 'count', 'total', 'item', 'listing']):
                        try:
                            num_value = int(value)
                            # 合理的产品数量范围（1-100000）
                            if 1 <= num_value <= 100000:
                                results.append({
                                    'path': current_path,
                                    'value': num_value,
                                    'key': key
                                })
                        except (ValueError, TypeError):
                            pass

                    # 递归搜索
                    elif isinstance(value, dict):
                        results.extend(find_numeric_fields_recursive(value, current_path, max_depth - 1))

            return results

        numeric_fields = find_numeric_fields_recursive(product_data)
        if numeric_fields:
            # 按优先级排序选择最可能的字段
            def get_field_priority(field):
                key_lower = field['key'].lower()
                # 最高优先级：包含'product'和'count'
                if 'product' in key_lower and 'count' in key_lower:
                    return 1
                # 高优先级：包含'count'或'total'
                elif 'count' in key_lower or 'total' in key_lower:
                    return 2
                # 中等优先级：包含'listing'或'item'
                elif 'listing' in key_lower or 'item' in key_lower:
                    return 3
                # 低优先级：其他包含'product'的字段
                elif 'product' in key_lower:
                    return 4
                else:
                    return 5

            # 按优先级排序
            numeric_fields.sort(key=get_field_priority)

            # 选择优先级最高的字段
            best_match = numeric_fields[0]
            return best_match['value']

        return 0


class TikTokProductStorage:
    """TikTok商品临时存储管理器"""
    
    def __init__(self):
        self.products: List[TikTokProduct] = []
        self.has_unsaved_data = False
    
    def add_product(self, product: TikTokProduct):
        """添加商品（带去重机制）"""
        # 检查是否已存在相同product_id的商品
        existing_index = None
        for i, existing_product in enumerate(self.products):
            if existing_product.product_id == product.product_id:
                existing_index = i
                break

        if existing_index is not None:
            # 如果存在，替换现有商品（保持最新数据）
            self.products[existing_index] = product
            from loguru import logger
            logger.debug(f"🔄 [临时存储] 更新现有商品: {product.product_id}")
        else:
            # 如果不存在，添加新商品
            self.products.append(product)
            from loguru import logger
            logger.debug(f"➕ [临时存储] 添加新商品: {product.product_id}")

        self.has_unsaved_data = True
    
    def clear_products(self):
        """清空商品数据"""
        self.products.clear()
        self.has_unsaved_data = False
    
    def get_products(self) -> List[TikTokProduct]:
        """获取所有商品"""
        return self.products.copy()
    
    def get_product_count(self) -> int:
        """获取商品数量"""
        return len(self.products)

    def get_unique_product_count(self) -> int:
        """获取唯一商品数量（按product_id去重）"""
        unique_ids = set(product.product_id for product in self.products)
        return len(unique_ids)
    
    def export_to_dict_list(self) -> List[Dict[str, Any]]:
        """导出为字典列表"""
        return [product.to_export_dict() for product in self.products]
    
    def mark_as_saved(self):
        """标记为已保存"""
        self.has_unsaved_data = False
    
    def has_data(self) -> bool:
        """是否有数据"""
        return len(self.products) > 0
    
    def has_unsaved_changes(self) -> bool:
        """是否有未保存的更改"""
        return self.has_unsaved_data and self.has_data()


# 全局存储实例
tiktok_storage = TikTokProductStorage()
