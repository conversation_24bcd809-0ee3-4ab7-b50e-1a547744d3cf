"""
日志设置工具
"""

import sys
from pathlib import Path
from loguru import logger


def _is_console_available():
    """检查控制台是否可用（适配PyInstaller console=False环境）"""
    try:
        # 检查标准错误流是否可用
        if hasattr(sys.stderr, 'write') and hasattr(sys.stderr, 'flush'):
            # 尝试写入测试
            sys.stderr.write('')
            sys.stderr.flush()
            return True
    except (OSError, AttributeError, ValueError):
        pass

    # 检查是否在PyInstaller无控制台环境中
    if hasattr(sys, '_MEIPASS') and not hasattr(sys.stderr, 'fileno'):
        return False

    return True


def setup_logger():
    """设置日志系统"""
    try:
        # 动态导入配置，避免循环导入和初始化顺序问题
        from ..config.settings import config

        # 检查配置是否正确加载
        if config is None or config.log is None:
            raise ValueError("配置对象未正确初始化")

        if config.log.log_dir is None:
            raise ValueError("日志目录配置为空")

        # 确保日志目录存在
        config.log.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 移除默认处理器
        logger.remove()

        # 检查是否在无控制台环境中（PyInstaller console=False）
        console_available = _is_console_available()

        # 只在控制台可用时添加控制台处理器
        if console_available:
            logger.add(
                sys.stderr,
                level=config.log.level,
                format=config.log.format,
                colorize=True,
                backtrace=True,
                diagnose=True
            )
        else:
            # 无控制台环境，使用文件作为主要输出
            # 不添加控制台处理器，避免在无控制台环境中出错
            pass
        
        # 添加主日志文件处理器
        log_file = config.log.log_dir / "tiktok_shop_tool.log"
        logger.add(
            str(log_file),
            level=config.log.level,
            format=config.log.format,
            rotation=config.log.rotation,
            retention=config.log.retention,
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
        
        # 添加错误日志文件处理器
        error_log_file = config.log.log_dir / "error.log"
        logger.add(
            str(error_log_file),
            level="ERROR",
            format=config.log.format,
            rotation=config.log.rotation,
            retention=config.log.retention,
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
        
        # 添加爬虫专用日志文件
        crawler_log_file = config.log.log_dir / "crawler.log"
        logger.add(
            str(crawler_log_file),
            level="ERROR",
            format=config.log.format,
            rotation=config.log.rotation,
            retention=config.log.retention,
            encoding="utf-8",
            filter=lambda record: "crawler" in record["name"].lower()
        )
        
        logger.info("日志系统初始化完成")
        logger.info(f"日志目录: {config.log.log_dir}")
        logger.info(f"日志级别: {config.log.level}")
        
    except Exception as e:
        error_msg = f"日志系统初始化失败: {str(e)}"
        # 在无控制台环境中，print可能会失败
        try:
            print(f"❌ {error_msg}")
            # 打印调试信息
            print(f"调试信息:")
            print(f"  - config对象: {config}")
            print(f"  - config.log对象: {getattr(config, 'log', 'None')}")
            print(f"  - log_dir: {getattr(getattr(config, 'log', None), 'log_dir', 'None')}")
        except:
            pass  # 忽略print失败

        # 尝试使用基本的日志配置
        try:
            import tempfile
            fallback_log_dir = Path(tempfile.gettempdir()) / "TikTokShopTool_fallback" / "logs"
            fallback_log_dir.mkdir(parents=True, exist_ok=True)

            logger.remove()

            # 检查控制台是否可用
            if _is_console_available():
                logger.add(
                    sys.stderr,
                    level="ERROR",
                    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
                    colorize=True
                )

            logger.add(
                str(fallback_log_dir / "fallback.log"),
                level="ERROR",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
                rotation="10 MB",
                retention="7 days",
                encoding="utf-8"
            )

            logger.warning(f"使用备用日志配置: {fallback_log_dir}")
            logger.error(f"原始错误: {error_msg}")

        except Exception as fallback_error:
            try:
                print(f"❌ 备用日志配置也失败: {fallback_error}")
            except:
                pass  # 忽略print失败
            raise e


def get_logger(name: str):
    """获取指定名称的日志器"""
    return logger.bind(name=name)
