/* TikTok Shop Tool - 深色主题样式 */

/* 主窗口 */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* 通用组件 */
QWidget {
    background-color: #2b2b2b;
    color: #ffffff;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
}

/* 按钮样式 */
QPushButton {
    background-color: #0078d4;
    border: none;
    color: white;
    padding: 8px 16px;
    text-align: center;
    font-size: 9pt;
    font-weight: bold;
    border-radius: 4px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #106ebe;
}

QPushButton:pressed {
    background-color: #005a9e;
}

QPushButton:disabled {
    background-color: #404040;
    color: #808080;
}

/* 危险按钮 */
QPushButton[class="danger"] {
    background-color: #d13438;
}

QPushButton[class="danger"]:hover {
    background-color: #b71c1c;
}

/* 成功按钮 */
QPushButton[class="success"] {
    background-color: #107c10;
}

QPushButton[class="success"]:hover {
    background-color: #0e6e0e;
}

/* 输入框 */
QLineEdit {
    background-color: #404040;
    border: 2px solid #555555;
    border-radius: 4px;
    padding: 8px;
    color: #ffffff;
    font-size: 9pt;
}

QLineEdit:focus {
    border-color: #0078d4;
}

QLineEdit:disabled {
    background-color: #333333;
    color: #808080;
}

/* 文本编辑器 */
QTextEdit {
    background-color: #404040;
    border: 2px solid #555555;
    border-radius: 4px;
    padding: 8px;
    color: #ffffff;
    font-size: 9pt;
}

QTextEdit:focus {
    border-color: #0078d4;
}

/* 组合框 */
QComboBox {
    background-color: #404040;
    border: 2px solid #555555;
    border-radius: 4px;
    padding: 6px 12px;
    color: #ffffff;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #0078d4;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(resources/icons/arrow_down_white.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #404040;
    border: 1px solid #555555;
    selection-background-color: #0078d4;
    color: #ffffff;
}

/* 数值输入框 */
QSpinBox, QDoubleSpinBox {
    background-color: #404040;
    border: 2px solid #555555;
    border-radius: 4px;
    padding: 6px;
    color: #ffffff;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #0078d4;
}

/* 复选框 */
QCheckBox {
    color: #ffffff;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #555555;
    border-radius: 3px;
    background-color: #404040;
}

QCheckBox::indicator:checked {
    background-color: #0078d4;
    border-color: #0078d4;
    image: url(resources/icons/check_white.png);
}

QCheckBox::indicator:hover {
    border-color: #0078d4;
}

/* 单选按钮 */
QRadioButton {
    color: #ffffff;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #555555;
    border-radius: 8px;
    background-color: #404040;
}

QRadioButton::indicator:checked {
    background-color: #0078d4;
    border-color: #0078d4;
}

/* 进度条 */
QProgressBar {
    background-color: #404040;
    border: 2px solid #555555;
    border-radius: 5px;
    text-align: center;
    color: #ffffff;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #0078d4;
    border-radius: 3px;
}

/* 滑块 */
QSlider::groove:horizontal {
    background-color: #404040;
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #0078d4;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #106ebe;
}

/* 表格 */
QTableWidget {
    background-color: #404040;
    alternate-background-color: #383838;
    gridline-color: #555555;
    color: #ffffff;
    border: 1px solid #555555;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QHeaderView::section {
    background-color: #333333;
    color: #ffffff;
    padding: 8px;
    border: 1px solid #555555;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #404040;
}

/* 列表 */
QListWidget {
    background-color: #404040;
    border: 1px solid #555555;
    color: #ffffff;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #555555;
}

QListWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QListWidget::item:hover {
    background-color: #4a4a4a;
}

/* 树形视图 */
QTreeWidget {
    background-color: #404040;
    border: 1px solid #555555;
    color: #ffffff;
}

QTreeWidget::item {
    padding: 4px;
}

QTreeWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

/* 选项卡 */
QTabWidget::pane {
    border: 1px solid #555555;
    background-color: #404040;
}

QTabBar::tab {
    background-color: #333333;
    color: #ffffff;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #404040;
    border-bottom: 2px solid #0078d4;
}

QTabBar::tab:hover {
    background-color: #4a4a4a;
}

/* 分组框 */
QGroupBox {
    color: #ffffff;
    font-weight: bold;
    border: 2px solid #555555;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #0078d4;
}

/* 分割器 */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #333333;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar:horizontal {
    background-color: #333333;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #555555;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #666666;
}

/* 菜单 */
QMenuBar {
    background-color: #333333;
    color: #ffffff;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    padding: 8px 12px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #0078d4;
}

QMenu {
    background-color: #404040;
    border: 1px solid #555555;
    color: #ffffff;
}

QMenu::item {
    padding: 8px 24px;
}

QMenu::item:selected {
    background-color: #0078d4;
}

/* 状态栏 */
QStatusBar {
    background-color: #333333;
    color: #ffffff;
    border-top: 1px solid #555555;
}

/* 工具提示 */
QToolTip {
    background-color: #404040;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 4px;
    border-radius: 3px;
}
