"""
店铺数据模型
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
import json


class ShopStatus(Enum):
    """店铺状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    CLOSED = "closed"


class ShopType(Enum):
    """店铺类型枚举"""
    INDIVIDUAL = "individual"
    BUSINESS = "business"
    BRAND = "brand"
    OFFICIAL = "official"


@dataclass
class ShopRating:
    """店铺评分信息"""
    average_rating: float = 0.0
    total_reviews: int = 0
    positive_rate: float = 0.0  # 好评率
    response_rate: float = 0.0  # 回复率
    response_time: Optional[str] = None  # 平均回复时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "average_rating": self.average_rating,
            "total_reviews": self.total_reviews,
            "positive_rate": self.positive_rate,
            "response_rate": self.response_rate,
            "response_time": self.response_time
        }


@dataclass
class ShopStats:
    """店铺统计信息"""
    total_products: int = 0
    active_products: int = 0
    total_sales: int = 0
    monthly_sales: int = 0
    followers: int = 0
    following: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_products": self.total_products,
            "active_products": self.active_products,
            "total_sales": self.total_sales,
            "monthly_sales": self.monthly_sales,
            "followers": self.followers,
            "following": self.following
        }


@dataclass
class ShopContact:
    """店铺联系信息"""
    email: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    social_media: Dict[str, str] = field(default_factory=dict)  # {"instagram": "url", "facebook": "url"}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "email": self.email,
            "phone": self.phone,
            "website": self.website,
            "social_media": self.social_media
        }


@dataclass
class ShopAddress:
    """店铺地址信息"""
    country: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    address: Optional[str] = None
    postal_code: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "country": self.country,
            "state": self.state,
            "city": self.city,
            "address": self.address,
            "postal_code": self.postal_code
        }


@dataclass
class Shop:
    """店铺数据模型"""
    
    # 基本信息
    shop_id: str
    shop_name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    
    # 店铺类型和状态
    shop_type: ShopType = ShopType.INDIVIDUAL
    status: ShopStatus = ShopStatus.ACTIVE
    
    # 链接信息
    shop_url: str = ""
    avatar_url: Optional[str] = None
    banner_url: Optional[str] = None
    
    # 评分和统计
    rating: ShopRating = field(default_factory=ShopRating)
    stats: ShopStats = field(default_factory=ShopStats)
    
    # 联系和地址信息
    contact: ShopContact = field(default_factory=ShopContact)
    address: ShopAddress = field(default_factory=ShopAddress)
    
    # 认证信息
    is_verified: bool = False
    is_official: bool = False
    verification_badges: List[str] = field(default_factory=list)
    
    # 分类和标签
    categories: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    
    # 营业信息
    business_hours: Dict[str, str] = field(default_factory=dict)  # {"monday": "9:00-18:00"}
    shipping_info: Dict[str, Any] = field(default_factory=dict)
    return_policy: Optional[str] = None
    
    # 时间信息
    created_at: Optional[datetime] = None
    joined_at: Optional[datetime] = None
    last_active: Optional[datetime] = None
    scraped_at: datetime = field(default_factory=datetime.now)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "shop_id": self.shop_id,
            "shop_name": self.shop_name,
            "display_name": self.display_name,
            "description": self.description,
            "shop_type": self.shop_type.value,
            "status": self.status.value,
            "shop_url": self.shop_url,
            "avatar_url": self.avatar_url,
            "banner_url": self.banner_url,
            "rating": self.rating.to_dict(),
            "stats": self.stats.to_dict(),
            "contact": self.contact.to_dict(),
            "address": self.address.to_dict(),
            "is_verified": self.is_verified,
            "is_official": self.is_official,
            "verification_badges": self.verification_badges,
            "categories": self.categories,
            "tags": self.tags,
            "business_hours": self.business_hours,
            "shipping_info": self.shipping_info,
            "return_policy": self.return_policy,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "joined_at": self.joined_at.isoformat() if self.joined_at else None,
            "last_active": self.last_active.isoformat() if self.last_active else None,
            "scraped_at": self.scraped_at.isoformat(),
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Shop':
        """从字典创建Shop实例"""
        # 处理评分信息
        rating = ShopRating()
        if "rating" in data and data["rating"]:
            rating = ShopRating(**data["rating"])
        
        # 处理统计信息
        stats = ShopStats()
        if "stats" in data and data["stats"]:
            stats = ShopStats(**data["stats"])
        
        # 处理联系信息
        contact = ShopContact()
        if "contact" in data and data["contact"]:
            contact = ShopContact(**data["contact"])
        
        # 处理地址信息
        address = ShopAddress()
        if "address" in data and data["address"]:
            address = ShopAddress(**data["address"])
        
        # 处理时间字段
        time_fields = ["created_at", "joined_at", "last_active", "scraped_at"]
        for field_name in time_fields:
            if field_name in data and data[field_name] and isinstance(data[field_name], str):
                data[field_name] = datetime.fromisoformat(data[field_name])
        
        # 处理枚举字段
        if "shop_type" in data:
            data["shop_type"] = ShopType(data["shop_type"])
        if "status" in data:
            data["status"] = ShopStatus(data["status"])
        
        # 设置处理后的数据
        data["rating"] = rating
        data["stats"] = stats
        data["contact"] = contact
        data["address"] = address
        
        return cls(**data)
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        return self.display_name or self.shop_name
    
    def get_full_address(self) -> str:
        """获取完整地址"""
        address_parts = []
        if self.address.address:
            address_parts.append(self.address.address)
        if self.address.city:
            address_parts.append(self.address.city)
        if self.address.state:
            address_parts.append(self.address.state)
        if self.address.country:
            address_parts.append(self.address.country)
        return ", ".join(address_parts)
    
    def is_high_rated(self, threshold: float = 4.0) -> bool:
        """是否为高评分店铺"""
        return self.rating.average_rating >= threshold
    
    def is_popular(self, min_followers: int = 1000) -> bool:
        """是否为热门店铺"""
        return self.stats.followers >= min_followers
    
    def get_trust_score(self) -> float:
        """计算信任度评分 (0-100)"""
        score = 0.0
        
        # 评分权重 (40%)
        if self.rating.total_reviews > 0:
            score += (self.rating.average_rating / 5.0) * 40
        
        # 认证权重 (20%)
        if self.is_verified:
            score += 10
        if self.is_official:
            score += 10
        
        # 好评率权重 (20%)
        score += (self.rating.positive_rate / 100.0) * 20
        
        # 回复率权重 (10%)
        score += (self.rating.response_rate / 100.0) * 10
        
        # 活跃度权重 (10%)
        if self.last_active:
            days_since_active = (datetime.now() - self.last_active).days
            if days_since_active <= 7:
                score += 10
            elif days_since_active <= 30:
                score += 5
        
        return min(100.0, score)
