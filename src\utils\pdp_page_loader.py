"""
PDP页面加载器
实现简化的页面加载逻辑，解决页面加载超时问题
"""

import time
import asyncio
from typing import Optional, Dict, Any
from loguru import logger
from selenium.common.exceptions import TimeoutException, WebDriverException


class PDPPageLoader:
    """PDP页面加载器 - 简化版"""
    
    def __init__(self, driver, max_retries: int = 3, fixed_wait_time: int = 3, retry_interval: int = 2):
        """
        初始化PDP页面加载器
        
        Args:
            driver: Selenium WebDriver实例
            max_retries: 最大重试次数
            fixed_wait_time: 固定等待时间（秒）
            retry_interval: 重试间隔时间（秒）
        """
        self.driver = driver
        self.max_retries = max_retries
        self.fixed_wait_time = fixed_wait_time
        self.retry_interval = retry_interval
        
        logger.info(f"🔧 [PDP加载器] 初始化完成")
        logger.info(f"   - 最大重试次数: {max_retries}")
        logger.info(f"   - 固定等待时间: {fixed_wait_time}秒")
        logger.info(f"   - 重试间隔: {retry_interval}秒")
    
    async def load_pdp_page(self, url: str) -> Optional[str]:
        """
        加载PDP页面内容（简化版）
        
        实现6步重试流程：
        1. 访问PDP页面URL
        2. 等待3秒钟（固定等待时间）
        3. 获取当前页面内容
        4. 尝试解析页面数据
        5. 检查解析结果
        6. 重试控制
        
        Args:
            url: PDP页面URL
            
        Returns:
            页面内容字符串，失败返回None
        """
        logger.info(f"🚀 [PDP加载器] 开始加载PDP页面: {url}")
        
        for attempt in range(1, self.max_retries + 1):
            try:
                logger.info(f"🔄 [PDP加载器] 第{attempt}次尝试加载页面")
                
                # 步骤1: 访问PDP页面URL
                logger.info(f"🌐 [PDP加载器] 步骤1: 访问页面URL")
                start_time = time.time()
                
                # 设置较短的页面加载超时，避免长时间等待
                self.driver.set_page_load_timeout(10)  # 10秒超时
                
                try:
                    self.driver.get(url)
                    logger.info(f"✅ [PDP加载器] 页面访问成功")
                except TimeoutException:
                    # 页面加载超时，但可能内容已经加载，继续处理
                    logger.warning(f"⏰ [PDP加载器] 页面加载超时，但继续处理")
                except Exception as e:
                    logger.error(f"❌ [PDP加载器] 页面访问失败: {str(e)}")
                    if attempt < self.max_retries:
                        logger.info(f"🔄 [PDP加载器] {self.retry_interval}秒后重试...")
                        await asyncio.sleep(self.retry_interval)
                        continue
                    else:
                        return None
                
                # 步骤2: 等待3秒钟（固定等待时间）
                logger.info(f"⏳ [PDP加载器] 步骤2: 固定等待{self.fixed_wait_time}秒")
                await asyncio.sleep(self.fixed_wait_time)
                
                # 步骤3: 获取当前页面内容
                logger.info(f"📄 [PDP加载器] 步骤3: 获取页面内容")
                try:
                    page_content = self.driver.page_source
                    content_length = len(page_content)
                    logger.info(f"✅ [PDP加载器] 获取到页面内容，长度: {content_length}字符")
                except Exception as e:
                    logger.error(f"❌ [PDP加载器] 获取页面内容失败: {str(e)}")
                    if attempt < self.max_retries:
                        logger.info(f"🔄 [PDP加载器] {self.retry_interval}秒后重试...")
                        await asyncio.sleep(self.retry_interval)
                        continue
                    else:
                        return None
                
                # 步骤4: 尝试解析页面数据
                logger.info(f"🔍 [PDP加载器] 步骤4: 解析页面数据")
                parse_result = self._parse_page_content(page_content)
                
                # 步骤5: 检查解析结果
                logger.info(f"✅ [PDP加载器] 步骤5: 检查解析结果")
                
                if parse_result['has_product_data']:
                    # 检测到正常的商品数据
                    elapsed = time.time() - start_time
                    logger.info(f"🎉 [PDP加载器] 成功加载PDP页面！")
                    logger.info(f"   - 尝试次数: {attempt}")
                    logger.info(f"   - 总耗时: {elapsed:.2f}秒")
                    logger.info(f"   - 内容长度: {content_length}字符")
                    logger.info(f"   - 包含商品数据: 是")
                    return page_content
                
                elif parse_result['has_captcha']:
                    # 检测到滑块验证码
                    logger.warning(f"🚫 [PDP加载器] 检测到验证码，需要人工处理")
                    logger.info(f"💡 [PDP加载器] 验证码类型: {parse_result['captcha_type']}")
                    
                    # 返回页面内容，让上层处理验证码
                    return page_content
                
                else:
                    # 既无数据也无验证码
                    logger.warning(f"⚠️ [PDP加载器] 页面内容异常，无商品数据也无验证码")
                    logger.info(f"📊 [PDP加载器] 页面分析结果:")
                    logger.info(f"   - 包含商品数据: {parse_result['has_product_data']}")
                    logger.info(f"   - 包含验证码: {parse_result['has_captcha']}")
                    logger.info(f"   - 页面类型: {parse_result['page_type']}")
                    
                    # 步骤6: 重试控制
                    if attempt < self.max_retries:
                        logger.info(f"🔄 [PDP加载器] 第{attempt}次尝试失败，{self.retry_interval}秒后重试...")
                        await asyncio.sleep(self.retry_interval)
                        continue
                    else:
                        logger.error(f"❌ [PDP加载器] 所有重试都失败，返回最后获取的内容")
                        return page_content
                        
            except Exception as e:
                error_msg = str(e)
                logger.error(f"❌ [PDP加载器] 第{attempt}次尝试异常: {error_msg}")

                # 检查是否是会话失效错误
                if self._is_session_invalid_error(error_msg):
                    logger.warning("⚠️ [PDP加载器] 检测到会话失效错误，需要重新创建浏览器实例")
                    # 这里我们不能直接重建浏览器，因为浏览器实例是外部传入的
                    # 应该由调用方处理浏览器重建
                    return None

                if attempt < self.max_retries:
                    logger.info(f"🔄 [PDP加载器] {self.retry_interval}秒后重试...")
                    await asyncio.sleep(self.retry_interval)
                    continue
                else:
                    logger.error(f"❌ [PDP加载器] 所有重试都失败")
                    return None

    def _is_session_invalid_error(self, error_msg: str) -> bool:
        """检查是否是会话失效错误"""
        session_error_keywords = [
            'invalid session id',
            'session not created',
            'chrome not reachable',
            'session deleted because of page crash',
            'no such session',
            'session timed out',
            'disconnected: not connected to DevTools'
        ]

        error_lower = error_msg.lower()
        for keyword in session_error_keywords:
            if keyword in error_lower:
                return True
        return False
        
        logger.error(f"❌ [PDP加载器] 加载失败，已达到最大重试次数")
        return None
    
    def _parse_page_content(self, content: str) -> Dict[str, Any]:
        """
        解析页面内容，检查是否包含商品数据或验证码
        
        Args:
            content: 页面HTML内容
            
        Returns:
            解析结果字典
        """
        try:
            result = {
                'has_product_data': False,
                'has_captcha': False,
                'captcha_type': None,
                'page_type': 'unknown',
                'content_length': len(content)
            }
            
            # 检测正常商品页面的关键标识
            product_indicators = [
                '__MODERN_ROUTER_DATA__',  # TikTok的主要数据容器
                'product_id',              # 商品ID
                'product_detail',          # 商品详情
                'seller_id',               # 卖家ID
                'shop_name'                # 店铺名称
            ]
            
            product_score = 0
            for indicator in product_indicators:
                if indicator in content:
                    product_score += 1
                    logger.debug(f"🔍 [页面解析] 发现商品指示器: {indicator}")
            
            # 如果找到2个或以上指示器，认为是正常商品页面
            if product_score >= 2:
                result['has_product_data'] = True
                result['page_type'] = 'product'
                logger.debug(f"✅ [页面解析] 检测到商品数据 (评分: {product_score}/5)")
            
            # 检测验证码页面的关键标识
            captcha_indicators = [
                'captcha-config',          # TikTok验证码配置
                'captcha',                 # 通用验证码关键词
                'verify',                  # 验证关键词
                'challenge',               # 挑战关键词
                'slider',                  # 滑块验证
                'puzzle'                   # 拼图验证
            ]
            
            for indicator in captcha_indicators:
                if indicator in content.lower():
                    result['has_captcha'] = True
                    result['captcha_type'] = indicator
                    result['page_type'] = 'captcha'
                    logger.debug(f"🚫 [页面解析] 检测到验证码指示器: {indicator}")
                    break
            
            # 检测页面标题和URL中的验证码关键词
            try:
                title = self.driver.title.lower() if self.driver else ""
                url = self.driver.current_url.lower() if self.driver else ""
                
                captcha_keywords = ['captcha', 'verify', 'challenge']
                for keyword in captcha_keywords:
                    if keyword in title or keyword in url:
                        result['has_captcha'] = True
                        result['captcha_type'] = f'url_{keyword}'
                        result['page_type'] = 'captcha'
                        logger.debug(f"🚫 [页面解析] 在标题/URL中发现验证码: {keyword}")
                        break
            except:
                pass
            
            logger.debug(f"📊 [页面解析] 解析结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ [页面解析] 解析页面内容失败: {str(e)}")
            return {
                'has_product_data': False,
                'has_captcha': False,
                'captcha_type': None,
                'page_type': 'error',
                'content_length': len(content) if content else 0,
                'error': str(e)
            }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'max_retries': self.max_retries,
            'fixed_wait_time': self.fixed_wait_time,
            'retry_interval': self.retry_interval,
            'expected_normal_time': f"{self.fixed_wait_time}-{self.fixed_wait_time + 2}秒",
            'expected_max_time': f"{self.max_retries * (self.fixed_wait_time + self.retry_interval)}秒"
        }
