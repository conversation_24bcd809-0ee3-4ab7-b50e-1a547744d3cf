{"metadata": {"timestamp": "2025-07-31T15:55:02.541422", "total_requests": 2, "total_responses": 2, "responses_with_body": 2, "target_patterns": ["product_list", "brandy_desktop/store", "api/shop", "httpbin.org", "json"]}, "data": {"requests": [{"requestId": "13BD0DB6716532F8EA7EA2EC11A24039", "url": "https://httpbin.org/json", "method": "GET", "headers": {"Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "timestamp": 1753948497785, "postData": null}, {"requestId": "35208.2", "url": "https://httpbin.org/favicon.ico", "method": "GET", "headers": {"Referer": "https://httpbin.org/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "timestamp": 1753948502523, "postData": null}], "responses": [{"requestId": "13BD0DB6716532F8EA7EA2EC11A24039", "url": "https://httpbin.org/json", "status": 200, "statusText": "", "headers": {"access-control-allow-credentials": "true", "access-control-allow-origin": "*", "content-length": "429", "content-type": "application/json", "date": "Thu, 31 Jul 2025 07:54:59 GMT", "server": "gunicorn/19.9.0"}, "mimeType": "application/json", "timestamp": 1753948499471, "request": {"requestId": "13BD0DB6716532F8EA7EA2EC11A24039", "url": "https://httpbin.org/json", "method": "GET", "headers": {"Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "timestamp": 1753948497785, "postData": null}, "body": "{\n  \"slideshow\": {\n    \"author\": \"Yours Truly\", \n    \"date\": \"date of publication\", \n    \"slides\": [\n      {\n        \"title\": \"Wake up to WonderWidgets!\", \n        \"type\": \"all\"\n      }, \n      {\n        \"items\": [\n          \"Why <em>WonderWidgets</em> are great\", \n          \"Who <em>buys</em> WonderWidgets\"\n        ], \n        \"title\": \"Overview\", \n        \"type\": \"all\"\n      }\n    ], \n    \"title\": \"Sample Slide Show\"\n  }\n}\n", "body_length": 429}, {"requestId": "35208.2", "url": "https://httpbin.org/favicon.ico", "status": 404, "statusText": "", "headers": {"access-control-allow-credentials": "true", "access-control-allow-origin": "*", "content-length": "233", "content-type": "text/html", "date": "Thu, 31 Jul 2025 07:55:00 GMT", "server": "gunicorn/19.9.0"}, "mimeType": "text/html", "timestamp": 1753948502523, "request": {"requestId": "35208.2", "url": "https://httpbin.org/favicon.ico", "method": "GET", "headers": {"Referer": "https://httpbin.org/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "timestamp": 1753948502523, "postData": null}, "body": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 3.2 Final//EN\">\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>\n", "body_length": 233}], "summary": {"total_requests": 2, "total_responses": 2, "target_patterns": ["product_list", "brandy_desktop/store", "api/shop", "httpbin.org", "json"]}}}