#!/usr/bin/env python3
"""
测试TikTok爬虫集成网络拦截功能
在现有的TikTok Shop爬虫中启用网络拦截，捕获"View more"按钮的API响应
"""

import sys
import asyncio
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawler.tiktok_crawler import TikTokShopCrawler
from src.crawler.crawler_modes import CrawlerMode


async def test_tiktok_crawler_with_network_interception():
    """测试TikTok爬虫的网络拦截功能"""
    print("🚀 测试TikTok爬虫网络拦截功能")
    print("=" * 50)
    
    # 目标店铺URL
    shop_url = "https://www.tiktok.com/shop/store/xiaomian-department-store/7496209819680934905"
    
    print(f"🎯 测试目标:")
    print(f"   店铺URL: {shop_url}")
    print(f"   功能: 在现有爬虫中启用网络拦截")
    print(f"   目标: 捕获'View more'按钮的API响应")
    print(f"   输出: 保存原始JSON响应数据")
    print()
    
    try:
        # 创建TikTok爬虫实例
        print("🔧 初始化TikTok Shop爬虫...")
        crawler = TikTokShopCrawler(crawler_mode=CrawlerMode.BROWSER)
        
        # 启用网络拦截模式
        print("🕸️ 启用网络拦截模式...")
        crawler.enable_network_interception_mode(True, "intercepted_api_data")
        
        if crawler.enable_network_interception:
            print("✅ 网络拦截模式已启用")
        else:
            print("❌ 网络拦截模式启用失败")
            return False
        
        print(f"\n📡 拦截配置:")
        if crawler.network_interceptor:
            for pattern in crawler.network_interceptor.target_patterns:
                print(f"   • {pattern}")
        
        # 使用现有的店铺爬取方法
        print(f"\n🛒 开始爬取店铺页面...")
        
        # 设置过滤条件
        filter_conditions = {
            "min_sales": 1,
            "max_load_more": 3  # 限制点击次数
        }
        
        # 调用现有的店铺爬取方法
        content = await crawler.get_shop_page_with_zero_sales_detection(
            shop_url, 
            filter_conditions=filter_conditions
        )
        
        if content:
            print(f"✅ 店铺页面爬取成功，内容长度: {len(content)} 字符")
            
            # 手动保存拦截的数据
            print(f"\n💾 保存拦截的API响应数据...")
            saved_file = crawler.save_intercepted_data_to_json()
            
            if saved_file:
                print(f"✅ API响应数据已保存: {saved_file}")
                
                # 验证保存的文件
                saved_path = Path(saved_file)
                if saved_path.exists():
                    file_size = saved_path.stat().st_size
                    print(f"📊 文件信息:")
                    print(f"   文件路径: {saved_path}")
                    print(f"   文件大小: {file_size} 字节")
                    
                    # 尝试读取并显示文件结构
                    try:
                        import json
                        with open(saved_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        metadata = data.get('metadata', {})
                        print(f"   保存时间: {metadata.get('timestamp', 'Unknown')}")
                        print(f"   总请求数: {metadata.get('total_requests', 0)}")
                        print(f"   总响应数: {metadata.get('total_responses', 0)}")
                        print(f"   包含响应体的响应数: {metadata.get('responses_with_body', 0)}")
                        
                        # 显示响应详情
                        responses = data.get('data', {}).get('responses', [])
                        if responses:
                            print(f"\n📋 拦截的API响应:")
                            for i, response in enumerate(responses):
                                url = response.get('url', 'Unknown')
                                status = response.get('status', 'Unknown')
                                has_body = bool(response.get('body'))
                                body_length = response.get('body_length', 0)
                                
                                print(f"   响应 {i+1}:")
                                print(f"     URL: {url[:60]}...")
                                print(f"     状态: {status}")
                                print(f"     响应体: {'✅ 有' if has_body else '❌ 无'} ({body_length} 字符)")
                                
                                if has_body and body_length > 0:
                                    # 显示响应体的前100个字符
                                    body_preview = response.get('body', '')[:100]
                                    print(f"     内容预览: {body_preview}...")
                        else:
                            print(f"⚠️ 未找到API响应数据")
                            
                    except Exception as e:
                        print(f"⚠️ 读取保存文件时出错: {str(e)}")
                else:
                    print(f"❌ 保存的文件不存在: {saved_path}")
            else:
                print(f"⚠️ 没有拦截到API响应数据")
            
            return True
        else:
            print(f"❌ 店铺页面爬取失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        try:
            if 'crawler' in locals():
                print(f"\n🔒 清理爬虫资源...")
                crawler.enable_network_interception_mode(False)
                await crawler.close()
        except Exception as e:
            print(f"⚠️ 清理资源时出错: {str(e)}")


async def main():
    """主测试函数"""
    try:
        print("🎉 TikTok Shop爬虫网络拦截功能测试")
        print("=" * 60)
        
        success = await test_tiktok_crawler_with_network_interception()
        
        print(f"\n" + "=" * 60)
        if success:
            print("🎉 TikTok爬虫网络拦截功能测试成功！")
            print("\n✅ 验证结果:")
            print("   • 网络拦截功能已成功集成到现有TikTok爬虫")
            print("   • 能够在'View more'按钮点击时捕获API响应")
            print("   • 原始JSON响应数据已保存到文件")
            print("   • 数据结构完整，包含请求和响应信息")
            
            print("\n💡 使用方法:")
            print("   1. 在TikTok爬虫中调用 enable_network_interception_mode(True)")
            print("   2. 正常执行店铺爬取操作")
            print("   3. 系统会自动拦截API响应并保存到JSON文件")
            print("   4. 可以手动调用 save_intercepted_data_to_json() 保存数据")
            
            print("\n📁 输出文件:")
            print("   • intercepted_api_data/tiktok_shop_intercepted_YYYYMMDD_HHMMSS.json")
            print("   • 包含完整的API请求和响应数据")
            print("   • 可用于分析TikTok Shop的API结构")
        else:
            print("❌ TikTok爬虫网络拦截功能测试失败")
            print("请检查错误信息并解决相关问题")
        
        return success
        
    except Exception as e:
        print(f"❌ 主测试函数执行失败: {str(e)}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
