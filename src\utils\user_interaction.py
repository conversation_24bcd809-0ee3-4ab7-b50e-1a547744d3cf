"""
用户交互管理器 - 处理验证码和模式切换的用户交互
"""

import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from loguru import logger

try:
    from PyQt6.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QProgressBar, QApplication
    from PyQt6.QtCore import QTimer, pyqtSignal, QObject, QThread
    from PyQt6.QtGui import QPixmap, QIcon
    HAS_QT = True
    QT_VERSION = 6
except ImportError:
    try:
        from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QProgressBar, QApplication
        from PyQt5.QtCore import QTimer, pyqtSignal, QObject, QThread
        from PyQt5.QtGui import QPixmap, QIcon
        HAS_QT = True
        QT_VERSION = 5
    except ImportError:
        logger.warning("⚠️ [用户交互] PyQt未安装，将使用控制台交互")
        HAS_QT = False
        QT_VERSION = 0


def ensure_qapplication():
    """确保QApplication实例存在"""
    if not HAS_QT:
        return None

    try:
        app = QApplication.instance()
        if app is None:
            # 创建QApplication实例
            import sys
            app = QApplication(sys.argv if hasattr(sys, 'argv') else [])
            logger.info("🎨 [用户交互] 创建QApplication实例")
        return app
    except Exception as e:
        logger.error(f"❌ [用户交互] 创建QApplication失败: {str(e)}")
        return None


class InteractionType(Enum):
    """交互类型枚举"""
    CAPTCHA_DETECTED = "captcha_detected"
    CAPTCHA_COMPLETED = "captcha_completed"
    MODE_SWITCHED = "mode_switched"
    USER_CONFIRMATION = "user_confirmation"
    PROGRESS_UPDATE = "progress_update"
    BROWSER_INITIALIZED = "browser_initialized"
    INITIALIZATION_PROGRESS = "initialization_progress"


class InteractionEvent:
    """交互事件"""
    
    def __init__(self, event_type: InteractionType, data: Dict[str, Any]):
        self.event_type = event_type
        self.data = data
        self.timestamp = time.time()
        self.handled = False


class CaptchaDialog(QDialog if HAS_QT else object):
    """验证码处理对话框"""
    
    def __init__(self, parent=None):
        if not HAS_QT:
            return
        
        super().__init__(parent)
        self.setWindowTitle("验证码检测")
        self.setModal(False)  # 非模态，允许用户操作浏览器
        self.resize(400, 200)
        
        self.setup_ui()
        self.is_completed = False
        
        # 定时器检查验证码状态
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(self.check_captcha_status)
        self.check_timer.start(2000)  # 每2秒检查一次
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("🚫 检测到验证码")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e74c3c;")
        layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel(
            "系统检测到验证码，请在浏览器窗口中完成验证。\n"
            "完成验证后，系统将自动继续爬取任务。"
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("等待用户完成验证...")
        layout.addWidget(self.status_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.pause_button = QPushButton("暂停任务")
        self.pause_button.clicked.connect(self.pause_task)
        button_layout.addWidget(self.pause_button)
        
        self.continue_button = QPushButton("强制继续")
        self.continue_button.clicked.connect(self.force_continue)
        button_layout.addWidget(self.continue_button)
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def check_captcha_status(self):
        """检查验证码状态（需要外部实现）"""
        # 这个方法需要与爬虫配合实现
        pass
    
    def captcha_completed(self):
        """验证码完成"""
        self.is_completed = True
        self.status_label.setText("✅ 验证码已完成！")
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(100)
        self.check_timer.stop()
        
        # 3秒后自动关闭
        QTimer.singleShot(3000, self.close)
    
    def pause_task(self):
        """暂停任务"""
        self.status_label.setText("⏸️ 任务已暂停")
        # 这里可以发送暂停信号
    
    def force_continue(self):
        """强制继续"""
        self.status_label.setText("⚡ 强制继续任务")
        self.close()


class UserInteractionManager:
    """用户交互管理器"""
    
    def __init__(self, use_gui: bool = True):
        """
        初始化用户交互管理器

        Args:
            use_gui: 是否使用GUI界面
        """
        # 智能检测GUI可用性
        self.use_gui = use_gui and HAS_QT and self._check_gui_available()
        self.event_queue: List[InteractionEvent] = []
        self.active_dialogs: Dict[str, Any] = {}

        if use_gui and not self.use_gui:
            logger.warning("⚠️ [用户交互] GUI不可用，将使用控制台模式")

        # 回调函数
        self.callbacks: Dict[InteractionType, List[Callable]] = {
            InteractionType.CAPTCHA_DETECTED: [],
            InteractionType.CAPTCHA_COMPLETED: [],
            InteractionType.MODE_SWITCHED: [],
            InteractionType.USER_CONFIRMATION: [],
            InteractionType.PROGRESS_UPDATE: [],
            InteractionType.BROWSER_INITIALIZED: [],
            InteractionType.INITIALIZATION_PROGRESS: []
        }

    def _check_gui_available(self) -> bool:
        """检查GUI是否可用"""
        if not HAS_QT:
            return False

        try:
            # 尝试获取或创建QApplication实例
            app = ensure_qapplication()
            return app is not None
        except Exception as e:
            logger.debug(f"GUI可用性检查失败: {str(e)}")
            return False
        
        logger.info(f"🎭 [用户交互] 初始化完成，GUI模式: {self.use_gui}")
    
    def register_callback(self, event_type: InteractionType, callback: Callable):
        """注册回调函数"""
        self.callbacks[event_type].append(callback)
        logger.debug(f"📝 [用户交互] 注册回调: {event_type.value}")
    
    async def handle_interaction(self, data: Dict[str, Any]) -> Any:
        """处理用户交互"""
        try:
            interaction_type = data.get('type', '')
            
            if interaction_type == 'captcha_detected':
                return await self._handle_captcha_detected(data)
            elif interaction_type == 'captcha_completed':
                return await self._handle_captcha_completed(data)
            elif interaction_type == 'mode_switched':
                return await self._handle_mode_switched(data)
            elif interaction_type == 'user_confirmation':
                return await self._handle_user_confirmation(data)
            elif interaction_type == 'browser_initialized':
                return await self._handle_browser_initialized(data)
            elif interaction_type == 'initialization_progress':
                return await self._handle_initialization_progress(data)
            else:
                logger.warning(f"⚠️ [用户交互] 未知交互类型: {interaction_type}")
                return None
                
        except Exception as e:
            logger.error(f"❌ [用户交互] 处理交互失败: {str(e)}")
            return None
    
    async def _handle_captcha_detected(self, data: Dict[str, Any]) -> Any:
        """处理验证码检测"""
        try:
            message = data.get('message', '检测到验证码')
            logger.warning(f"🚫 [用户交互] {message}")
            
            if self.use_gui:
                # 确保QApplication存在
                app = ensure_qapplication()
                if app:
                    # 显示GUI对话框
                    dialog = CaptchaDialog()
                    dialog.show()
                    self.active_dialogs['captcha'] = dialog

                    logger.info("👁️ [用户交互] 已显示验证码处理对话框")
                else:
                    # 降级到控制台模式
                    logger.warning("⚠️ [用户交互] GUI不可用，降级到控制台模式")
                    print(f"\n🚫 {message}")
                    print("请在浏览器窗口中完成验证码，然后按回车键继续...")
                    input()
            else:
                # 控制台提示
                print(f"\n🚫 {message}")
                print("请在浏览器窗口中完成验证码，然后按回车键继续...")
                input()
            
            # 执行回调
            for callback in self.callbacks[InteractionType.CAPTCHA_DETECTED]:
                try:
                    await callback(data)
                except Exception as e:
                    logger.error(f"❌ [用户交互] 回调执行失败: {str(e)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [用户交互] 处理验证码检测失败: {str(e)}")
            return False
    
    async def _handle_captcha_completed(self, data: Dict[str, Any]) -> Any:
        """处理验证码完成"""
        try:
            message = data.get('message', '验证码已完成')
            logger.info(f"✅ [用户交互] {message}")
            
            # 关闭验证码对话框
            if 'captcha' in self.active_dialogs:
                dialog = self.active_dialogs['captcha']
                if hasattr(dialog, 'captcha_completed'):
                    dialog.captcha_completed()
                del self.active_dialogs['captcha']
            
            # 执行回调
            for callback in self.callbacks[InteractionType.CAPTCHA_COMPLETED]:
                try:
                    await callback(data)
                except Exception as e:
                    logger.error(f"❌ [用户交互] 回调执行失败: {str(e)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [用户交互] 处理验证码完成失败: {str(e)}")
            return False
    
    async def _handle_mode_switched(self, data: Dict[str, Any]) -> Any:
        """处理模式切换"""
        try:
            from_mode = data.get('from_mode', '')
            to_mode = data.get('to_mode', '')
            reason = data.get('reason', '')
            
            message = f"模式切换: {from_mode} -> {to_mode}"
            if reason:
                message += f" (原因: {reason})"
            
            logger.info(f"🔄 [用户交互] {message}")
            
            if self.use_gui:
                # 可以显示一个简短的通知
                pass
            else:
                print(f"\n🔄 {message}")
            
            # 执行回调
            for callback in self.callbacks[InteractionType.MODE_SWITCHED]:
                try:
                    await callback(data)
                except Exception as e:
                    logger.error(f"❌ [用户交互] 回调执行失败: {str(e)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [用户交互] 处理模式切换失败: {str(e)}")
            return False
    
    async def _handle_user_confirmation(self, data: Dict[str, Any]) -> Any:
        """处理用户确认"""
        try:
            message = data.get('message', '需要用户确认')
            default_response = data.get('default', True)
            
            logger.info(f"❓ [用户交互] {message}")
            
            if self.use_gui:
                # 确保QApplication存在
                app = ensure_qapplication()
                if app:
                    # 显示确认对话框
                    reply = QMessageBox.question(
                        None, "用户确认", message,
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.Yes if default_response else QMessageBox.StandardButton.No
                    )
                    result = reply == QMessageBox.StandardButton.Yes
                else:
                    # 降级到控制台模式
                    logger.warning("⚠️ [用户交互] GUI不可用，降级到控制台模式")
                    response = input(f"\n❓ {message} (y/n, 默认{'y' if default_response else 'n'}): ").strip().lower()
                    if not response:
                        result = default_response
                    else:
                        result = response in ['y', 'yes', '是', '确定']
            else:
                # 控制台确认
                response = input(f"\n❓ {message} (y/n, 默认{'y' if default_response else 'n'}): ").strip().lower()
                if not response:
                    result = default_response
                else:
                    result = response in ['y', 'yes', '是', '确定']
            
            logger.info(f"✅ [用户交互] 用户选择: {'确认' if result else '取消'}")
            return result
            
        except Exception as e:
            logger.error(f"❌ [用户交互] 处理用户确认失败: {str(e)}")
            return False

    async def _handle_browser_initialized(self, data: Dict[str, Any]) -> Any:
        """处理浏览器初始化完成"""
        try:
            message = data.get('message', '浏览器初始化完成')
            elapsed_time = data.get('elapsed_time', 0)

            logger.info(f"🚀 [用户交互] {message}")

            if not self.use_gui:
                print(f"\n🚀 {message}")

            # 执行回调
            for callback in self.callbacks[InteractionType.BROWSER_INITIALIZED]:
                try:
                    await callback(data)
                except Exception as e:
                    logger.error(f"❌ [用户交互] 回调执行失败: {str(e)}")

            return True

        except Exception as e:
            logger.error(f"❌ [用户交互] 处理浏览器初始化完成失败: {str(e)}")
            return False

    async def _handle_initialization_progress(self, data: Dict[str, Any]) -> Any:
        """处理初始化进度更新"""
        try:
            message = data.get('message', '初始化中...')
            progress = data.get('progress', 0)

            logger.debug(f"🔄 [用户交互] [{progress}%] {message}")

            if not self.use_gui:
                print(f"\r🔄 [{progress:3d}%] {message}", end='', flush=True)
                if progress >= 100:
                    print()  # 换行

            # 执行回调
            for callback in self.callbacks[InteractionType.INITIALIZATION_PROGRESS]:
                try:
                    await callback(data)
                except Exception as e:
                    logger.error(f"❌ [用户交互] 回调执行失败: {str(e)}")

            return True

        except Exception as e:
            logger.error(f"❌ [用户交互] 处理初始化进度失败: {str(e)}")
            return False
    
    def close_all_dialogs(self):
        """关闭所有对话框"""
        try:
            for dialog_id, dialog in list(self.active_dialogs.items()):
                try:
                    if hasattr(dialog, 'close'):
                        dialog.close()
                except:
                    pass
                del self.active_dialogs[dialog_id]
            
            logger.info("🔒 [用户交互] 已关闭所有对话框")
            
        except Exception as e:
            logger.error(f"❌ [用户交互] 关闭对话框失败: {str(e)}")


# 全局用户交互管理器实例
_interaction_manager = None

def get_interaction_manager(use_gui: bool = True) -> UserInteractionManager:
    """获取全局用户交互管理器实例"""
    global _interaction_manager
    if _interaction_manager is None:
        _interaction_manager = UserInteractionManager(use_gui)
    return _interaction_manager
