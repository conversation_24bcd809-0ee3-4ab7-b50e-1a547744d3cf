# TikTok Shop 网络拦截功能使用指南

## 🎯 功能概述

TikTok Shop爬虫项目的网络拦截功能是一个强大的HTTP请求拦截系统，专门用于捕获"加载更多"按钮触发的API响应数据。该功能突破了传统HTML解析的限制，直接获取API级别的结构化数据。

## ✨ 核心特性

### 🌐 浏览器支持
- ✅ **Chrome优先策略** - 自动检测并优先使用Google Chrome
- ✅ **Edge备选支持** - Chrome不可用时自动切换到Microsoft Edge
- ✅ **统一驱动管理** - 两种浏览器都使用ChromeDriver
- ✅ **版本自动匹配** - 自动下载对应版本的ChromeDriver

### 🕸️ 网络拦截技术
- ✅ **Chrome DevTools Protocol (CDP)** - 使用CDP进行网络监控
- ✅ **实时请求拦截** - 捕获所有HTTP请求和响应
- ✅ **智能过滤** - 只拦截目标API请求
- ✅ **完整响应数据** - 获取完整的JSON响应内容

### 🤖 自动化功能
- ✅ **智能按钮识别** - 自动识别"加载更多"按钮
- ✅ **自动点击操作** - 模拟用户点击行为
- ✅ **等待机制** - 智能等待页面和数据加载
- ✅ **批量处理** - 支持多次点击和批量店铺爬取

### 💾 数据处理
- ✅ **实时数据解析** - 即时解析API响应数据
- ✅ **内存存储集成** - 与MemoryStorage系统无缝集成
- ✅ **数据去重** - 自动去除重复商品数据
- ✅ **多格式导出** - 支持JSON、CSV、Excel格式导出

## 🏗️ 架构设计

### 核心组件

1. **NetworkInterceptor** - 网络拦截器核心
2. **EnhancedBrowserManager** - 增强版浏览器管理器
3. **InterceptedDataProcessor** - 拦截数据处理器
4. **NetworkCrawlerManager** - 网络拦截爬虫管理器

### 数据流程

```
TikTok Shop页面 → 点击"加载更多" → API请求 → CDP拦截 → 数据解析 → 内存存储 → 数据导出
```

## 🚀 快速开始

### 基本使用

```python
from src.utils.memory_storage import MemoryStorage
from src.crawler.network_crawler_manager import NetworkCrawlerManager

# 创建内存存储
memory_storage = MemoryStorage()

# 创建网络拦截爬虫管理器
with NetworkCrawlerManager(memory_storage) as crawler:
    # 设置爬虫环境
    crawler.setup_crawler(enable_interception=True)
    
    # 爬取单个店铺
    result = crawler.crawl_shop_with_load_more(
        shop_url="https://www.tiktok.com/@shop_example",
        max_load_more=5
    )
    
    # 导出数据
    crawler.export_crawled_data('json', 'products.json')
```

### 批量爬取

```python
# 批量爬取多个店铺
shop_urls = [
    "https://www.tiktok.com/@shop1",
    "https://www.tiktok.com/@shop2",
    "https://www.tiktok.com/@shop3"
]

results = crawler.crawl_multiple_shops(shop_urls, max_load_more=3)

for result in results:
    print(f"店铺: {result['shop_url']}")
    print(f"提取商品数: {result['products_extracted']}")
```

## 🔧 高级配置

### 自定义拦截模式

```python
# 添加自定义拦截模式
crawler.browser_manager.add_interception_pattern("custom_api_pattern")

# 添加响应回调
def custom_response_handler(response_data):
    print(f"拦截到响应: {response_data['url']}")

crawler.browser_manager.add_response_callback(custom_response_handler)
```

### 浏览器配置

```python
# 获取浏览器信息
browser_info = crawler.browser_manager.get_browser_info()
print(f"使用浏览器: {browser_info['browser_name']}")

# 手动导航
crawler.browser_manager.navigate_to("https://example.com")

# 等待加载更多按钮
button_found = crawler.browser_manager.wait_for_load_more_button(timeout=10)
```

## 📊 数据结构

### API响应数据结构

拦截的TikTok Shop API响应通常包含以下结构：

```json
{
  "data": {
    "products": [
      {
        "id": "product_id",
        "title": "商品标题",
        "price": 99.99,
        "original_price": 199.99,
        "image": "图片URL",
        "shop_id": "店铺ID",
        "shop_name": "店铺名称"
      }
    ]
  }
}
```

### 处理结果结构

```python
{
    'success': True,
    'shop_url': 'https://www.tiktok.com/@shop',
    'load_more_clicks': 3,
    'api_responses': 5,
    'products_extracted': 150,
    'errors': 0,
    'saved_file': 'path/to/saved/data.json'
}
```

## 🛠️ 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome/Edge是否正确安装
   - 验证ChromeDriver版本匹配
   - 确保有足够的系统权限

2. **网络拦截失败**
   - 确认CDP功能已启用
   - 检查目标URL是否正确
   - 验证网络连接状态

3. **按钮识别失败**
   - 检查页面是否完全加载
   - 验证按钮选择器是否正确
   - 增加等待时间

### 调试模式

```python
# 启用详细日志
import logging
logging.getLogger().setLevel(logging.DEBUG)

# 获取详细统计信息
stats = crawler.get_crawl_statistics()
print(f"详细统计: {stats}")
```

## ⚠️ 注意事项

### 合规使用
- 遵守TikTok的使用条款和robots.txt
- 合理设置请求间隔，避免过于频繁的请求
- 尊重网站的反爬虫机制

### 性能优化
- 定期清理内存存储以避免内存溢出
- 合理设置max_load_more参数
- 在生产环境中添加适当的错误处理和重试机制

### 数据安全
- 妥善保管爬取的数据
- 遵守相关的数据保护法规
- 不要爬取敏感或私人信息

## 🔄 更新日志

### v1.0.0 (2025-07-31)
- ✅ 初始版本发布
- ✅ Chrome/Edge双浏览器支持
- ✅ CDP网络拦截功能
- ✅ 自动"加载更多"按钮识别
- ✅ 与MemoryStorage系统集成
- ✅ 多格式数据导出

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志获取详细错误信息
3. 确保所有依赖项正确安装
4. 验证浏览器和驱动版本匹配

## 🎯 商业化价值

### 核心优势
- **突破传统限制** - 直接获取API级别的结构化数据
- **提高数据质量** - 避免HTML解析的不稳定性
- **支持动态内容** - 完整抓取动态加载的商品数据
- **企业级能力** - 提供可靠的数据处理和存储机制
- **架构兼容性** - 与现有系统无缝集成

### 应用场景
- 电商数据分析
- 市场研究
- 价格监控
- 竞品分析
- 数据挖掘

---

*本文档持续更新中，如有疑问请参考源代码注释或联系技术支持。*
