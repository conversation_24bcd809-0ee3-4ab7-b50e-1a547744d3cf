"""
拦截数据处理器
处理网络拦截的API响应数据，解析并集成到MemoryStorage系统
与现有的数据去重和存储机制无缝集成
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from decimal import Decimal
from loguru import logger

from ..models.product import Product, Currency, ProductStatus
from ..models.shop import Shop, ShopType, ShopStatus
from .memory_storage import MemoryStorage


class InterceptedDataProcessor:
    """拦截数据处理器"""
    
    def __init__(self, memory_storage: MemoryStorage):
        """
        初始化数据处理器
        
        Args:
            memory_storage: 内存存储实例
        """
        self.memory_storage = memory_storage
        self.processed_count = 0
        self.error_count = 0
        
        logger.info("🔄 [数据处理器] 初始化完成")
    
    def process_intercepted_responses(self, responses: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        处理拦截的响应数据
        
        Args:
            responses: 拦截的响应数据列表
            
        Returns:
            Dict: 处理结果统计
        """
        results = {
            'total_responses': len(responses),
            'processed_products': 0,
            'processed_shops': 0,
            'errors': 0
        }
        
        logger.info(f"🔄 [数据处理器] 开始处理 {len(responses)} 个响应")
        
        for response in responses:
            try:
                # 获取响应体
                response_body = self._extract_response_body(response)
                if not response_body:
                    continue
                
                # 解析JSON数据
                json_data = self._parse_json_response(response_body)
                if not json_data:
                    continue
                
                # 根据URL类型处理数据
                url = response.get('url', '')
                if 'product_list' in url:
                    product_count = self._process_product_list_response(json_data)
                    results['processed_products'] += product_count
                elif 'store' in url:
                    shop_count = self._process_store_response(json_data)
                    results['processed_shops'] += shop_count
                
            except Exception as e:
                logger.error(f"❌ [数据处理器] 处理响应失败: {str(e)}")
                results['errors'] += 1
        
        logger.info(f"✅ [数据处理器] 处理完成 - 商品: {results['processed_products']}, 店铺: {results['processed_shops']}, 错误: {results['errors']}")
        return results
    
    def _extract_response_body(self, response: Dict[str, Any]) -> Optional[str]:
        """提取响应体内容"""
        try:
            # 优先从body字段获取
            if 'body' in response and response['body']:
                body = response['body']
                logger.debug(f"✅ [数据处理器] 从body字段提取响应体: {len(body)} 字符")
                return body

            # 备选：从content字段获取
            elif 'content' in response and response['content']:
                content = response['content']
                logger.debug(f"✅ [数据处理器] 从content字段提取响应体: {len(content)} 字符")
                return content

            # 备选：从responseBody字段获取
            elif 'responseBody' in response and response['responseBody']:
                response_body = response['responseBody']
                logger.debug(f"✅ [数据处理器] 从responseBody字段提取响应体: {len(response_body)} 字符")
                return response_body

            else:
                # 记录响应结构用于调试
                available_keys = list(response.keys())
                logger.warning(f"⚠️ [数据处理器] 响应中未找到body内容，可用字段: {available_keys}")

                # 尝试从嵌套结构中查找
                for key in ['data', 'result', 'response']:
                    if key in response and isinstance(response[key], dict):
                        nested_body = self._extract_response_body(response[key])
                        if nested_body:
                            logger.debug(f"✅ [数据处理器] 从嵌套字段 {key} 提取响应体")
                            return nested_body

                return None

        except Exception as e:
            logger.error(f"❌ [数据处理器] 提取响应体失败: {str(e)}")
            return None
    
    def _parse_json_response(self, response_body: str) -> Optional[Dict[str, Any]]:
        """解析JSON响应"""
        try:
            if isinstance(response_body, str):
                return json.loads(response_body)
            elif isinstance(response_body, dict):
                return response_body
            else:
                logger.debug("⚠️ [数据处理器] 响应体格式不支持")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ [数据处理器] JSON解析失败: {str(e)}")
            return None
    
    def _process_product_list_response(self, json_data: Dict[str, Any]) -> int:
        """处理商品列表响应"""
        processed_count = 0
        
        try:
            # 根据TikTok Shop API结构提取商品数据
            # 这里需要根据实际API响应结构调整
            products_data = self._extract_products_from_response(json_data)
            
            for product_data in products_data:
                try:
                    product = self._create_product_from_data(product_data)
                    if product:
                        success = self.memory_storage.save_product(product)
                        if success:
                            processed_count += 1
                            logger.debug(f"💾 [数据处理器] 商品已保存: {product.product_id}")
                        
                except Exception as e:
                    logger.error(f"❌ [数据处理器] 处理单个商品失败: {str(e)}")
                    continue
            
            logger.info(f"✅ [数据处理器] 商品列表处理完成: {processed_count} 个商品")
            return processed_count
            
        except Exception as e:
            logger.error(f"❌ [数据处理器] 处理商品列表响应失败: {str(e)}")
            return 0
    
    def _extract_products_from_response(self, json_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取商品数据"""
        try:
            # TikTok Shop API可能的数据结构
            possible_paths = [
                ['data', 'products'],
                ['data', 'items'],
                ['products'],
                ['items'],
                ['data', 'product_list'],
                ['product_list']
            ]
            
            for path in possible_paths:
                current = json_data
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        current = None
                        break
                
                if current and isinstance(current, list):
                    logger.debug(f"🎯 [数据处理器] 找到商品数据路径: {' -> '.join(path)}")
                    return current
            
            logger.warning("⚠️ [数据处理器] 未找到商品数据")
            return []
            
        except Exception as e:
            logger.error(f"❌ [数据处理器] 提取商品数据失败: {str(e)}")
            return []
    
    def _create_product_from_data(self, product_data: Dict[str, Any]) -> Optional[Product]:
        """从API数据创建Product对象"""
        try:
            # 提取基本信息
            product_id = str(product_data.get('id') or product_data.get('product_id') or product_data.get('productId', ''))
            if not product_id:
                logger.warning("⚠️ [数据处理器] 商品缺少ID")
                return None
            
            title = product_data.get('title') or product_data.get('name') or product_data.get('product_name', '')
            description = product_data.get('description', '')
            
            # 提取价格信息
            price_info = self._extract_price_info(product_data)
            
            # 提取分类信息
            category = product_data.get('category') or product_data.get('category_name', '')
            subcategory = product_data.get('subcategory') or product_data.get('sub_category', '')
            
            # 提取品牌信息
            brand = product_data.get('brand') or product_data.get('brand_name', '')
            
            # 提取库存和销量
            stock = product_data.get('stock') or product_data.get('inventory', 0)
            sold_count = product_data.get('sold_count') or product_data.get('sales_count', 0)
            
            # 提取图片信息
            main_image_url = self._extract_main_image(product_data)
            
            # 提取店铺信息
            shop_info = self._extract_shop_info(product_data)
            
            # 创建Product对象
            product = Product(
                product_id=product_id,
                title=title,
                description=description,
                category=category,
                subcategory=subcategory,
                brand=brand,
                price=Decimal(str(price_info['current_price'])),
                original_price=Decimal(str(price_info['original_price'])) if price_info['original_price'] else None,
                currency=Currency.USD,  # 默认USD，可根据实际情况调整
                discount_percentage=price_info['discount_percentage'],
                stock=stock,
                sold_count=sold_count,
                main_image_url=main_image_url,
                shop_id=shop_info['shop_id'],
                shop_name=shop_info['shop_name'],
                shop_url=shop_info['shop_url'],
                status=ProductStatus.ACTIVE,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                scraped_at=datetime.now()
            )
            
            return product
            
        except Exception as e:
            logger.error(f"❌ [数据处理器] 创建Product对象失败: {str(e)}")
            return None
    
    def _extract_price_info(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取价格信息"""
        try:
            # 尝试多种可能的价格字段
            current_price = 0.0
            original_price = None
            discount_percentage = None
            
            # 当前价格
            price_fields = ['price', 'current_price', 'sale_price', 'final_price']
            for field in price_fields:
                if field in product_data:
                    current_price = float(product_data[field])
                    break
            
            # 原价
            original_price_fields = ['original_price', 'list_price', 'regular_price']
            for field in original_price_fields:
                if field in product_data:
                    original_price = float(product_data[field])
                    break
            
            # 计算折扣百分比
            if original_price and original_price > current_price:
                discount_percentage = round((1 - current_price / original_price) * 100, 2)
            
            return {
                'current_price': current_price,
                'original_price': original_price,
                'discount_percentage': discount_percentage
            }
            
        except Exception as e:
            logger.error(f"❌ [数据处理器] 提取价格信息失败: {str(e)}")
            return {'current_price': 0.0, 'original_price': None, 'discount_percentage': None}
    
    def _extract_main_image(self, product_data: Dict[str, Any]) -> str:
        """提取主图URL"""
        try:
            # 尝试多种可能的图片字段
            image_fields = ['image', 'main_image', 'thumbnail', 'cover_image']
            
            for field in image_fields:
                if field in product_data:
                    image_data = product_data[field]
                    if isinstance(image_data, str):
                        return image_data
                    elif isinstance(image_data, dict) and 'url' in image_data:
                        return image_data['url']
                    elif isinstance(image_data, list) and image_data:
                        first_image = image_data[0]
                        if isinstance(first_image, str):
                            return first_image
                        elif isinstance(first_image, dict) and 'url' in first_image:
                            return first_image['url']
            
            return ''
            
        except Exception as e:
            logger.error(f"❌ [数据处理器] 提取主图失败: {str(e)}")
            return ''
    
    def _extract_shop_info(self, product_data: Dict[str, Any]) -> Dict[str, str]:
        """提取店铺信息"""
        try:
            shop_id = str(product_data.get('shop_id') or product_data.get('seller_id', ''))
            shop_name = product_data.get('shop_name') or product_data.get('seller_name', '')
            shop_url = product_data.get('shop_url') or product_data.get('seller_url', '')
            
            return {
                'shop_id': shop_id,
                'shop_name': shop_name,
                'shop_url': shop_url
            }
            
        except Exception as e:
            logger.error(f"❌ [数据处理器] 提取店铺信息失败: {str(e)}")
            return {'shop_id': '', 'shop_name': '', 'shop_url': ''}
    
    def _process_store_response(self, json_data: Dict[str, Any]) -> int:
        """处理店铺响应"""
        try:
            # 这里可以添加店铺数据处理逻辑
            # 暂时返回0，表示未处理店铺数据
            logger.debug("ℹ️ [数据处理器] 店铺数据处理功能待实现")
            return 0
            
        except Exception as e:
            logger.error(f"❌ [数据处理器] 处理店铺响应失败: {str(e)}")
            return 0
    
    def get_processing_stats(self) -> Dict[str, int]:
        """获取处理统计信息"""
        return {
            'processed_count': self.processed_count,
            'error_count': self.error_count
        }
