
import os
import sys
import json
import shutil
import platform
import subprocess
import urllib.request
import urllib.error
from pathlib import Path
from typing import Optional, Dict, List, Tuple
from loguru import logger
import zipfile
import tempfile


class ChromeDriverManager:
    """ChromeDriver 管理器 - 支持Chrome和Edge浏览器"""

    def __init__(self):
        self.system = platform.system().lower()
        self.browser_version = None
        self.browser_path = None
        self.browser_name = None  # 'Chrome' 或 'Edge'
        self.driver_cache_dir = Path.home() / ".tiktok_shop_tool" / "drivers"
        self.driver_cache_dir.mkdir(parents=True, exist_ok=True)
        
    def detect_chrome_installation(self) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        检测浏览器安装情况 - 优先Chrome，备选Edge

        Returns:
            Tuple[bool, Optional[str], Optional[str]]: (是否安装, 浏览器路径, 浏览器版本)
        """
        try:
            # 优先检测Chrome浏览器
            chrome_paths = self._get_possible_chrome_paths()

            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    version = self._get_browser_version(chrome_path)
                    if version:
                        self.browser_path = chrome_path
                        self.browser_version = version
                        self.browser_name = "Chrome"
                        logger.info(f"✅ [浏览器检测] 找到Chrome: {chrome_path}, 版本: {version}")
                        return True, chrome_path, version

            # 如果没有找到Chrome，检测Edge浏览器
            logger.info("🔍 [浏览器检测] 未找到Chrome，尝试检测Edge浏览器...")
            edge_paths = self._get_possible_edge_paths()

            for edge_path in edge_paths:
                if os.path.exists(edge_path):
                    version = self._get_browser_version(edge_path)
                    if version:
                        self.browser_path = edge_path
                        self.browser_version = version
                        self.browser_name = "Edge"
                        logger.info(f"✅ [浏览器检测] 找到Edge: {edge_path}, 版本: {version}")
                        return True, edge_path, version

            logger.error("❌ [浏览器检测] 未找到Chrome或Edge浏览器安装")
            return False, None, None

        except Exception as e:
            logger.error(f"❌ [浏览器检测] 检测浏览器安装时出错: {str(e)}")
            return False, None, None
    
    def _get_possible_chrome_paths(self) -> List[str]:
        """获取可能的Chrome安装路径"""
        if self.system == "windows":
            return [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
            ]
        elif self.system == "darwin":  # macOS
            return [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium"
            ]
        else:  # Linux
            return [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/snap/bin/chromium"
            ]

    def _get_possible_edge_paths(self) -> List[str]:
        """获取可能的Edge安装路径"""
        if self.system == "windows":
            return [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge\Application\msedge.exe"),
                r"C:\Users\<USER>\AppData\Local\Microsoft\Edge\Application\msedge.exe"
            ]
        elif self.system == "darwin":  # macOS
            return [
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge"
            ]
        else:  # Linux
            return [
                "/usr/bin/microsoft-edge-stable",
                "/usr/bin/microsoft-edge",
                "/usr/bin/microsoft-edge-beta",
                "/usr/bin/microsoft-edge-dev",
                "/snap/bin/microsoft-edge"
            ]
    
    def _get_browser_version(self, browser_path: str) -> Optional[str]:
        """获取浏览器版本（Chrome或Edge）"""
        try:
            if self.system == "windows":
                # Windows: 使用PowerShell获取版本信息
                cmd = f'Get-ItemProperty "{browser_path}" | Select-Object -ExpandProperty VersionInfo | Select-Object -ExpandProperty ProductVersion'
                result = subprocess.run(
                    ["powershell", "-Command", cmd],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    version = result.stdout.strip()
                    if version:
                        return version

                # 备用方案：直接执行浏览器 --version
                try:
                    result = subprocess.run(
                        [browser_path, "--version"],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    if result.returncode == 0:
                        version_line = result.stdout.strip()
                        # 提取版本号 (例如: "Google Chrome 138.0.7204.158" 或 "Microsoft Edge 138.0.7204.158")
                        parts = version_line.split()
                        for part in parts:
                            if '.' in part and part.replace('.', '').isdigit():
                                return part
                except:
                    pass

            else:
                # macOS/Linux
                result = subprocess.run(
                    [browser_path, "--version"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    version_line = result.stdout.strip()
                    parts = version_line.split()
                    for part in parts:
                        if '.' in part and part.replace('.', '').replace('-', '').isdigit():
                            return part

        except Exception as e:
            logger.debug(f"获取浏览器版本失败: {str(e)}")

        return None
    
    def get_chromedriver_version(self, browser_version: str) -> str:
        """根据浏览器版本获取对应的ChromeDriver版本（Chrome和Edge都使用ChromeDriver）"""
        try:
            # Chrome/Edge 115+ 使用新的版本映射API
            major_version = int(browser_version.split('.')[0])

            if major_version >= 115:
                # 使用Chrome for Testing API（Edge也兼容）
                url = f"https://googlechromelabs.github.io/chrome-for-testing/LATEST_RELEASE_{major_version}"
                try:
                    with urllib.request.urlopen(url, timeout=10) as response:
                        driver_version = response.read().decode('utf-8').strip()
                        browser_name = self.browser_name or "浏览器"
                        logger.info(f"✅ [版本映射] {browser_name} {browser_version} -> ChromeDriver {driver_version}")
                        return driver_version
                except urllib.error.URLError:
                    logger.info(f"⏭️ [版本映射] 跳过在线版本检查，使用本地版本映射")

            # 备用方案：使用浏览器版本的前三位
            version_parts = browser_version.split('.')
            if len(version_parts) >= 3:
                fallback_version = '.'.join(version_parts[:3])
                logger.info(f"💡 [版本映射] 使用备用版本: {fallback_version}")
                return fallback_version

        except Exception as e:
            logger.error(f"❌ [版本映射] 获取ChromeDriver版本失败: {str(e)}")

        # 最终备用方案
        return browser_version
    
    def download_chromedriver(self, driver_version: str) -> Optional[str]:
        """下载ChromeDriver到本地缓存"""
        try:
            # 构建下载URL
            platform_suffix = self._get_platform_suffix()
            if not platform_suffix:
                logger.error("❌ [ChromeDriver下载] 不支持的平台")
                return None
            
            # Chrome 115+ 使用新的下载URL格式
            major_version = int(driver_version.split('.')[0])
            if major_version >= 115:
                download_url = f"https://storage.googleapis.com/chrome-for-testing-public/{driver_version}/{platform_suffix}/chromedriver-{platform_suffix}.zip"
            else:
                download_url = f"https://chromedriver.storage.googleapis.com/{driver_version}/chromedriver_{platform_suffix}.zip"
            
            # 检查本地缓存
            driver_dir = self.driver_cache_dir / driver_version
            driver_executable = driver_dir / ("chromedriver.exe" if self.system == "windows" else "chromedriver")
            
            if driver_executable.exists():
                logger.info(f"✅ [ChromeDriver缓存] 使用缓存的ChromeDriver: {driver_executable}")
                return str(driver_executable)
            
            # 下载ChromeDriver
            logger.info(f"📥 [ChromeDriver下载] 开始下载: {download_url}")
            
            temp_file_path = None
            try:
                with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
                    with urllib.request.urlopen(download_url, timeout=30) as response:
                        shutil.copyfileobj(response, temp_file)
                    temp_file_path = temp_file.name

                # 解压文件
                driver_dir.mkdir(parents=True, exist_ok=True)
                with zipfile.ZipFile(temp_file_path, 'r') as zip_ref:
                    zip_ref.extractall(driver_dir)

                # 查找解压后的chromedriver文件
                for root, dirs, files in os.walk(driver_dir):
                    for file in files:
                        if file.startswith('chromedriver'):
                            extracted_path = Path(root) / file
                            final_path = driver_dir / ("chromedriver.exe" if self.system == "windows" else "chromedriver")

                            if extracted_path != final_path:
                                shutil.move(str(extracted_path), str(final_path))

                            # 设置执行权限 (Unix系统)
                            if self.system != "windows":
                                os.chmod(str(final_path), 0o755)

                            logger.info(f"✅ [ChromeDriver下载] 下载完成: {final_path}")
                            return str(final_path)

                logger.error("❌ [ChromeDriver下载] 解压后未找到chromedriver文件")
                return None

            except Exception as download_error:
                logger.error(f"❌ [ChromeDriver下载] 下载过程失败: {str(download_error)}")
                return None
            finally:
                # 清理临时文件
                if temp_file_path:
                    try:
                        os.unlink(temp_file_path)
                    except:
                        pass
                    
        except Exception as e:
            logger.error(f"❌ [ChromeDriver下载] 下载失败: {str(e)}")
            return None
    
    def _get_platform_suffix(self) -> Optional[str]:
        """获取平台后缀"""
        if self.system == "windows":
            return "win64" if platform.machine().endswith('64') else "win32"
        elif self.system == "darwin":
            return "mac-arm64" if platform.machine() == "arm64" else "mac-x64"
        elif self.system == "linux":
            return "linux64"
        return None
    
    def get_or_download_chromedriver(self) -> Optional[str]:
        """获取或下载ChromeDriver"""
        try:
            if not self.browser_version:
                browser_detected, browser_path, browser_version = self.detect_chrome_installation()
                if not browser_detected:
                    return None

            driver_version = self.get_chromedriver_version(self.browser_version)
            return self.download_chromedriver(driver_version)

        except Exception as e:
            logger.error(f"❌ [ChromeDriver管理] 获取ChromeDriver失败: {str(e)}")
            return None

    def get_browser_info(self) -> Dict[str, Optional[str]]:
        """获取当前检测到的浏览器信息"""
        return {
            "browser_name": self.browser_name,
            "browser_path": self.browser_path,
            "browser_version": self.browser_version
        }


class NetworkConnectivityChecker:
    """网络连接检查器"""
    
    @staticmethod
    def check_internet_connection() -> bool:
        """检查互联网连接 - 已跳过网络验证"""
        # 网络连接验证已被跳过，直接返回True以避免启动延迟
        # logger.info("⏭️ [网络检查] 跳过网络连接验证，直接启动浏览器")
        return True

        # 原网络检查代码已注释，以提高启动速度和离线兼容性
        # test_urls = [
        #     "https://www.google.com",
        #     "https://www.baidu.com",
        #     "https://httpbin.org/get"
        # ]
        #
        # for url in test_urls:
        #     try:
        #         with urllib.request.urlopen(url, timeout=5) as response:
        #             if response.status == 200:
        #                 logger.info(f"✅ [网络检查] 网络连接正常: {url}")
        #                 return True
        #     except:
        #         continue
        #
        # logger.error("❌ [网络检查] 无网络连接")
        # return False
    
    @staticmethod
    def check_chromedriver_download_access() -> bool:
        """检查ChromeDriver下载服务的可访问性 - 已跳过网络验证"""
        # ChromeDriver下载服务验证已被跳过，直接返回True
        logger.info("⏭️ [下载服务检查] 跳过下载服务验证，假设服务可用")
        return True

        # 原下载服务检查代码已注释，以提高启动速度
        # test_urls = [
        #     "https://googlechromelabs.github.io/chrome-for-testing/",
        #     "https://storage.googleapis.com/chrome-for-testing-public/",
        #     "https://chromedriver.storage.googleapis.com/"
        # ]
        #
        # for url in test_urls:
        #     try:
        #         with urllib.request.urlopen(url, timeout=10) as response:
        #             if response.status == 200:
        #                 logger.info(f"✅ [下载服务检查] 可访问: {url}")
        #                 return True
        #     except Exception as e:
        #         logger.debug(f"下载服务不可访问: {url} - {str(e)}")
        #         continue
        #
        # logger.warning("⚠️ [下载服务检查] ChromeDriver下载服务不可访问")
        # return False


class BrowserConfigValidator:
    """浏览器配置验证器"""
    
    def __init__(self):
        self.driver_manager = ChromeDriverManager()
        self.network_checker = NetworkConnectivityChecker()
    
    def validate_browser_environment(self) -> Dict[str, any]:
        """验证浏览器环境"""
        result = {
            "chrome_installed": False,
            "chrome_path": None,
            "chrome_version": None,
            "chromedriver_available": False,
            "chromedriver_path": None,
            "network_available": False,
            "download_service_available": False,
            "recommendations": []
        }
        
        try:
            # 检查浏览器安装（Chrome优先，Edge备选）
            browser_detected, browser_path, browser_version = self.driver_manager.detect_chrome_installation()
            result["chrome_installed"] = browser_detected
            result["chrome_path"] = browser_path
            result["chrome_version"] = browser_version

            if not browser_detected:
                result["recommendations"].append("请安装Google Chrome或Microsoft Edge浏览器")
                return result
            
            # 跳过网络连接检查，直接设置为可用状态
            logger.info("⏭️ [环境验证] 跳过网络连接验证步骤")
            result["network_available"] = True  # 跳过网络检查
            result["download_service_available"] = True  # 跳过下载服务检查
            
            # 尝试获取ChromeDriver
            chromedriver_path = self.driver_manager.get_or_download_chromedriver()
            result["chromedriver_available"] = chromedriver_path is not None
            result["chromedriver_path"] = chromedriver_path
            
            if not result["chromedriver_available"]:
                # 移除网络相关的错误判断，直接提供通用建议
                logger.warning("⚠️ [环境验证] ChromeDriver不可用，但已跳过网络验证")
                result["recommendations"].append("ChromeDriver获取失败，请检查Chrome版本或手动下载配置")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [环境验证] 验证浏览器环境失败: {str(e)}")
            result["recommendations"].append(f"环境验证异常: {str(e)}")
            return result
