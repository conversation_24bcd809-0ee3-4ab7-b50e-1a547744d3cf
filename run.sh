#!/bin/bash

# TikTok Shop 商品数据爬取工具启动脚本

echo "========================================"
echo "TikTok Shop 商品数据爬取工具"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8或更高版本"
    exit 1
fi

# 检查Python版本
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "错误: Python版本过低，需要3.8或更高版本，当前版本: $python_version"
    exit 1
fi

# 创建虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "错误: 创建虚拟环境失败"
        exit 1
    fi
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 检查依赖是否安装
if [ ! -d "venv/lib/python*/site-packages/PyQt6" ]; then
    echo "安装依赖包..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 安装依赖包失败"
        exit 1
    fi
fi

# 运行程序
echo "启动应用程序..."
python main.py

exit_code=$?
if [ $exit_code -ne 0 ]; then
    echo
    echo "程序异常退出，错误代码: $exit_code"
    read -p "按回车键继续..."
fi

deactivate
