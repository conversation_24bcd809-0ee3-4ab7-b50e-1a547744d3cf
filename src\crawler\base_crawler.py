"""
基础爬虫类
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, urlparse
import aiohttp
import requests
from loguru import logger
from datetime import timedelta

# 代理功能已移除，改为直连模式

# 尝试导入fake_useragent，如果失败则使用备用方案
try:
    from fake_useragent import UserAgent
    HAS_FAKE_USERAGENT = True
except ImportError:
    logger.warning("fake_useragent 未安装，将使用内置User-Agent列表")
    HAS_FAKE_USERAGENT = False
    UserAgent = None

# 确保Brotli支持
try:
    import brotli
    logger.info("✅ [网络] Brotli解码器已加载")
    HAS_BROTLI = True
except ImportError:
    try:
        import brotlipy
        logger.info("✅ [网络] BrotliPy解码器已加载")
        HAS_BROTLI = True
    except ImportError:
        logger.warning("⚠️ [网络] 未找到Brotli解码器，可能影响某些网站访问")
        HAS_BROTLI = False

from ..config.settings import config
from ..models.scraping_task import ScrapingTask, TaskStatus


class BaseCrawler:
    """基础爬虫类"""
    
    def __init__(self, task: Optional[ScrapingTask] = None):
        self.task = task
        self.session: Optional[aiohttp.ClientSession] = None

        # 初始化User-Agent生成器
        if HAS_FAKE_USERAGENT:
            try:
                self.user_agent = UserAgent()
            except Exception as e:
                logger.warning(f"初始化fake_useragent失败: {e}，使用备用方案")
                self.user_agent = None
        else:
            self.user_agent = None

        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.start_time = time.time()

        # 从配置加载设置
        self.request_delay = config.crawler.request_delay
        self.random_delay_range = config.crawler.random_delay_range
        self.timeout = config.crawler.timeout
        self.max_retries = config.crawler.max_retries
        self.max_concurrent = config.crawler.max_concurrent
        self.user_agents = config.crawler.user_agents
        self.headers = config.crawler.headers.copy()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
    
    async def start_session(self):
        """启动会话"""
        try:
            # 创建SSL上下文（禁用SSL验证以解决连接问题）
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            connector = aiohttp.TCPConnector(
                limit=self.max_concurrent,
                limit_per_host=self.max_concurrent,
                ttl_dns_cache=300,
                use_dns_cache=True,
                ssl=ssl_context,  # 使用自定义SSL上下文
                enable_cleanup_closed=True
            )

            # 增加超时时间
            timeout = aiohttp.ClientTimeout(
                total=self.timeout * 2,  # 增加总超时时间
                connect=15,  # 连接超时
                sock_read=30  # 读取超时
            )

            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self.get_headers()
            )

            logger.info("✅ [网络] 爬虫会话已启动")
            logger.debug(f"   - SSL验证: 已禁用（用于解决连接问题）")
            logger.debug(f"   - 总超时: {self.timeout * 2}秒")
            logger.debug(f"   - 连接超时: 15秒")

        except Exception as e:
            logger.error(f"💥 [网络] 爬虫会话启动失败: {str(e)}")
            raise
    
    async def close_session(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            logger.info("爬虫会话已关闭")
    
    def get_headers(self) -> Dict[str, str]:
        """获取完全模拟浏览器的请求头"""
        headers = self.headers.copy()

        # 根据Brotli支持情况设置Accept-Encoding
        if HAS_BROTLI:
            accept_encoding = 'gzip, deflate, br, zstd'
            logger.debug("🔧 [网络] 启用完整压缩支持 (gzip, deflate, br, zstd)")
        else:
            accept_encoding = 'gzip, deflate'
            logger.debug("🔧 [网络] 禁用Brotli压缩（未安装解码器）")

        # 使用完全匹配浏览器的请求头
        browser_headers = {
            'Authority': 'www.tiktok.com',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': accept_encoding,
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'max-age=0',
            'Priority': 'u=0, i',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        }

        # 添加TikTok会话Cookie（这是关键！）
        tiktok_cookie = self.get_tiktok_session_cookie()
        if tiktok_cookie:
            browser_headers['Cookie'] = tiktok_cookie
            logger.debug("🍪 [网络] 添加TikTok会话Cookie")
        else:
            logger.warning("⚠️ [网络] 未设置TikTok会话Cookie，可能影响内容获取")

        headers.update(browser_headers)

        logger.debug("🔧 [网络] 使用完全浏览器模拟请求头")
        logger.debug(f"   - User-Agent: {browser_headers['User-Agent']}")
        logger.debug(f"   - Accept-Encoding: {browser_headers['Accept-Encoding']}")
        logger.debug(f"   - Cookie设置: {'是' if tiktok_cookie else '否'}")

        return headers

    def get_tiktok_session_cookie(self) -> Optional[str]:
        """获取TikTok会话Cookie"""
        try:
            
            # # 方法1: 使用提供的真实Cookie
            # real_cookie = self.get_real_tiktok_cookie()
            # if real_cookie:
            #     logger.debug("🍪 [Cookie] 使用真实TikTok Cookie")
            #     return real_cookie

            # 方法2: 使用默认的基础Cookie（包含必要的会话标识）
            default_cookie = self.generate_default_tiktok_cookie()
            if default_cookie:
                logger.debug("🍪 [Cookie] 使用生成的默认TikTok Cookie")
                return default_cookie

            logger.warning("⚠️ [Cookie] 无法获取TikTok Cookie")
            return None

        except Exception as e:
            logger.error(f"💥 [Cookie] 获取TikTok Cookie失败: {str(e)}")
            return None

    def get_real_tiktok_cookie(self) -> str:
        """获取真实的TikTok Cookie（从浏览器提供）"""
        # 使用您提供的真实Cookie
        real_cookie = "ttwid=1%7C-DBbfUTL57lhn8Us0f-tXi_dcp1qzxk2YkBI6eCOTFY%7C1752905531%7C81a214891004c36906814786a745a3db6f8f88bf7bb7394ecd10cefdc88f5e31; i18next=en; msToken=NtqxqZtrpioKYW2qXKCXH929SQT2-OwvKD7iu3PJzltpnBOC3w09afjogn-ykews_5fIqosGAkE3juNUtatfoa2SnuNK9i-KMs0HYb9_ml-6HY8UxjNggAjphK3UG0rEHFJAMw=="
        return real_cookie

    def generate_default_tiktok_cookie(self) -> str:
        """生成增强的TikTok Cookie，包含反爬虫对策"""
        try:
            import time
            import random
            import hashlib
            import uuid
            import json
            import base64

            logger.info("🍪 [Cookie] 开始生成增强的TikTok Cookie")

            # 当前时间戳
            current_time = int(time.time())

            # 生成设备指纹相关ID
            device_id = str(random.randint(7000000000000000000, 7******************))
            install_id = str(random.randint(7000000000000000000, 7******************))
            web_id = str(random.randint(7000000000000000000, 7******************))

            # 生成会话相关ID
            session_id = ''.join(random.choices('0123456789abcdef', k=32))
            random_id = ''.join(random.choices('0123456789abcdef', k=32))

            # 生成msToken (TikTok的重要认证token)
            ms_token_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'
            ms_token = ''.join(random.choices(ms_token_chars, k=107)) + '=='

            # 生成tt_webid_v2 (重要的设备标识)
            tt_webid_v2 = f"{web_id}"

            # 生成ttwid (TikTok Web ID)
            ttwid_hash = hashlib.md5(f"{random_id}{current_time}".encode()).hexdigest()
            ttwid = f"1%7C{random_id}%7C{current_time}%7C{ttwid_hash}"

            # 生成设备信息相关的cookie值
            screen_info = f"{random.choice([1920, 1366, 1536, 1440])}x{random.choice([1080, 768, 864, 900])}"
            timezone_offset = random.choice([-480, -420, -360, -300, -240, -180, 0, 60, 120, 180, 240, 300, 360, 420, 480])

            # 构建完整的Cookie集合
            cookie_parts = [
                # 核心会话Cookie
                f"ttwid={ttwid}",
                f"tt_webid={web_id}",
                f"tt_webid_v2={tt_webid_v2}",
                f"msToken={ms_token}",

                # 设备指纹Cookie
                f"device_id={device_id}",
                f"install_id={install_id}",
                f"sessionid={session_id}",

                # 地区和语言设置
                "i18next=en",
                "tt_lang=en",
                "locale=en",
                "region=US",
                "timezone=America/New_York",

                # 设备和浏览器信息
                f"screen_info={screen_info}",
                f"timezone_offset={timezone_offset}",
                "browser_language=en-US",
                "browser_platform=Win32",
                "browser_name=Mozilla",
                "browser_version=5.0",

                # 反爬虫检测相关
                f"_abck={self._generate_abck_token()}",
                f"bm_sz={self._generate_bm_sz()}",
                f"ak_bmsc={self._generate_ak_bmsc()}",

                # TikTok特定的追踪和分析Cookie
                f"tt_csrf_token={self._generate_csrf_token()}",
                f"odin_tt={random.randint(*********, *********)}",
                f"passport_csrf_token={self._generate_csrf_token()}",

                # 用户偏好设置
                "cookie-consent=true",
                "privacy-policy-accepted=true",
                "terms-of-service=accepted",

                # 性能和分析相关
                f"perf_feed_cache={random.randint(1000000, 9999999)}",
                f"s_v_web_id={web_id}",

                # 额外的反检测措施
                f"__tea_cache_tokens_1988={self._generate_tea_token()}",
                f"store-idc=useast1a",
                f"store-country-code=us",

                # 时间戳相关
                f"timestamp={current_time}",
                f"last_login_time={current_time - random.randint(3600, 86400)}",
            ]

            # 随机打乱cookie顺序（模拟真实浏览器行为）
            random.shuffle(cookie_parts)

            default_cookie = '; '.join(cookie_parts)

            logger.info(f"🍪 [Cookie] 生成增强Cookie成功，包含 {len(cookie_parts)} 个字段")
            logger.debug(f"🍪 [Cookie] Cookie预览: {default_cookie[:150]}...")

            return default_cookie

        except Exception as e:
            logger.error(f"💥 [Cookie] 生成增强Cookie失败: {str(e)}")
            # 降级到简单Cookie
            return self._generate_simple_fallback_cookie()

    def _generate_abck_token(self) -> str:
        """生成_abck反爬虫token"""
        import random
        import time
        chars = '0123456789ABCDEF'
        token_parts = [
            ''.join(random.choices(chars, k=8)),
            ''.join(random.choices(chars, k=4)),
            ''.join(random.choices(chars, k=4)),
            ''.join(random.choices(chars, k=4)),
            ''.join(random.choices(chars, k=12))
        ]
        return '-'.join(token_parts) + f'~-1~{random.randint(1000, 9999)}~{int(time.time())}'

    def _generate_bm_sz(self) -> str:
        """生成bm_sz token"""
        import random
        chars = '0123456789ABCDEF'
        return ''.join(random.choices(chars, k=16))

    def _generate_ak_bmsc(self) -> str:
        """生成ak_bmsc token"""
        import random
        import time
        chars = '0123456789ABCDEF'
        return ''.join(random.choices(chars, k=32)) + f'~{int(time.time())}'

    def _generate_csrf_token(self) -> str:
        """生成CSRF token"""
        import random
        chars = '0123456789abcdef'
        return ''.join(random.choices(chars, k=32))

    def _generate_tea_token(self) -> str:
        """生成__tea_cache_tokens token"""
        import random
        import json
        import base64
        import time

        token_data = {
            "timestamp": int(time.time()),
            "random": random.randint(100000, 999999),
            "version": "1.0"
        }

        try:
            json_str = json.dumps(token_data, separators=(',', ':'))
            encoded = base64.b64encode(json_str.encode()).decode()
            return encoded[:32]  # 截取前32位
        except:
            # 降级方案
            chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            return ''.join(random.choices(chars, k=32))

    def _generate_simple_fallback_cookie(self) -> str:
        """生成简单的降级Cookie"""
        try:
            import time
            import random
            import hashlib

            timestamp = str(int(time.time()))
            random_id = ''.join(random.choices('0123456789abcdef', k=32))

            cookie_parts = [
                f"ttwid=1%7C{random_id}%7C{timestamp}%7C{hashlib.md5(random_id.encode()).hexdigest()}",
                "i18next=en",
                f"msToken={''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_', k=107))}=="
            ]

            return '; '.join(cookie_parts)
        except:
            return "i18next=en"

    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        try:
            import random

            # 真实的User-Agent列表（模拟不同的浏览器和操作系统）
            user_agents = [
                # Chrome on Windows
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',

                # Chrome on macOS
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',

                # Firefox on Windows
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',

                # Safari on macOS
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',

                # Edge on Windows
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
            ]

            selected_ua = random.choice(user_agents)
            logger.debug(f"🎭 [User-Agent] 选择随机UA: {selected_ua}")
            return selected_ua

        except Exception as e:
            logger.debug(f"⚠️ [User-Agent] 生成随机UA失败: {str(e)}")
            # 降级到默认UA
            return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        # 优先使用配置中的User-Agent列表
        if self.user_agents:
            return random.choice(self.user_agents)

        # 尝试使用fake_useragent
        if self.user_agent:
            try:
                return self.user_agent.random
            except Exception as e:
                logger.debug(f"获取随机User-Agent失败: {e}")

        # 备用User-Agent列表
        fallback_user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]

        return random.choice(fallback_user_agents)
    
    async def delay(self):
        """请求延迟"""
        if self.random_delay_range:
            delay = random.uniform(*self.random_delay_range)
        else:
            delay = self.request_delay
        
        if delay > 0:
            await asyncio.sleep(delay)
    
    async def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[aiohttp.ClientResponse]:
        """发起HTTP请求（支持代理）"""
        if not self.session:
            await self.start_session()

        self.request_count += 1

        # 更新任务统计
        if self.task:
            self.task.total_requests = self.request_count

        for attempt in range(self.max_retries + 1):
            try:
                # 请求延迟
                if attempt > 0:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    await self.delay()

                # 更新User-Agent
                headers = kwargs.get('headers', {})
                headers.update(self.get_headers())
                kwargs['headers'] = headers

                logger.debug(f"请求 {method} {url} (尝试 {attempt + 1}/{self.max_retries + 1}) [直连]")

                start_time = time.time()
                async with self.session.request(method, url, **kwargs) as response:
                    response_time = time.time() - start_time

                    if response.status == 200:
                        self.success_count += 1
                        if self.task:
                            self.task.successful_requests = self.success_count



                        # 立即读取响应内容，避免连接关闭问题
                        response._content = await response.read()
                        return response
                    elif response.status == 429:  # 请求过于频繁
                        logger.warning(f"请求被限制 (429): {url}")
                        await asyncio.sleep(60)  # 等待1分钟
                        continue
                    elif response.status in [403, 404]:
                        logger.warning(f"请求失败 ({response.status}): {url}")
                        break
                    else:
                        logger.warning(f"请求返回状态码 {response.status}: {url}")
                        continue
                        
            except asyncio.TimeoutError:
                logger.warning(f"⏰ [网络] 请求超时: {url}")
                continue
            except aiohttp.ClientConnectorError as e:
                logger.error(f"🔌 [网络] 连接错误: {url} - {str(e)}")
                if "指定的网络名不再可用" in str(e):
                    logger.error("💡 [网络] 建议: 检查网络连接或使用代理/VPN")
                elif "ssl" in str(e).lower():
                    logger.error("💡 [网络] 建议: SSL连接问题，已禁用SSL验证")

                continue
            except aiohttp.ClientSSLError as e:
                logger.error(f"🔒 [网络] SSL错误: {url} - {str(e)}")
                logger.error("💡 [网络] 建议: SSL证书问题，已禁用SSL验证")
                continue
            except aiohttp.ClientError as e:
                logger.error(f"📡 [网络] 客户端错误: {url} - {str(e)}")
                continue
            except asyncio.TimeoutError as e:
                logger.error(f"⏰ [网络] 请求超时: {url} - {str(e)}")
                continue
            except Exception as e:
                logger.error(f"💥 [网络] 未知异常: {url} - {str(e)}")
                logger.debug(f"   异常类型: {type(e).__name__}")
                continue
        
        self.error_count += 1
        if self.task:
            self.task.failed_requests = self.error_count
        
        logger.error(f"请求最终失败: {url}")
        return None
    
    async def get_text(self, url: str, **kwargs) -> Optional[str]:
        """获取文本内容"""
        logger.info(f"🌐 [网络请求] 开始获取文本内容: {url}")
        response = await self.make_request(url, **kwargs)
        if response:
            try:
                # 使用预读取的内容（在make_request中已读取）
                if hasattr(response, '_content'):
                    raw_data = response._content
                    logger.debug(f"📥 [网络请求] 使用预读取数据: {len(raw_data)} 字节")
                else:
                    # 如果没有预读取，则现在读取
                    raw_data = await response.read()
                    logger.debug(f"📥 [网络请求] 读取原始数据: {len(raw_data)} 字节")

                # 手动解码为文本
                text_content = raw_data.decode('utf-8', errors='ignore')
                logger.info(f"✅ [网络请求] 成功获取文本内容，长度: {len(text_content)} 字符")
                return text_content

            except Exception as e:
                logger.warning(f"⚠️ [网络请求] 原始数据解码失败，尝试备用方案: {str(e)}")

                # 备用方案：使用response.text()
                try:
                    text_content = await response.text()
                    logger.info(f"✅ [网络请求] 备用方案成功，长度: {len(text_content)} 字符")
                    return text_content
                except Exception as backup_error:
                    logger.error(f"❌ [网络请求] 备用方案也失败: {str(backup_error)}")
        else:
            logger.error(f"❌ [网络请求] 请求失败，无响应: {url}")
        return None
    
    async def get_json(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取JSON内容"""
        logger.info(f"🌐 [网络请求] 开始获取JSON内容: {url}")
        response = await self.make_request(url, **kwargs)
        if response:
            try:
                json_data = await response.json()
                logger.info(f"✅ [网络请求] 成功获取JSON内容")
                logger.debug(f"📊 [网络请求] JSON结构: {list(json_data.keys()) if isinstance(json_data, dict) else type(json_data)}")
                return json_data
            except Exception as e:
                logger.error(f"❌ [网络请求] 解析JSON失败: {url} - {str(e)}")
        else:
            logger.error(f"❌ [网络请求] 请求失败，无响应: {url}")
        return None
    
    async def get_bytes(self, url: str, **kwargs) -> Optional[bytes]:
        """获取二进制内容"""
        response = await self.make_request(url, **kwargs)
        if response:
            try:
                return await response.read()
            except Exception as e:
                logger.error(f"读取响应字节失败: {url} - {str(e)}")
        return None
    
    def sync_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """同步HTTP请求（用于简单场景）"""
        headers = kwargs.get('headers', {})
        headers.update(self.get_headers())
        kwargs['headers'] = headers
        kwargs['timeout'] = self.timeout
        
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    time.sleep(2 ** attempt)
                else:
                    time.sleep(self.request_delay)
                
                response = requests.request(method, url, **kwargs)
                if response.status_code == 200:
                    return response
                elif response.status_code == 429:
                    time.sleep(60)
                    continue
                elif response.status_code in [403, 404]:
                    break
                    
            except Exception as e:
                logger.error(f"同步请求异常: {url} - {str(e)}")
                continue
        
        return None
    
    def update_task_progress(self, completed: int = 0, failed: int = 0, total: Optional[int] = None):
        """更新任务进度"""
        if self.task:
            if total is not None:
                self.task.progress.total = total
            self.task.progress.completed += completed
            self.task.progress.failed += failed
            
            # 计算平均响应时间
            if self.request_count > 0:
                elapsed = time.time() - self.start_time
                self.task.average_response_time = elapsed / self.request_count
    
    def is_valid_url(self, url: str) -> bool:
        """验证URL是否有效"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def normalize_url(self, url: str, base_url: Optional[str] = None) -> str:
        """标准化URL"""
        if base_url and not url.startswith(('http://', 'https://')):
            url = urljoin(base_url, url)
        return url.strip()
    
    def extract_domain(self, url: str) -> Optional[str]:
        """提取域名"""
        try:
            return urlparse(url).netloc
        except Exception:
            return None
    
    async def check_task_status(self) -> bool:
        """检查任务状态"""
        if not self.task:
            return True
        
        if self.task.status == TaskStatus.CANCELLED:
            logger.info("任务已取消")
            return False
        elif self.task.status == TaskStatus.PAUSED:
            logger.info("任务已暂停，等待恢复...")
            while self.task.status == TaskStatus.PAUSED:
                await asyncio.sleep(1)
            logger.info("任务已恢复")
        
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        elapsed = time.time() - self.start_time
        return {
            "total_requests": self.request_count,
            "successful_requests": self.success_count,
            "failed_requests": self.error_count,
            "success_rate": (self.success_count / self.request_count * 100) if self.request_count > 0 else 0,
            "average_response_time": elapsed / self.request_count if self.request_count > 0 else 0,
            "elapsed_time": elapsed,
            "requests_per_second": self.request_count / elapsed if elapsed > 0 else 0
        }



    async def close_session(self):
        """关闭会话"""
        # 关闭HTTP会话
        if self.session:
            await self.session.close()
            self.session = None

        logger.info("爬虫会话已关闭")
