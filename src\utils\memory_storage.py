"""
内存数据存储管理器
替代原来的数据库存储，使用内存中的数据结构存储爬取的数据
"""

import json
from typing import List, Optional, Dict, Any
from datetime import datetime
from loguru import logger
from threading import Lock

from ..models.product import Product
from ..models.shop import Shop
from ..models.scraping_task import ScrapingTask


class MemoryStorage:
    """内存数据存储管理器"""
    
    def __init__(self, max_products: int = 10000, max_shops: int = 1000, max_tasks: int = 500):
        """
        初始化内存存储
        
        Args:
            max_products: 最大商品数量限制
            max_shops: 最大店铺数量限制  
            max_tasks: 最大任务数量限制
        """
        # 数据存储容器
        self.products: Dict[str, Product] = {}  # product_id -> Product
        self.shops: Dict[str, Shop] = {}        # shop_id -> Shop
        self.tasks: Dict[str, ScrapingTask] = {} # task_id -> ScrapingTask
        
        # 索引和排序列表
        self.products_by_shop: Dict[str, List[str]] = {}  # shop_id -> [product_ids]
        self.products_by_category: Dict[str, List[str]] = {}  # category -> [product_ids]
        self.recent_products: List[str] = []  # 按时间排序的product_id列表
        self.recent_tasks: List[str] = []     # 按时间排序的task_id列表
        
        # 容量限制
        self.max_products = max_products
        self.max_shops = max_shops
        self.max_tasks = max_tasks
        
        # 线程安全锁
        self._lock = Lock()
        
        logger.info(f"内存存储管理器初始化完成 - 最大容量: 商品{max_products}, 店铺{max_shops}, 任务{max_tasks}")
    
    async def initialize(self):
        """初始化存储（兼容原接口）"""
        logger.info("内存存储已就绪")
        return True
    
    def save_product(self, product: Product) -> bool:
        """
        保存商品数据到内存
        
        Args:
            product: 商品对象
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                # 检查容量限制
                if len(self.products) >= self.max_products and product.product_id not in self.products:
                    # 移除最旧的商品
                    self._remove_oldest_product()
                
                # 如果是更新现有商品，先清理旧索引
                if product.product_id in self.products:
                    old_product = self.products[product.product_id]
                    self._cleanup_product_indexes(old_product)

                # 保存商品
                self.products[product.product_id] = product

                # 更新索引
                self._update_product_indexes(product)
                
                # 更新最近商品列表
                if product.product_id in self.recent_products:
                    self.recent_products.remove(product.product_id)
                self.recent_products.insert(0, product.product_id)
                
                logger.debug(f"💾 [内存存储] 商品保存成功: {product.product_id} - {product.title}")
                return True
                
        except Exception as e:
            logger.error(f"保存商品失败: {product.product_id} - {str(e)}")
            return False
    
    async def save_product_async(self, product: Product) -> bool:
        """异步保存商品数据（兼容原接口）"""
        return self.save_product(product)
    
    def save_shop(self, shop: Shop) -> bool:
        """
        保存店铺数据到内存
        
        Args:
            shop: 店铺对象
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                # 检查容量限制
                if len(self.shops) >= self.max_shops and shop.shop_id not in self.shops:
                    # 移除最旧的店铺
                    self._remove_oldest_shop()
                
                # 保存店铺
                self.shops[shop.shop_id] = shop
                
                logger.debug(f"店铺保存成功: {shop.shop_id} - {shop.shop_name}")
                return True
                
        except Exception as e:
            logger.error(f"保存店铺失败: {shop.shop_id} - {str(e)}")
            return False
    
    async def save_shop_async(self, shop: Shop) -> bool:
        """异步保存店铺数据（兼容原接口）"""
        return self.save_shop(shop)
    
    def save_task(self, task: ScrapingTask) -> bool:
        """
        保存任务数据到内存
        
        Args:
            task: 任务对象
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                # 检查容量限制
                if len(self.tasks) >= self.max_tasks and task.task_id not in self.tasks:
                    # 移除最旧的任务
                    self._remove_oldest_task()
                
                # 保存任务
                self.tasks[task.task_id] = task
                
                # 更新最近任务列表
                if task.task_id in self.recent_tasks:
                    self.recent_tasks.remove(task.task_id)
                self.recent_tasks.insert(0, task.task_id)
                
                logger.debug(f"任务保存成功: {task.task_id}")
                return True
                
        except Exception as e:
            logger.error(f"保存任务失败: {task.task_id} - {str(e)}")
            return False
    
    async def save_task_async(self, task: ScrapingTask) -> bool:
        """异步保存任务数据（兼容原接口）"""
        return self.save_task(task)
    
    def load_product(self, product_id: str) -> Optional[Product]:
        """
        加载商品数据
        
        Args:
            product_id: 商品ID
            
        Returns:
            Product: 商品对象，如果不存在返回None
        """
        with self._lock:
            return self.products.get(product_id)
    
    async def load_product_async(self, product_id: str) -> Optional[Product]:
        """异步加载商品数据（兼容原接口）"""
        return self.load_product(product_id)
    
    def load_shop(self, shop_id: str) -> Optional[Shop]:
        """
        加载店铺数据
        
        Args:
            shop_id: 店铺ID
            
        Returns:
            Shop: 店铺对象，如果不存在返回None
        """
        with self._lock:
            return self.shops.get(shop_id)
    
    async def load_shop_async(self, shop_id: str) -> Optional[Shop]:
        """异步加载店铺数据（兼容原接口）"""
        return self.load_shop(shop_id)
    
    def load_task(self, task_id: str) -> Optional[ScrapingTask]:
        """
        加载任务数据
        
        Args:
            task_id: 任务ID
            
        Returns:
            ScrapingTask: 任务对象，如果不存在返回None
        """
        with self._lock:
            return self.tasks.get(task_id)
    
    async def load_task_async(self, task_id: str) -> Optional[ScrapingTask]:
        """异步加载任务数据（兼容原接口）"""
        return self.load_task(task_id)
    
    def list_products(self, shop_id: Optional[str] = None, category: Optional[str] = None, 
                     limit: int = 100) -> List[Product]:
        """
        列出商品
        
        Args:
            shop_id: 店铺ID过滤
            category: 分类过滤
            limit: 返回数量限制
            
        Returns:
            List[Product]: 商品列表
        """
        with self._lock:
            product_ids = []
            
            if shop_id:
                # 按店铺过滤
                product_ids = self.products_by_shop.get(shop_id, [])
            elif category:
                # 按分类过滤
                product_ids = self.products_by_category.get(category, [])
            else:
                # 返回最近的商品
                product_ids = self.recent_products
            
            # 应用限制并返回商品对象
            limited_ids = product_ids[:limit]
            return [self.products[pid] for pid in limited_ids if pid in self.products]
    
    async def list_products_async(self, shop_id: Optional[str] = None, limit: int = 100) -> List[Product]:
        """异步列出商品（兼容原接口）"""
        return self.list_products(shop_id=shop_id, limit=limit)
    
    def list_shops(self, limit: int = 100) -> List[Shop]:
        """
        列出店铺
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[Shop]: 店铺列表
        """
        with self._lock:
            shops = list(self.shops.values())
            # 按更新时间排序（最新的在前）
            shops.sort(key=lambda s: s.scraped_at if s.scraped_at else datetime.min, reverse=True)
            return shops[:limit]
    
    def list_tasks(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        列出任务
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 任务数据列表
        """
        with self._lock:
            limited_ids = self.recent_tasks[:limit]
            tasks = []
            for task_id in limited_ids:
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    tasks.append(task.to_dict() if hasattr(task, 'to_dict') else task.__dict__)
            return tasks
    
    async def list_tasks_async(self, limit: int = 100) -> List[Dict[str, Any]]:
        """异步列出任务（兼容原接口）"""
        return self.list_tasks(limit)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            Dict: 统计信息
        """
        with self._lock:
            return {
                'total_products': len(self.products),
                'total_shops': len(self.shops),
                'total_tasks': len(self.tasks),
                'max_products': self.max_products,
                'max_shops': self.max_shops,
                'max_tasks': self.max_tasks,
                'categories': list(self.products_by_category.keys()),
                'shops_with_products': list(self.products_by_shop.keys())
            }
    
    def clear_all(self):
        """清空所有数据"""
        with self._lock:
            self.products.clear()
            self.shops.clear()
            self.tasks.clear()
            self.products_by_shop.clear()
            self.products_by_category.clear()
            self.recent_products.clear()
            self.recent_tasks.clear()
            logger.info("所有内存数据已清空")
    
    async def close(self):
        """关闭存储（兼容原接口）"""
        logger.info("内存存储管理器已关闭")
    
    def _update_product_indexes(self, product: Product):
        """更新商品索引"""
        # 更新店铺索引
        if product.shop_id:
            if product.shop_id not in self.products_by_shop:
                self.products_by_shop[product.shop_id] = []
            if product.product_id not in self.products_by_shop[product.shop_id]:
                self.products_by_shop[product.shop_id].append(product.product_id)
        
        # 更新分类索引
        if product.category:
            if product.category not in self.products_by_category:
                self.products_by_category[product.category] = []
            if product.product_id not in self.products_by_category[product.category]:
                self.products_by_category[product.category].append(product.product_id)
    
    def _remove_oldest_product(self):
        """移除最旧的商品"""
        if self.recent_products:
            oldest_id = self.recent_products.pop()
            if oldest_id in self.products:
                product = self.products.pop(oldest_id)
                # 清理索引
                self._cleanup_product_indexes(product)
                logger.debug(f"移除最旧商品: {oldest_id}")
    
    def _remove_oldest_shop(self):
        """移除最旧的店铺"""
        if self.shops:
            # 找到最旧的店铺
            oldest_shop = min(self.shops.values(), 
                            key=lambda s: s.scraped_at if s.scraped_at else datetime.min)
            self.shops.pop(oldest_shop.shop_id)
            logger.debug(f"移除最旧店铺: {oldest_shop.shop_id}")
    
    def _remove_oldest_task(self):
        """移除最旧的任务"""
        if self.recent_tasks:
            oldest_id = self.recent_tasks.pop()
            if oldest_id in self.tasks:
                self.tasks.pop(oldest_id)
                logger.debug(f"移除最旧任务: {oldest_id}")
    
    def _cleanup_product_indexes(self, product: Product):
        """清理商品索引"""
        # 清理店铺索引
        if product.shop_id and product.shop_id in self.products_by_shop:
            if product.product_id in self.products_by_shop[product.shop_id]:
                self.products_by_shop[product.shop_id].remove(product.product_id)
                if not self.products_by_shop[product.shop_id]:
                    del self.products_by_shop[product.shop_id]
        
        # 清理分类索引
        if product.category and product.category in self.products_by_category:
            if product.product_id in self.products_by_category[product.category]:
                self.products_by_category[product.category].remove(product.product_id)
                if not self.products_by_category[product.category]:
                    del self.products_by_category[product.category]
