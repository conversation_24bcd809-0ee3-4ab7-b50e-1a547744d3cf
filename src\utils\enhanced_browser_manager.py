"""
增强版浏览器管理器
集成网络拦截功能与现有的ChromeDriverManager
支持Chrome和Edge浏览器的网络请求拦截
"""

from typing import Optional, Dict, Any, List
from pathlib import Path
from loguru import logger
from selenium import webdriver

from .browser_config import ChromeDriverManager, BrowserConfigValidator
from .network_interceptor import NetworkInterceptor


class EnhancedBrowserManager:
    """增强版浏览器管理器 - 集成网络拦截功能"""
    
    def __init__(self, output_dir: Optional[Path] = None):
        """
        初始化增强版浏览器管理器
        
        Args:
            output_dir: 网络拦截数据输出目录
        """
        self.driver_manager = ChromeDriverManager()
        self.network_interceptor = NetworkInterceptor(output_dir)
        self.validator = BrowserConfigValidator()
        
        # 浏览器和驱动信息
        self.driver: Optional[webdriver.Chrome] = None
        self.browser_info: Dict[str, Optional[str]] = {}
        self.driver_path: Optional[str] = None
        
        logger.info("🚀 [增强浏览器管理器] 初始化完成")
    
    def setup_browser_with_interception(self, enable_interception: bool = True) -> bool:
        """
        设置支持网络拦截的浏览器
        
        Args:
            enable_interception: 是否启用网络拦截
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 1. 验证浏览器环境
            logger.info("🔍 [增强浏览器管理器] 验证浏览器环境...")
            env_result = self.validator.validate_browser_environment()
            
            if not env_result['chrome_installed'] or not env_result['chromedriver_available']:
                logger.error("❌ [增强浏览器管理器] 浏览器环境验证失败")
                return False
            
            # 2. 获取浏览器信息
            # 先执行浏览器检测以获取完整信息
            browser_detected, browser_path, browser_version = self.driver_manager.detect_chrome_installation()
            if browser_detected:
                self.browser_info = self.driver_manager.get_browser_info()
            else:
                self.browser_info = {'browser_name': None, 'browser_path': None, 'browser_version': None}

            self.driver_path = env_result['chromedriver_path']
            
            logger.info(f"✅ [增强浏览器管理器] 浏览器信息:")
            logger.info(f"   浏览器: {self.browser_info['browser_name']}")
            logger.info(f"   版本: {self.browser_info['browser_version']}")
            logger.info(f"   路径: {self.browser_info['browser_path']}")
            logger.info(f"   驱动: {self.driver_path}")
            
            # 3. 设置WebDriver
            if enable_interception:
                logger.info("🕸️ [增强浏览器管理器] 启用网络拦截功能...")
                self.driver = self.network_interceptor.setup_driver_with_cdp(
                    driver_path=self.driver_path,
                    browser_path=self.browser_info['browser_path'],
                    browser_name=self.browser_info['browser_name']
                )
            else:
                logger.info("🌐 [增强浏览器管理器] 使用标准WebDriver...")
                self.driver = self._setup_standard_driver()
            
            if self.driver:
                logger.info("✅ [增强浏览器管理器] 浏览器设置完成")
                return True
            else:
                logger.error("❌ [增强浏览器管理器] 浏览器设置失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ [增强浏览器管理器] 设置浏览器失败: {str(e)}")
            return False
    
    def _setup_standard_driver(self) -> Optional[webdriver.Chrome]:
        """设置标准WebDriver（无网络拦截）"""
        try:
            from selenium.webdriver.chrome.service import Service
            
            options = webdriver.ChromeOptions()
            options.binary_location = self.browser_info['browser_path']
            
            # 基本选项
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(self.driver_path)
            driver = webdriver.Chrome(service=service, options=options)
            
            # 隐藏自动化特征
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
            
        except Exception as e:
            logger.error(f"❌ [增强浏览器管理器] 标准WebDriver设置失败: {str(e)}")
            return None
    
    def add_interception_pattern(self, pattern: str):
        """添加网络拦截匹配模式"""
        self.network_interceptor.add_target_pattern(pattern)
    
    def add_response_callback(self, callback):
        """添加响应拦截回调函数"""
        self.network_interceptor.add_response_callback(callback)
    
    def start_network_monitoring(self):
        """开始网络监控"""
        self.network_interceptor.start_monitoring()
    
    def stop_and_extract_network_data(self) -> Dict[str, Any]:
        """停止监控并提取网络数据"""
        return self.network_interceptor.stop_monitoring_and_extract()
    
    def get_driver(self) -> Optional[webdriver.Chrome]:
        """获取WebDriver实例"""
        return self.driver
    
    def get_browser_info(self) -> Dict[str, Optional[str]]:
        """获取浏览器信息"""
        return self.browser_info
    
    def navigate_to(self, url: str):
        """导航到指定URL"""
        if self.driver:
            self.driver.get(url)
            logger.info(f"🌐 [增强浏览器管理器] 导航到: {url}")
        else:
            logger.error("❌ [增强浏览器管理器] WebDriver未初始化")
    
    def wait_for_load_more_button(self, timeout: int = 10) -> bool:
        """等待"加载更多"按钮出现"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            if not self.driver:
                return False
            
            # 常见的"加载更多"按钮选择器
            selectors = [
                "button[data-e2e='load-more']",
                "button:contains('Load More')",
                "button:contains('加载更多')",
                ".load-more-button",
                "[data-testid='load-more']"
            ]
            
            wait = WebDriverWait(self.driver, timeout)
            
            for selector in selectors:
                try:
                    if selector.startswith("button:contains"):
                        # 使用XPath处理包含文本的选择器
                        text = selector.split("'")[1]
                        xpath = f"//button[contains(text(), '{text}')]"
                        element = wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
                    else:
                        element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                    
                    if element:
                        logger.info(f"✅ [增强浏览器管理器] 找到加载更多按钮: {selector}")
                        return True
                        
                except Exception:
                    continue
            
            logger.warning("⚠️ [增强浏览器管理器] 未找到加载更多按钮")
            return False
            
        except Exception as e:
            logger.error(f"❌ [增强浏览器管理器] 等待加载更多按钮失败: {str(e)}")
            return False
    
    def click_load_more_button(self) -> bool:
        """点击"加载更多"按钮"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            import time
            
            if not self.driver:
                return False
            
            # 开始网络监控
            self.start_network_monitoring()
            
            # 查找并点击按钮
            selectors = [
                "button[data-e2e='load-more']",
                "//button[contains(text(), 'Load More')]",
                "//button[contains(text(), '加载更多')]",
                ".load-more-button",
                "[data-testid='load-more']"
            ]
            
            button_clicked = False
            for selector in selectors:
                try:
                    if selector.startswith("//"):
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    
                    # 点击按钮
                    element.click()
                    button_clicked = True
                    logger.info(f"✅ [增强浏览器管理器] 成功点击加载更多按钮: {selector}")
                    break
                    
                except Exception:
                    continue
            
            if not button_clicked:
                logger.warning("⚠️ [增强浏览器管理器] 未能点击加载更多按钮")
                return False
            
            # 等待网络请求完成
            time.sleep(3)
            
            # 提取网络数据
            network_data = self.stop_and_extract_network_data()
            
            if network_data['responses']:
                logger.info(f"🎯 [增强浏览器管理器] 捕获到 {len(network_data['responses'])} 个目标响应")
                return True
            else:
                logger.warning("⚠️ [增强浏览器管理器] 未捕获到目标API响应")
                return False
                
        except Exception as e:
            logger.error(f"❌ [增强浏览器管理器] 点击加载更多按钮失败: {str(e)}")
            return False
    
    def save_intercepted_data(self, filename: Optional[str] = None) -> Optional[Path]:
        """保存拦截的网络数据"""
        try:
            data = self.network_interceptor.get_intercepted_data()
            if data['requests'] or data['responses']:
                return self.network_interceptor.save_intercepted_data(data, filename)
            else:
                logger.info("ℹ️ [增强浏览器管理器] 没有数据需要保存")
                return None
        except Exception as e:
            logger.error(f"❌ [增强浏览器管理器] 保存数据失败: {str(e)}")
            return None
    
    def get_intercepted_responses(self) -> List[Dict[str, Any]]:
        """获取拦截的响应数据"""
        return self.network_interceptor.intercepted_responses
    
    def clear_intercepted_data(self):
        """清空拦截的数据"""
        self.network_interceptor.clear_data()
    
    def close(self):
        """关闭浏览器管理器"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("🔒 [增强浏览器管理器] 浏览器已关闭")
            
            self.network_interceptor.close()
            
        except Exception as e:
            logger.error(f"❌ [增强浏览器管理器] 关闭失败: {str(e)}")
        
        self.driver = None
