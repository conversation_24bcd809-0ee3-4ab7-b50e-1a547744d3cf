"""
数据导出工具
从内存存储中导出数据到各种格式的文件
"""

import json
import csv
from typing import List, Optional, Dict, Any
from pathlib import Path
from datetime import datetime
from loguru import logger

try:
    import pandas as pd
    import openpyxl
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logger.warning("pandas或openpyxl未安装，Excel导出功能将不可用")

from ..models.product import Product
from ..models.shop import Shop
from .memory_storage import MemoryStorage


class DataExporter:
    """数据导出器"""
    
    def __init__(self, memory_storage: MemoryStorage, output_dir: Optional[Path] = None):
        """
        初始化数据导出器
        
        Args:
            memory_storage: 内存存储实例
            output_dir: 输出目录，默认为exports
        """
        self.memory_storage = memory_storage
        self.output_dir = output_dir or Path("exports")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"数据导出器初始化完成，输出目录: {self.output_dir}")
    
    def export_products_to_json(self, filename: Optional[str] = None, 
                               shop_id: Optional[str] = None,
                               category: Optional[str] = None) -> Path:
        """
        导出商品数据到JSON文件
        
        Args:
            filename: 文件名，默认自动生成
            shop_id: 店铺ID过滤
            category: 分类过滤
            
        Returns:
            Path: 导出文件路径
        """
        try:
            # 获取商品数据
            products = self.memory_storage.list_products(
                shop_id=shop_id, 
                category=category, 
                limit=10000
            )
            
            # 转换为字典格式
            products_data = []
            for product in products:
                product_dict = {
                    'product_id': product.product_id,
                    'title': product.title,
                    'description': product.description,
                    'category': product.category,
                    'subcategory': product.subcategory,
                    'brand': product.brand,
                    'price': float(product.price),
                    'original_price': float(product.original_price) if product.original_price else None,
                    'currency': product.currency.value if hasattr(product.currency, 'value') else str(product.currency),
                    'discount_percentage': product.discount_percentage,
                    'stock': product.stock,
                    'sold_count': product.sold_count,
                    'monthly_sales': product.monthly_sales,
                    'main_image_url': product.main_image_url,
                    'shop_id': product.shop_id,
                    'shop_name': product.shop_name,
                    'shop_url': product.shop_url,
                    'product_url': getattr(product, 'product_url', ''),
                    'status': product.status.value if hasattr(product.status, 'value') else str(product.status),
                    'created_at': product.created_at.isoformat() if product.created_at else None,
                    'updated_at': product.updated_at.isoformat() if product.updated_at else None,
                    'scraped_at': product.scraped_at.isoformat() if product.scraped_at else None,
                    'images': [
                        {
                            'url': img.url,
                            'alt_text': img.alt_text,
                            'width': img.width,
                            'height': img.height,
                            'is_main': img.is_main,
                            'order': img.order
                        } for img in product.images
                    ] if product.images else [],
                    'rating': {
                        'average_rating': product.rating.average_rating,
                        'total_reviews': product.rating.total_reviews,
                        'rating_distribution': product.rating.rating_distribution
                    } if hasattr(product, 'rating') and product.rating else None
                }
                products_data.append(product_dict)
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filter_suffix = ""
                if shop_id:
                    filter_suffix += f"_shop_{shop_id}"
                if category:
                    filter_suffix += f"_category_{category}"
                filename = f"products_{timestamp}{filter_suffix}.json"
            
            # 导出文件
            output_path = self.output_dir / filename
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'export_time': datetime.now().isoformat(),
                    'total_count': len(products_data),
                    'filters': {
                        'shop_id': shop_id,
                        'category': category
                    },
                    'products': products_data
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"商品数据已导出到JSON: {output_path} ({len(products_data)} 条记录)")
            return output_path
            
        except Exception as e:
            logger.error(f"导出商品数据到JSON失败: {str(e)}")
            raise
    
    def export_products_to_csv(self, filename: Optional[str] = None,
                              shop_id: Optional[str] = None,
                              category: Optional[str] = None) -> Path:
        """
        导出商品数据到CSV文件
        
        Args:
            filename: 文件名，默认自动生成
            shop_id: 店铺ID过滤
            category: 分类过滤
            
        Returns:
            Path: 导出文件路径
        """
        try:
            # 获取商品数据
            products = self.memory_storage.list_products(
                shop_id=shop_id, 
                category=category, 
                limit=10000
            )
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filter_suffix = ""
                if shop_id:
                    filter_suffix += f"_shop_{shop_id}"
                if category:
                    filter_suffix += f"_category_{category}"
                filename = f"products_{timestamp}{filter_suffix}.csv"
            
            # 导出文件
            output_path = self.output_dir / filename
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
                if not products:
                    # 写入空文件头
                    writer = csv.writer(f)
                    writer.writerow(['product_id', 'title', 'price', 'shop_name', 'category'])
                    logger.info(f"商品数据已导出到CSV: {output_path} (0 条记录)")
                    return output_path
                
                # 写入CSV数据
                fieldnames = [
                    'product_id', 'title', 'description', 'category', 'subcategory', 'brand',
                    'price', 'original_price', 'currency', 'discount_percentage',
                    'stock', 'sold_count', 'monthly_sales', 'main_image_url',
                    'shop_id', 'shop_name', 'shop_url', 'product_url',
                    'status', 'created_at', 'updated_at', 'scraped_at'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for product in products:
                    row = {
                        'product_id': product.product_id,
                        'title': product.title,
                        'description': product.description,
                        'category': product.category,
                        'subcategory': product.subcategory,
                        'brand': product.brand,
                        'price': float(product.price),
                        'original_price': float(product.original_price) if product.original_price else '',
                        'currency': product.currency.value if hasattr(product.currency, 'value') else str(product.currency),
                        'discount_percentage': product.discount_percentage or '',
                        'stock': product.stock or '',
                        'sold_count': product.sold_count,
                        'monthly_sales': product.monthly_sales or '',
                        'main_image_url': product.main_image_url or '',
                        'shop_id': product.shop_id,
                        'shop_name': product.shop_name,
                        'shop_url': product.shop_url or '',
                        'product_url': getattr(product, 'product_url', ''),
                        'status': product.status.value if hasattr(product.status, 'value') else str(product.status),
                        'created_at': product.created_at.isoformat() if product.created_at else '',
                        'updated_at': product.updated_at.isoformat() if product.updated_at else '',
                        'scraped_at': product.scraped_at.isoformat() if product.scraped_at else ''
                    }
                    writer.writerow(row)
            
            logger.info(f"商品数据已导出到CSV: {output_path} ({len(products)} 条记录)")
            return output_path
            
        except Exception as e:
            logger.error(f"导出商品数据到CSV失败: {str(e)}")
            raise
    
    def export_products_to_excel(self, filename: Optional[str] = None,
                                shop_id: Optional[str] = None,
                                category: Optional[str] = None) -> Path:
        """
        导出商品数据到Excel文件
        
        Args:
            filename: 文件名，默认自动生成
            shop_id: 店铺ID过滤
            category: 分类过滤
            
        Returns:
            Path: 导出文件路径
        """
        if not PANDAS_AVAILABLE:
            raise ImportError("pandas和openpyxl未安装，无法导出Excel文件")
        
        try:
            # 获取商品数据
            products = self.memory_storage.list_products(
                shop_id=shop_id, 
                category=category, 
                limit=10000
            )
            
            # 转换为DataFrame
            products_data = []
            for product in products:
                row = {
                    '商品ID': product.product_id,
                    '商品标题': product.title,
                    '商品描述': product.description,
                    '分类': product.category,
                    '子分类': product.subcategory,
                    '品牌': product.brand,
                    '价格': float(product.price),
                    '原价': float(product.original_price) if product.original_price else None,
                    '货币': product.currency.value if hasattr(product.currency, 'value') else str(product.currency),
                    '折扣百分比': product.discount_percentage,
                    '库存': product.stock,
                    '销量': product.sold_count,
                    '月销量': product.monthly_sales,
                    '主图URL': product.main_image_url,
                    '店铺ID': product.shop_id,
                    '店铺名称': product.shop_name,
                    '店铺URL': product.shop_url,
                    '商品URL': getattr(product, 'product_url', ''),
                    '状态': product.status.value if hasattr(product.status, 'value') else str(product.status),
                    '创建时间': product.created_at.isoformat() if product.created_at else '',
                    '更新时间': product.updated_at.isoformat() if product.updated_at else '',
                    '抓取时间': product.scraped_at.isoformat() if product.scraped_at else ''
                }
                products_data.append(row)
            
            df = pd.DataFrame(products_data)
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filter_suffix = ""
                if shop_id:
                    filter_suffix += f"_shop_{shop_id}"
                if category:
                    filter_suffix += f"_category_{category}"
                filename = f"products_{timestamp}{filter_suffix}.xlsx"
            
            # 导出文件
            output_path = self.output_dir / filename
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='商品数据', index=False)
                
                # 获取工作表并设置列宽
                worksheet = writer.sheets['商品数据']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"商品数据已导出到Excel: {output_path} ({len(products)} 条记录)")
            return output_path
            
        except Exception as e:
            logger.error(f"导出商品数据到Excel失败: {str(e)}")
            raise
    
    def export_shops_to_json(self, filename: Optional[str] = None) -> Path:
        """
        导出店铺数据到JSON文件
        
        Args:
            filename: 文件名，默认自动生成
            
        Returns:
            Path: 导出文件路径
        """
        try:
            # 获取店铺数据
            shops = self.memory_storage.list_shops(limit=1000)
            
            # 转换为字典格式
            shops_data = []
            for shop in shops:
                shop_dict = {
                    'shop_id': shop.shop_id,
                    'shop_name': shop.shop_name,
                    'display_name': shop.display_name,
                    'description': shop.description,
                    'shop_type': shop.shop_type.value if hasattr(shop.shop_type, 'value') else str(shop.shop_type),
                    'status': shop.status.value if hasattr(shop.status, 'value') else str(shop.status),
                    'shop_url': shop.shop_url,
                    'avatar_url': shop.avatar_url,
                    'banner_url': shop.banner_url,
                    'is_verified': shop.is_verified,
                    'is_official': shop.is_official,
                    'verification_badges': shop.verification_badges if hasattr(shop, 'verification_badges') else [],
                    'created_at': shop.created_at.isoformat() if shop.created_at else None,
                    'joined_at': shop.joined_at.isoformat() if shop.joined_at else None,
                    'last_active': shop.last_active.isoformat() if shop.last_active else None,
                    'scraped_at': shop.scraped_at.isoformat() if shop.scraped_at else None
                }
                shops_data.append(shop_dict)
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"shops_{timestamp}.json"
            
            # 导出文件
            output_path = self.output_dir / filename
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'export_time': datetime.now().isoformat(),
                    'total_count': len(shops_data),
                    'shops': shops_data
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"店铺数据已导出到JSON: {output_path} ({len(shops_data)} 条记录)")
            return output_path
            
        except Exception as e:
            logger.error(f"导出店铺数据到JSON失败: {str(e)}")
            raise
    
    def get_export_stats(self) -> Dict[str, Any]:
        """
        获取可导出数据的统计信息
        
        Returns:
            Dict: 统计信息
        """
        return self.memory_storage.get_stats()
