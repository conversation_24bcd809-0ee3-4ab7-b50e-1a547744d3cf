"""
直连爬虫 - 使用HTTP请求直接获取数据
完全独立的实现，不依赖Selenium或浏览器
"""

import asyncio
import aiohttp
import json
import re
import time
from typing import Dict, Any, Optional, List, Callable
from urllib.parse import urlencode, urlparse
from loguru import logger

from .crawler_interface import (
    ICrawler, CrawlerResult, CrawlerStatus, CrawlerError, ErrorType,
    ProductData, ShopData, DirectCrawlerConfig,
    create_success_result, create_error_result
)
from .url_utils import URLUtils, URLValidator


class DirectCrawler(ICrawler):
    """直连HTTP爬虫 - 独立实现"""
    
    def __init__(self, config: Optional[DirectCrawlerConfig] = None):
        """
        初始化直连爬虫
        
        Args:
            config: 爬虫配置
        """
        self.config = config or DirectCrawlerConfig()
        self.status = CrawlerStatus.IDLE
        self.session: Optional[aiohttp.ClientSession] = None
        self.user_interaction_callback: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'requests_made': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'captcha_detections': 0,
            'rate_limits': 0,
            'start_time': None,
            'last_request_time': None
        }
        
        # TikTok API配置
        self.api_config = {
            'base_url': 'https://www.tiktok.com',
            'shop_api_path': '/api/shop/store',
            'product_api_path': '/api/shop/product',
            'default_headers': {
                'User-Agent': self.config.user_agent,
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        }
        
        logger.info("🌐 [直连爬虫] 初始化完成")
    
    async def initialize(self) -> bool:
        """初始化爬虫"""
        try:
            if self.status != CrawlerStatus.IDLE:
                logger.warning("⚠️ [直连爬虫] 爬虫已初始化")
                return True
            
            self.status = CrawlerStatus.INITIALIZING
            logger.info("🚀 [直连爬虫] 开始初始化...")
            
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(
                limit=self.config.connection_pool_size,
                limit_per_host=self.config.max_concurrent_requests,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self.api_config['default_headers']
            )
            
            # 更新统计信息
            self.stats['start_time'] = time.time()
            self.status = CrawlerStatus.RUNNING
            
            logger.info("✅ [直连爬虫] 初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ [直连爬虫] 初始化失败: {str(e)}")
            self.status = CrawlerStatus.ERROR
            return False
    
    async def close(self) -> None:
        """关闭爬虫，清理资源"""
        try:
            logger.info("🔒 [直连爬虫] 开始关闭...")
            
            if self.session:
                await self.session.close()
                self.session = None
            
            self.status = CrawlerStatus.CLOSED
            logger.info("✅ [直连爬虫] 关闭完成")
            
        except Exception as e:
            logger.error(f"❌ [直连爬虫] 关闭失败: {str(e)}")
    
    async def get_product_data(self, url: str) -> CrawlerResult:
        """获取产品数据"""
        try:
            if not URLValidator.is_valid_product_url(url):
                return create_error_result(
                    ErrorType.INVALID_URL,
                    f"无效的产品URL: {url}",
                    url
                )
            
            logger.info(f"📦 [直连爬虫] 获取产品数据: {url}")
            
            # 提取产品ID
            product_id = URLUtils.extract_product_id(url)
            if not product_id:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法从URL中提取产品ID",
                    url
                )
            
            # 获取产品页面内容
            content_result = await self.get_page_content(url)
            if not content_result.success:
                return content_result
            
            # 解析产品数据
            product_data = await self._parse_product_data(content_result.data, product_id, url)
            if not product_data:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法解析产品数据",
                    url
                )
            
            return create_success_result(
                product_data,
                {'source': 'direct_http', 'url': url}
            )
            
        except Exception as e:
            logger.error(f"❌ [直连爬虫] 获取产品数据失败: {url} - {str(e)}")
            return create_error_result(
                ErrorType.UNKNOWN_ERROR,
                f"获取产品数据失败: {str(e)}",
                url
            )
    
    async def get_shop_data(self, url: str, load_products: bool = False) -> CrawlerResult:
        """获取店铺数据"""
        try:
            if not URLValidator.is_valid_shop_url(url):
                return create_error_result(
                    ErrorType.INVALID_URL,
                    f"无效的店铺URL: {url}",
                    url
                )
            
            logger.info(f"🏪 [直连爬虫] 获取店铺数据: {url}")
            
            # 提取店铺ID
            shop_id = URLUtils.extract_shop_id(url)
            if not shop_id:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法从URL中提取店铺ID",
                    url
                )
            
            # 获取店铺页面内容
            content_result = await self.get_page_content(url)
            if not content_result.success:
                return content_result
            
            # 解析店铺数据
            shop_data = await self._parse_shop_data(content_result.data, shop_id, url, load_products)
            if not shop_data:
                return create_error_result(
                    ErrorType.PARSING_ERROR,
                    "无法解析店铺数据",
                    url
                )
            
            return create_success_result(
                shop_data,
                {'source': 'direct_http', 'url': url}
            )
            
        except Exception as e:
            logger.error(f"❌ [直连爬虫] 获取店铺数据失败: {url} - {str(e)}")
            return create_error_result(
                ErrorType.UNKNOWN_ERROR,
                f"获取店铺数据失败: {str(e)}",
                url
            )
    
    async def get_page_content(self, url: str, **kwargs) -> CrawlerResult:
        """获取页面内容"""
        try:
            if not await self.initialize():
                return create_error_result(
                    ErrorType.NETWORK_ERROR,
                    "爬虫初始化失败",
                    url
                )
            
            # 更新统计信息
            self.stats['requests_made'] += 1
            self.stats['last_request_time'] = time.time()
            
            # 请求延迟
            if self.config.request_delay > 0:
                await asyncio.sleep(self.config.request_delay)
            
            # 发送HTTP请求
            headers = self.api_config['default_headers'].copy()
            headers.update(self.config.custom_headers)
            
            async with self.session.get(url, headers=headers) as response:
                logger.debug(f"📡 [直连爬虫] HTTP响应状态: {response.status}")
                
                if response.status == 200:
                    content = await response.text()
                    
                    # 检测验证码
                    if self._detect_captcha_in_content(content):
                        self.stats['captcha_detections'] += 1
                        return create_error_result(
                            ErrorType.CAPTCHA_DETECTED,
                            "检测到验证码页面",
                            url
                        )
                    
                    # 检测限流
                    if self._detect_rate_limit_in_content(content):
                        self.stats['rate_limits'] += 1
                        return create_error_result(
                            ErrorType.RATE_LIMITED,
                            "检测到访问限制",
                            url
                        )
                    
                    self.stats['successful_requests'] += 1
                    return create_success_result(
                        content,
                        {
                            'status_code': response.status,
                            'content_length': len(content),
                            'content_type': response.headers.get('content-type', '')
                        }
                    )
                
                elif response.status == 429:
                    self.stats['rate_limits'] += 1
                    return create_error_result(
                        ErrorType.RATE_LIMITED,
                        f"请求过于频繁: {response.status}",
                        url
                    )
                
                else:
                    self.stats['failed_requests'] += 1
                    return create_error_result(
                        ErrorType.NETWORK_ERROR,
                        f"HTTP请求失败: {response.status}",
                        url
                    )
                    
        except asyncio.TimeoutError:
            self.stats['failed_requests'] += 1
            return create_error_result(
                ErrorType.TIMEOUT_ERROR,
                "请求超时",
                url
            )
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"❌ [直连爬虫] 获取页面内容失败: {url} - {str(e)}")
            return create_error_result(
                ErrorType.NETWORK_ERROR,
                f"网络请求失败: {str(e)}",
                url
            )
    
    def get_status(self) -> CrawlerStatus:
        """获取爬虫当前状态"""
        return self.status
    
    def get_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        stats = self.stats.copy()
        if stats['start_time']:
            stats['uptime'] = time.time() - stats['start_time']
        return stats
    
    def set_user_interaction_callback(self, callback: Optional[Callable]) -> None:
        """设置用户交互回调函数"""
        self.user_interaction_callback = callback

    # ==================== 私有方法 ====================

    async def _parse_product_data(self, content: str, product_id: str, url: str) -> Optional[ProductData]:
        """解析产品数据"""
        try:
            logger.debug(f"📦 [直连解析] 开始解析产品数据: {product_id}")

            # 尝试从页面中提取JSON数据
            json_data = self._extract_json_from_html(content)
            if json_data:
                product_info = self._extract_product_from_json(json_data, product_id)
                if product_info:
                    return self._build_product_data(product_info, url)

            # 如果JSON解析失败，尝试HTML解析
            product_info = self._extract_product_from_html(content, product_id)
            if product_info:
                return self._build_product_data(product_info, url)

            logger.warning(f"⚠️ [直连解析] 无法解析产品数据: {product_id}")
            return None

        except Exception as e:
            logger.error(f"❌ [直连解析] 产品数据解析失败: {product_id} - {str(e)}")
            return None

    async def _parse_shop_data(self, content: str, shop_id: str, url: str, load_products: bool) -> Optional[ShopData]:
        """解析店铺数据"""
        try:
            logger.debug(f"🏪 [直连解析] 开始解析店铺数据: {shop_id}")

            # 尝试从页面中提取JSON数据
            json_data = self._extract_json_from_html(content)
            if json_data:
                shop_info = self._extract_shop_from_json(json_data, shop_id)
                if shop_info:
                    shop_data = self._build_shop_data(shop_info, url)

                    # 如果需要加载商品列表
                    if load_products and shop_data:
                        products = self._extract_products_from_json(json_data)
                        shop_data.products = [self._build_product_data(p, url) for p in products if p]

                    return shop_data

            # 如果JSON解析失败，尝试HTML解析
            shop_info = self._extract_shop_from_html(content, shop_id)
            if shop_info:
                shop_data = self._build_shop_data(shop_info, url)

                if load_products and shop_data:
                    products = self._extract_products_from_html(content)
                    shop_data.products = [self._build_product_data(p, url) for p in products if p]

                return shop_data

            logger.warning(f"⚠️ [直连解析] 无法解析店铺数据: {shop_id}")
            return None

        except Exception as e:
            logger.error(f"❌ [直连解析] 店铺数据解析失败: {shop_id} - {str(e)}")
            return None

    def _extract_json_from_html(self, content: str) -> Optional[Dict[str, Any]]:
        """从HTML中提取JSON数据"""
        try:
            # 查找页面中的JSON数据
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\.__NUXT__\s*=\s*({.+?});',
                r'<script[^>]*>.*?window\.__APP_DATA__\s*=\s*({.+?});.*?</script>',
                r'<script[^>]*type="application/json"[^>]*>({.+?})</script>'
            ]

            for pattern in json_patterns:
                matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    try:
                        return json.loads(match)
                    except json.JSONDecodeError:
                        continue

            return None

        except Exception as e:
            logger.debug(f"JSON提取失败: {str(e)}")
            return None

    def _extract_product_from_json(self, json_data: Dict[str, Any], product_id: str) -> Optional[Dict[str, Any]]:
        """从JSON数据中提取产品信息"""
        try:
            # 递归搜索产品数据
            def search_product(data, path=""):
                if isinstance(data, dict):
                    # 检查是否是产品对象
                    if self._is_product_object(data, product_id):
                        return data

                    # 递归搜索子对象
                    for key, value in data.items():
                        result = search_product(value, f"{path}.{key}")
                        if result:
                            return result

                elif isinstance(data, list):
                    for i, item in enumerate(data):
                        result = search_product(item, f"{path}[{i}]")
                        if result:
                            return result

                return None

            return search_product(json_data)

        except Exception as e:
            logger.debug(f"JSON产品提取失败: {str(e)}")
            return None

    def _extract_shop_from_json(self, json_data: Dict[str, Any], shop_id: str) -> Optional[Dict[str, Any]]:
        """从JSON数据中提取店铺信息"""
        try:
            # 递归搜索店铺数据
            def search_shop(data, path=""):
                if isinstance(data, dict):
                    # 检查是否是店铺对象
                    if self._is_shop_object(data, shop_id):
                        return data

                    # 递归搜索子对象
                    for key, value in data.items():
                        result = search_shop(value, f"{path}.{key}")
                        if result:
                            return result

                elif isinstance(data, list):
                    for i, item in enumerate(data):
                        result = search_shop(item, f"{path}[{i}]")
                        if result:
                            return result

                return None

            return search_shop(json_data)

        except Exception as e:
            logger.debug(f"JSON店铺提取失败: {str(e)}")
            return None

    def _extract_products_from_json(self, json_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从JSON数据中提取商品列表"""
        try:
            products = []

            def search_products(data, path=""):
                if isinstance(data, dict):
                    # 检查是否是产品对象
                    if self._is_product_object(data):
                        products.append(data)

                    # 递归搜索子对象
                    for key, value in data.items():
                        search_products(value, f"{path}.{key}")

                elif isinstance(data, list):
                    for i, item in enumerate(data):
                        search_products(item, f"{path}[{i}]")

            search_products(json_data)
            return products

        except Exception as e:
            logger.debug(f"JSON商品列表提取失败: {str(e)}")
            return []

    def _extract_product_from_html(self, content: str, product_id: str) -> Optional[Dict[str, Any]]:
        """从HTML中提取产品信息"""
        try:
            # HTML解析模式
            product_info = {}

            # 提取标题
            title_patterns = [
                r'<h1[^>]*class="[^"]*product-title[^"]*"[^>]*>([^<]+)</h1>',
                r'<title>([^<]+)</title>',
                r'<meta[^>]*property="og:title"[^>]*content="([^"]+)"'
            ]

            for pattern in title_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    product_info['title'] = match.group(1).strip()
                    break

            # 提取价格
            price_patterns = [
                r'<span[^>]*class="[^"]*price[^"]*"[^>]*>[\$¥€£]?([\d,\.]+)</span>',
                r'"price":\s*"?([\d,\.]+)"?',
                r'<meta[^>]*property="product:price:amount"[^>]*content="([\d,\.]+)"'
            ]

            for pattern in price_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    try:
                        price_str = match.group(1).replace(',', '')
                        product_info['price'] = float(price_str)
                        break
                    except ValueError:
                        continue

            # 提取销量
            sold_patterns = [
                r'(\d+)\s*sold',
                r'sold[:\s]*(\d+)',
                r'"sold_count":\s*(\d+)'
            ]

            for pattern in sold_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    try:
                        product_info['sold_count'] = int(match.group(1))
                        break
                    except ValueError:
                        continue

            # 提取评分
            rating_patterns = [
                r'"rating":\s*([\d\.]+)',
                r'<span[^>]*class="[^"]*rating[^"]*"[^>]*>([\d\.]+)</span>'
            ]

            for pattern in rating_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    try:
                        product_info['rating'] = float(match.group(1))
                        break
                    except ValueError:
                        continue

            # 如果提取到了基本信息，返回结果
            if product_info.get('title') or product_info.get('price'):
                product_info['product_id'] = product_id
                return product_info

            return None

        except Exception as e:
            logger.debug(f"HTML产品提取失败: {str(e)}")
            return None

    def _extract_shop_from_html(self, content: str, shop_id: str) -> Optional[Dict[str, Any]]:
        """从HTML中提取店铺信息"""
        try:
            shop_info = {}

            # 提取店铺名称
            name_patterns = [
                r'<h1[^>]*class="[^"]*shop-name[^"]*"[^>]*>([^<]+)</h1>',
                r'<title>([^<]+)\s*-\s*TikTok Shop</title>',
                r'"shop_name":\s*"([^"]+)"'
            ]

            for pattern in name_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    shop_info['shop_name'] = match.group(1).strip()
                    break

            # 提取商品数量
            count_patterns = [
                r'(\d+)\s*products?',
                r'"product_count":\s*(\d+)',
                r'<span[^>]*class="[^"]*product-count[^"]*"[^>]*>(\d+)</span>'
            ]

            for pattern in count_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    try:
                        shop_info['product_count'] = int(match.group(1))
                        break
                    except ValueError:
                        continue

            # 如果提取到了基本信息，返回结果
            if shop_info.get('shop_name'):
                shop_info['shop_id'] = shop_id
                return shop_info

            return None

        except Exception as e:
            logger.debug(f"HTML店铺提取失败: {str(e)}")
            return None

    def _extract_products_from_html(self, content: str) -> List[Dict[str, Any]]:
        """从HTML中提取商品列表"""
        try:
            products = []

            # 查找商品容器
            product_patterns = [
                r'<div[^>]*class="[^"]*product-item[^"]*"[^>]*>.*?</div>',
                r'<div[^>]*class="[^"]*w-full cursor-pointer[^"]*"[^>]*>.*?</div>'
            ]

            for pattern in product_patterns:
                matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    product_info = self._parse_product_container(match)
                    if product_info:
                        products.append(product_info)

            return products

        except Exception as e:
            logger.debug(f"HTML商品列表提取失败: {str(e)}")
            return []

    def _parse_product_container(self, container_html: str) -> Optional[Dict[str, Any]]:
        """解析单个商品容器"""
        try:
            product_info = {}

            # 提取产品ID
            id_patterns = [
                r'data-product-id="(\d+)"',
                r'/product/(\d+)',
                r'"product_id":\s*"?(\d+)"?'
            ]

            for pattern in id_patterns:
                match = re.search(pattern, container_html, re.IGNORECASE)
                if match:
                    product_info['product_id'] = match.group(1)
                    break

            # 提取标题
            title_patterns = [
                r'<h3[^>]*>([^<]+)</h3>',
                r'alt="([^"]+)"',
                r'title="([^"]+)"'
            ]

            for pattern in title_patterns:
                match = re.search(pattern, container_html, re.IGNORECASE)
                if match:
                    product_info['title'] = match.group(1).strip()
                    break

            # 提取价格
            price_match = re.search(r'[\$¥€£]?([\d,\.]+)', container_html)
            if price_match:
                try:
                    price_str = price_match.group(1).replace(',', '')
                    product_info['price'] = float(price_str)
                except ValueError:
                    pass

            # 提取销量
            sold_match = re.search(r'(\d+)\s*sold', container_html, re.IGNORECASE)
            if sold_match:
                try:
                    product_info['sold_count'] = int(sold_match.group(1))
                except ValueError:
                    pass

            return product_info if product_info.get('product_id') else None

        except Exception as e:
            logger.debug(f"商品容器解析失败: {str(e)}")
            return None

    def _build_product_data(self, product_info: Dict[str, Any], url: str) -> ProductData:
        """构建ProductData对象"""
        try:
            return ProductData(
                product_id=str(product_info.get('product_id', '')),
                title=product_info.get('title', ''),
                price=float(product_info.get('price', 0)),
                currency=product_info.get('currency', 'USD'),
                sold_count=int(product_info.get('sold_count', 0)),
                rating=float(product_info.get('rating', 0)),
                review_count=int(product_info.get('review_count', 0)),
                images=product_info.get('images', []),
                description=product_info.get('description', ''),
                seller_id=product_info.get('seller_id'),
                seller_name=product_info.get('seller_name'),
                shop_id=product_info.get('shop_id'),
                shop_name=product_info.get('shop_name'),
                category=product_info.get('category'),
                shipping_fee=float(product_info.get('shipping_fee', 0)),
                availability=product_info.get('availability', True),
                url=url
            )
        except Exception as e:
            logger.debug(f"构建ProductData失败: {str(e)}")
            return ProductData(
                product_id=str(product_info.get('product_id', '')),
                title=product_info.get('title', ''),
                price=0,
                url=url
            )

    def _build_shop_data(self, shop_info: Dict[str, Any], url: str) -> ShopData:
        """构建ShopData对象"""
        try:
            return ShopData(
                shop_id=str(shop_info.get('shop_id', '')),
                shop_name=shop_info.get('shop_name', ''),
                seller_id=shop_info.get('seller_id'),
                seller_name=shop_info.get('seller_name'),
                product_count=int(shop_info.get('product_count', 0)),
                rating=float(shop_info.get('rating', 0)),
                follower_count=int(shop_info.get('follower_count', 0)),
                description=shop_info.get('description', ''),
                avatar_url=shop_info.get('avatar_url'),
                cover_url=shop_info.get('cover_url'),
                url=url
            )
        except Exception as e:
            logger.debug(f"构建ShopData失败: {str(e)}")
            return ShopData(
                shop_id=str(shop_info.get('shop_id', '')),
                shop_name=shop_info.get('shop_name', ''),
                url=url
            )

    def _is_product_object(self, data: Dict[str, Any], product_id: str = None) -> bool:
        """判断是否为产品对象"""
        try:
            # 检查必要的产品字段
            product_indicators = ['product_id', 'title', 'name', 'price']
            has_indicators = sum(1 for indicator in product_indicators if indicator in data)

            # 如果指定了product_id，检查是否匹配
            if product_id and 'product_id' in data:
                return str(data['product_id']) == str(product_id)

            # 至少包含2个产品指示器
            return has_indicators >= 2

        except Exception:
            return False

    def _is_shop_object(self, data: Dict[str, Any], shop_id: str = None) -> bool:
        """判断是否为店铺对象"""
        try:
            # 检查必要的店铺字段
            shop_indicators = ['shop_id', 'shop_name', 'store_name', 'seller_id']
            has_indicators = sum(1 for indicator in shop_indicators if indicator in data)

            # 如果指定了shop_id，检查是否匹配
            if shop_id and 'shop_id' in data:
                return str(data['shop_id']) == str(shop_id)

            # 至少包含1个店铺指示器
            return has_indicators >= 1

        except Exception:
            return False

    def _detect_captcha_in_content(self, content: str) -> bool:
        """检测内容中是否包含验证码"""
        try:
            captcha_indicators = [
                'captcha',
                'verification',
                'robot check',
                'please verify',
                'security check',
                'anti-bot',
                'challenge'
            ]

            content_lower = content.lower()
            return any(indicator in content_lower for indicator in captcha_indicators)

        except Exception:
            return False

    def _detect_rate_limit_in_content(self, content: str) -> bool:
        """检测内容中是否包含限流信息"""
        try:
            rate_limit_indicators = [
                'rate limit',
                'too many requests',
                'request limit',
                'access denied',
                'temporarily blocked',
                'slow down'
            ]

            content_lower = content.lower()
            return any(indicator in content_lower for indicator in rate_limit_indicators)

        except Exception:
            return False
