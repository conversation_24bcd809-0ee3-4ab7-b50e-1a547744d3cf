# TikTok Shop 商品数据爬取工具

一个专业的TikTok Shop商品数据采集和分析工具，支持高效并发爬取店铺商品信息，提供完整的商品数据提取和分析功能。

## 🚀 核心特性

### 智能爬取引擎
- 🔗 **多格式URL支持**: 支持PDP链接、桌面链接、移动分享链接等多种TikTok Shop URL格式
- 🏪 **店铺批量采集**: 从单个商品链接自动发现并批量获取整个店铺的商品数据
- 🚀 **高并发处理**: 支持20个并发请求，显著提升数据采集效率
- 🔄 **混合爬取模式**: 智能切换直连模式和浏览器模式，应对不同的反爬虫策略
- 🛡️ **零销量检测**: 智能检测零销量商品，自动停止无效页面加载

### 数据提取能力
- 📦 **完整商品信息**: 提取商品ID、标题、价格、销量、评分、图片等全面数据
- 🚚 **运费信息**: 精确提取运费价格、币种等物流成本数据
- 🏬 **店铺数据**: 获取店铺名称、商品数量等店铺运营指标
- 💰 **价格分析**: 自动计算折扣金额、折扣百分比等价格指标
- ⭐ **评价数据**: 提取商品评分、评价数量等用户反馈数据

### 技术架构
- **异步并发**: 基于asyncio的高性能异步处理架构
- **模块化设计**: 清晰的模块分离，易于维护和扩展
- **智能解析**: 专门针对TikTok Shop的JSON数据结构优化
- **错误恢复**: 完善的异常处理和自动重试机制
- **调试支持**: 详细的日志记录和HTML调试文件保存

## 🔧 技术栈

- **核心框架**: Python 3.8+ 异步编程
- **网络请求**: aiohttp (异步HTTP客户端)
- **数据解析**: BeautifulSoup4, lxml, 正则表达式
- **浏览器自动化**: Selenium WebDriver (可选)
- **数据处理**: pandas, openpyxl
- **日志系统**: loguru
- **GUI框架**: PyQt6 (桌面应用)

## 📁 项目架构

```
TikTokShopTool/
├── src/                           # 源代码目录
│   ├── crawler/                   # 🕷️ 爬虫核心模块
│   │   ├── tiktok_crawler.py     # TikTok Shop专用爬虫
│   │   ├── base_crawler.py       # 基础爬虫类
│   │   ├── selenium_crawler.py   # 浏览器模式爬虫
│   │   ├── crawler_modes.py      # 混合模式管理
│   │   ├── crawler_manager.py    # 爬虫任务管理器
│   │   └── url_parser.py         # URL解析器
│   ├── models/                    # 📊 数据模型
│   │   ├── product.py            # 商品数据模型
│   │   ├── shop.py               # 店铺数据模型
│   │   └── scraping_task.py      # 爬取任务模型
│   ├── utils/                     # 🛠️ 工具模块
│   │   ├── concurrent_manager.py # 并发请求管理
│   │   ├── sales_detector.py     # 销量检测器
│   │   └── data_storage.py       # 数据存储管理
│   ├── config/                    # ⚙️ 配置管理
│   │   └── settings.py           # 系统配置
│   └── gui/                       # 🖥️ 图形界面
├── debug_shop_html/               # 🐛 调试文件目录
├── debug_pdp_html/                # 🐛 PDP页面调试文件
├── logs/                          # 📝 日志文件
└── docs/                          # 📚 文档目录
```

## 🔄 爬虫工作流程

### 完整数据采集链路

```mermaid
graph TD
    A[输入TikTok Shop URL] --> B[URL解析与验证]
    B --> C{URL类型识别}
    C -->|PDP链接| D[提取商品ID和店铺信息]
    C -->|桌面链接| E[转换为标准格式]
    C -->|分享链接| F[解析重定向链接]

    D --> G[构建店铺API URL]
    E --> G
    F --> G

    G --> H[获取店铺页面HTML]
    H --> I[HTML DOM解析]
    I --> J[提取所有商品PDP链接]
    J --> K[销量过滤]
    K --> L[并发获取商品详情]

    L --> M[解析__MODERN_ROUTER_DATA__]
    M --> N[提取完整商品数据]
    N --> O[数据结构化存储]
    O --> P[输出结果]
```

### 核心处理步骤

#### 1️⃣ **URL解析阶段**
- **多格式支持**: 自动识别PDP、桌面、移动分享等URL格式
- **标准化处理**: 将不同格式的URL转换为统一的处理格式
- **参数提取**: 提取商品ID、店铺名称等关键信息

#### 2️⃣ **店铺发现阶段**
- **seller_id提取**: 从商品页面HTML中提取seller_id
- **店铺API构建**: 构建店铺商品列表API URL
- **零销量检测**: 智能检测并停止加载零销量商品页面

#### 3️⃣ **商品列表获取**
- **HTML DOM解析**: 解析店铺页面的商品容器
- **PDP链接提取**: 提取所有商品的详情页面链接
- **销量过滤**: 根据设定的销量阈值过滤商品

#### 4️⃣ **并发数据采集**
- **20个并发请求**: 同时处理多个商品的详情获取
- **查询参数增强**: 自动添加必要的查询参数
- **智能重试**: 失败请求的自动重试机制

#### 5️⃣ **数据解析与提取**
- **JSON脚本识别**: 专门解析`__MODERN_ROUTER_DATA__`脚本
- **字段映射**: 将JSON数据映射到标准Product模型
- **数据计算**: 自动计算折扣百分比、折扣金额等衍生数据

## 📊 数据模型

### Product (商品数据模型)
```python
@dataclass
class Product:
    # 基本信息
    product_id: str              # 商品ID
    title: str                   # 商品标题
    description: str             # 商品描述
    category: str                # 商品类别

    # 价格信息
    price: Decimal               # 售价
    original_price: Decimal      # 原价
    currency: Currency           # 币种
    discount_percentage: float   # 折扣百分比

    # 销量和评价
    sold_count: int             # 销量
    rating: ProductRating       # 评分信息

    # 店铺信息
    shop_name: str              # 店铺名称
    shop_id: str                # 店铺ID

    # 图片信息
    images: List[ProductImage]  # 商品图片列表
    main_image_url: str         # 主图URL

    # 元数据
    metadata: Dict[str, Any]    # 额外信息（运费、店铺商品数等）
```

### 提取的关键数据字段

| 字段类别 | 具体字段 | 数据来源 | 说明 |
|---------|---------|---------|------|
| **基础信息** | `route_product_id` | `__MODERN_ROUTER_DATA__` | 商品唯一标识 |
| | `request_url` | JSON数据 | 商品详情页链接 |
| | `shop_name` | JSON数据 | 店铺名称 |
| **价格信息** | `real_price` | `price.real_price` | 当前售价 |
| | `original_price` | `price.original_price` | 原价 |
| | `currency` | `price.currency` | 币种 |
| **销量评价** | `sold_count` | JSON数据 | 商品销量 |
| | `product_rating` | `product_detail_review` | 商品评分 |
| | `review_count` | `product_detail_review` | 评价数量 |
| **运费信息** | `shipping_fee` | JSON数据 | 运费价格、数值、币种 |
| **店铺数据** | `on_sell_product_count` | JSON数据 | 店铺在售商品数量 |
| **图片信息** | `images.url_list` | JSON数据 | 高清商品图片链接 |

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Windows 10/11 (推荐)
- 8GB+ RAM (用于并发处理)

### 安装依赖
```bash
# 克隆项目
git clone https://github.com/your-repo/TikTokShopTool.git
cd TikTokShopTool

# 安装依赖
pip install -r requirements.txt
```

### 基本使用
```bash
# 启动GUI应用
python main.py

# 或直接使用爬虫API
python -c "
from src.crawler.tiktok_crawler import TikTokShopCrawler
import asyncio

async def main():
    crawler = TikTokShopCrawler()
    await crawler.start_session()

    # 爬取单个商品的店铺数据
    url = 'https://www.tiktok.com/shop/pdp/product-name/1234567890'
    products = await crawler.get_pdp_product_detail(url)

    print(f'获取到 {len(products)} 个商品')
    await crawler.close_session()

asyncio.run(main())
"
```

## ⚡ 性能特性

### 高效并发处理
- **20个并发请求**: 同时处理多个商品数据获取
- **智能队列管理**: 自动控制请求频率，避免服务器过载
- **异步架构**: 基于asyncio的高性能异步处理

### 智能优化策略
- **零销量检测**: 自动识别并停止加载零销量商品页面
- **增量加载**: 支持分页加载大量商品数据
- **缓存机制**: 避免重复请求相同的数据

### 性能基准测试
| 场景 | 商品数量 | 处理时间 | 成功率 |
|------|---------|---------|--------|
| 小型店铺 | 10-50个商品 | 30-60秒 | 95%+ |
| 中型店铺 | 50-200个商品 | 2-5分钟 | 90%+ |
| 大型店铺 | 200+个商品 | 5-15分钟 | 85%+ |

## 🛠️ 高级配置

### 爬虫模式配置
```python
from src.crawler.crawler_modes import CrawlerMode

# 直连模式（默认，速度快）
crawler = TikTokShopCrawler(crawler_mode=CrawlerMode.DIRECT)

# 浏览器模式（兼容性好，速度慢）
crawler = TikTokShopCrawler(crawler_mode=CrawlerMode.BROWSER)

# 自动切换模式（智能选择）
crawler = TikTokShopCrawler(crawler_mode=CrawlerMode.AUTO)
```

### 并发参数调整
```python
# 调整并发数量（默认20个）
crawler.max_concurrent = 10  # 降低并发数

# 设置销量过滤阈值
filter_conditions = {'sales_min': 10}  # 只获取销量>=10的商品
```

### 调试模式
```python
# 启用调试模式，保存HTML文件
crawler.debug_mode = True

# 调试文件将保存到：
# - debug_shop_html/     店铺页面HTML
# - debug_pdp_html/      商品详情页HTML
```

## 📈 开发路线图

### ✅ 已完成功能
- [x] 多格式URL解析支持
- [x] 高并发商品数据采集
- [x] 完整的商品信息提取
- [x] 智能零销量检测
- [x] 混合爬取模式
- [x] 详细的调试日志

### 🚧 开发中功能
- [ ] GUI桌面应用界面
- [ ] Excel数据导出功能
- [ ] 数据可视化图表
- [ ] 定时任务调度

### 🔮 计划功能
- [ ] 多店铺批量采集
- [ ] 价格监控和预警
- [ ] 竞品分析功能
- [ ] API接口服务
- [ ] 云端部署支持

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发环境设置
```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
python -m pytest tests/

# 代码格式化
black src/
```

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款和robots.txt规定。

## 📞 联系方式

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/TikTokShopTool/issues)
- 💬 讨论: [GitHub Discussions](https://github.com/your-repo/TikTokShopTool/discussions)

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！
